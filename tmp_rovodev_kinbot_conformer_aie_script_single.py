#!/usr/bin/env python3
"""
KinBot构象搜索和电离能计算脚本

该脚本利用KinBot框架对给定SMILES的化合物进行：
1. 构象搜索 (conformer search)
2. 电离能计算 (AIE - Adiabatic Ionization Energy)

作者: Rovo Dev
日期: 2024
"""

import sys
import os
import json
import argparse
import logging
import time
from pathlib import Path

# 导入KinBot模块
try:
    from kinbot.parameters import Parameters
    from kinbot.stationary_pt import StationaryPoint
    from kinbot.qc import QuantumChemistry
    from kinbot.optimize import Optimize
    from kinbot.utils import make_dirs, clean_files
    from kinbot.config_log import config_log
    from kinbot import license_message
    from kinbot.molpro import Molpro
    from kinbot.orca import Orca
except ImportError as e:
    print(f"错误：无法导入KinBot模块: {e}")
    print("请确保KinBot已正确安装")
    sys.exit(1)


def create_input_file(smiles=None, charge=0, mult=1, **kwargs):
    """
    创建KinBot输入文件
    
    参数:
    smiles: 化合物的SMILES字符串
    charge: 分子电荷
    mult: 分子多重度
    **kwargs: 其他参数
    
    返回:
    (kinbot_params, custom_params): kinbot原生参数和自定义参数的元组
    """
    
    # KinBot原生参数设置
    kinbot_params = {
        # 基本信息
        "title": kwargs.get("title", f"Conformer_AIE_calculation_{smiles or 'molecule'}"),
        "charge": charge,
        "mult": mult,
        
        # 计算设置
        "conformer_search": 1,  # 开启构象搜索
        "calc_aie": 0,          # 关闭kinbot原生AIE计算，我们手动控制
        "reaction_search": 0,   # 关闭反应搜索
        "high_level": 1,        # 开启高精度计算
        "rotor_scan": 0,        # 关闭转子扫描（AIE计算不需要）
        "barrier_threshold": kwargs.get("barrier_threshold", 100.0),  # 反应势垒阈值
        
        # L3计算参数
        "L3_calc": kwargs.get("L3_calc", 0),  # 开启L3计算
        "single_point_qc": kwargs.get("single_point_qc", "molpro"),  # L3计算软件
        "single_point_template": kwargs.get("single_point_template", ""),  # L3模板文件
        "single_point_key": kwargs.get("single_point_key", "MYENERGY"),  # L3能量关键词
        "single_point_command": kwargs.get("single_point_command", "molpro"),  # L3计算命令
        "single_point_ppn": kwargs.get("single_point_ppn", 4),  # L3计算处理器数
        
        # 构象搜索参数
        "difference_threshold": kwargs.get("difference_threshold", 0.1),  # kcal/mol
        "conf_grid": kwargs.get("conf_grid", 3),
        "semi_emp_conformer_search": kwargs.get("semi_emp_conformer_search", 1),
        "max_dihed": kwargs.get("max_dihed", 5),
        "random_conf": kwargs.get("random_conf", 500),
        "semi_emp_confomer_threshold": kwargs.get("semi_emp_confomer_threshold", 5),
        
        # 量子化学计算参数
        "qc": kwargs.get("qc", "gauss"),
        "method": kwargs.get("method", "b3lyp"),           # L1级别方法
        "basis": kwargs.get("basis", "6-31+g*"),           # L1级别基组
        "high_level_method": kwargs.get("high_level_method", "M062X"),    # L2级别方法
        "high_level_basis": kwargs.get("high_level_basis", "6-311++G(d,p)"),  # L2级别基组
        "semi_emp_method": kwargs.get("semi_emp_method", "am1"),
        
        # 队列系统参数
        "queuing": kwargs.get("queuing", "local"),
        "queue_name": kwargs.get("queue_name", "local"),
        "queue_template": kwargs.get("queue_template", ""),
        "ppn": kwargs.get("ppn", 4),
        "slurm_feature": kwargs.get("slurm_feature", ""),
        
        # 计算环境参数
        "qc_command": kwargs.get("qc_command", "g16"),
        "scratch": kwargs.get("scratch", ""),
        "username": kwargs.get("username", ""),
        "queue_job_limit": kwargs.get("queue_job_limit", 50),
        
        # 优化参数
        "opt": kwargs.get("opt", "maxcyc=64"),
        "integral": kwargs.get("integral", ""),
        "guessmix": kwargs.get("guessmix", 0),
        
        # 优化器设置
        "use_sella": kwargs.get("use_sella", False),
        
        # 环状分子参数
        "flat_ring_dih_angle": kwargs.get("flat_ring_dih_angle", 5.),
        
        # 其他设置
        "verbose": kwargs.get("verbose", 1),
        "delete_intermediate_files": kwargs.get("delete_intermediate_files", 0),
        "do_clean": kwargs.get("do_clean", True),
    }
    
    # 添加SMILES或几何结构
    if smiles:
        kinbot_params["smiles"] = smiles
    elif kwargs.get("structure"):
        kinbot_params["structure"] = kwargs.get("structure")
    
    # 自定义参数（不是kinbot原生参数）
    custom_params = {
        "aie_only_lowest": kwargs.get("aie_only_lowest", True),  # 只计算最低能量构象的AIE
        "calc_aie_l3": kwargs.get("calc_aie_l3", False),  # 开启L3高精度电离能计算
    }
    
    return kinbot_params, custom_params


def calculate_aie_l3_auto(well0, qc, par):
    """
    自动执行L3级别AIE计算
    基于已有的L2级别结果进行L3单点能计算
    
    参数:
    well0: StationaryPoint对象（中性分子）
    qc: QuantumChemistry对象
    par: 参数字典
    
    返回:
    success: 是否成功
    """
    logger = logging.getLogger('KinBot_Conformer_AIE')
    logger.info("开始L3级别AIE计算...")
    
    try:
        # 检查L3计算参数
        single_point_qc = par.get('single_point_qc', '').lower()
        if not single_point_qc:
            logger.error("未指定L3计算软件 (single_point_qc)")
            return False
        
        logger.info(f"使用L3计算软件: {single_point_qc}")
        logger.info(f"L3模板文件: {par.get('single_point_template', 'default')}")
        logger.info(f"L3能量关键词: {par.get('single_point_key', 'MYENERGY')}")
        
        # 获取L2级别的几何结构和能量
        high_level_job = f"{well0.chemid}_well_high"
        logger.info(f"读取L2级别结果: {high_level_job}")
        
        err_geom, neutral_geom = qc.get_qc_geom(high_level_job, well0.natom)
        err_energy, neutral_energy_l2 = qc.get_qc_energy(high_level_job)
        
        if err_geom != 0:
            logger.error(f"无法获取L2级别几何结构 (错误码: {err_geom})")
            return False
        if err_energy != 0:
            logger.error(f"无法获取L2级别能量 (错误码: {err_energy})")
            return False
        
        logger.info(f"L2中性分子能量: {neutral_energy_l2:.6f} Hartree")
        
        # 创建阳离子
        cation_charge = well0.charge + 1
        # 阳离子多重度计算
        if well0.mult % 2 == 1:  # 奇数多重度（偶数电子，通常闭壳层）
            cation_mult = well0.mult + 1  # 失去电子变成开壳层
        else:  # 偶数多重度（奇数电子，开壳层）
            cation_mult = well0.mult - 1  # 失去电子，多重度减少
        
        # 确保多重度至少为1
        if cation_mult < 1:
            cation_mult = 1
        
        logger.info(f"创建阳离子: 电荷={cation_charge}, 多重度={cation_mult}")
        
        cation = StationaryPoint(f'{well0.chemid}_cation',
                               cation_charge, cation_mult,
                               atom=well0.atom, geom=neutral_geom)
        try:
            cation.characterize()
            cation.calc_chemid()
            logger.info(f"阳离子chemid: {cation.chemid}")
        except Exception as e:
            logger.warning(f"阳离子表征失败: {e}")
            cation.chemid = f"{well0.chemid}_cation"
            logger.info(f"使用简化chemid: {cation.chemid}")
        
        # 检查阳离子L2能量是否存在
        cation_job = f"{cation.chemid}_well_high"
        logger.info(f"检查阳离子L2结果: {cation_job}")
        
        err_cation, cation_energy_l2 = qc.get_qc_energy(cation_job)
        
        if err_cation != 0:
            logger.warning(f"无法获取阳离子L2能量 (错误码: {err_cation})，可能需要先计算阳离子")
            # 尝试使用AIE计算的结果
            if hasattr(well0, 'aie_cation_energy'):
                cation_energy_l2 = well0.aie_cation_energy
                logger.info(f"使用AIE计算的阳离子能量: {cation_energy_l2:.6f} Hartree")
            else:
                logger.error("无法获取阳离子L2能量，跳过L3计算")
                return False
        else:
            logger.info(f"L2阳离子能量: {cation_energy_l2:.6f} Hartree")
        
        # 获取阳离子的L2零点能（参考中性分子的提取方式）
        # 阳离子的high_level计算结果应该在 {cation.chemid}_well_high 中
        cation_high_job = f"{cation.chemid}_well_high"
        logger.info(f"从阳离子high_level结果获取零点能: {cation_high_job}")
        
        err_cation_zpe, cation_zpe = qc.get_qc_zpe(cation_high_job, 1)  # wait=1 等待计算完成
        if err_cation_zpe != 0:
            logger.warning(f"无法获取阳离子零点能 (错误码: {err_cation_zpe})，使用0作为默认值")
            cation_zpe = 0.0
        else:
            logger.info(f"阳离子L2零点能: {cation_zpe:.8f} Hartree")
        
        # 获取阳离子的L2优化几何结构（也从high_level结果中获取）
        err_cation_geom, cation_geom = qc.get_qc_geom(cation_high_job, well0.natom)
        if err_cation_geom != 0:
            logger.warning(f"无法获取阳离子L2几何结构 (错误码: {err_cation_geom})，使用中性分子几何结构")
            cation_geom = neutral_geom
        else:
            logger.info(f"成功获取阳离子L2几何结构: {cation_high_job}")
        
        # 移除旧的复杂搜索逻辑，直接使用high_level结果
        cation_job_for_zpe = cation_high_job
        
        # 更新阳离子对象的几何结构
        cation.geom = cation_geom
        logger.info("已更新阳离子几何结构为L2优化结果")
        
        # 执行L3单点能计算（并行提交）
        logger.info("开始执行L3单点能计算...")
        logger.info("="*60)
        logger.info("并行提交中性分子和阳离子L3计算")
        logger.info("="*60)
        
        # 并行提交两个L3计算
        neutral_job_name = submit_l3_single_point_kinbot(well0, neutral_geom, par, qc, 'neutral', well0.chemid)
        cation_job_name = submit_l3_single_point_kinbot(cation, cation_geom, par, qc, 'cation', well0.chemid)
        
        if neutral_job_name is None or cation_job_name is None:
            logger.error("L3计算提交失败")
            return False
        
        logger.info(f"已提交中性分子L3计算: {neutral_job_name}")
        logger.info(f"已提交阳离子L3计算: {cation_job_name}")
        
        # 等待两个计算完成并获取结果（智能并行等待）
        logger.info("等待L3计算完成...")
        neutral_l3_energy, cation_l3_energy = wait_for_parallel_l3_completion(
            neutral_job_name, cation_job_name, qc
        )
        
        if neutral_l3_energy is None:
            logger.error("中性分子L3计算失败")
            return False
            
        if cation_l3_energy is None:
            logger.error("阳离子L3计算失败")
            return False
        
        # 计算L3级别的AIE（正确方法：L3电子能 + L2零点能）
        logger.info("获取L2级别的零点能修正...")
        
        # 获取中性分子的L2零点能
        neutral_job = f"{well0.chemid}_well"
        err_neutral_zpe, neutral_zpe = qc.get_qc_zpe(neutral_job, 1)
        if err_neutral_zpe != 0:
            logger.warning(f"无法获取中性分子零点能，使用0作为默认值")
            neutral_zpe = 0.0
        else:
            logger.info(f"中性分子L2零点能: {neutral_zpe:.8f} Hartree")
        
        # 使用之前已经获取的阳离子零点能 (cation_zpe)
        logger.info(f"阳离子L2零点能: {cation_zpe:.8f} Hartree")
        
        # 计算包含零点能修正的L3级别AIE
        # AIE = (E_cation_L3 + ZPE_cation_L2) - (E_neutral_L3 + ZPE_neutral_L2)
        neutral_total_energy = neutral_l3_energy + neutral_zpe
        cation_total_energy = cation_l3_energy + cation_zpe
        aie_l3_hartree = cation_total_energy - neutral_total_energy
        aie_l3_ev = aie_l3_hartree * 27.2114
        
        logger.info("="*60)
        logger.info("L3级别电离能计算完成!")
        logger.info(f"  中性分子L3电子能: {neutral_l3_energy:.8f} Hartree")
        logger.info(f"  中性分子L2零点能: {neutral_zpe:.8f} Hartree")
        logger.info(f"  中性分子总能量: {neutral_total_energy:.8f} Hartree")
        logger.info(f"  阳离子L3电子能: {cation_l3_energy:.8f} Hartree")
        logger.info(f"  阳离子L2零点能: {cation_zpe:.8f} Hartree")
        logger.info(f"  阳离子总能量: {cation_total_energy:.8f} Hartree")
        logger.info(f"  L3电离能 (含ZPE修正): {aie_l3_hartree:.6f} Hartree ({aie_l3_ev:.4f} eV)")
        
        # 如果有L2 AIE结果，进行比较
        if hasattr(well0, 'aie_hartree') and well0.aie_hartree is not None:
            aie_l2_hartree = well0.aie_hartree
            aie_l2_ev = well0.aie_ev
            diff_hartree = aie_l3_hartree - aie_l2_hartree
            diff_ev = diff_hartree * 27.2114
            
            logger.info("L2 vs L3 比较:")
            logger.info(f"  L2电离能: {aie_l2_hartree:.6f} Hartree ({aie_l2_ev:.4f} eV)")
            logger.info(f"  L3电离能: {aie_l3_hartree:.6f} Hartree ({aie_l3_ev:.4f} eV)")
            logger.info(f"  差值(L3-L2): {diff_hartree:.6f} Hartree ({diff_ev:.4f} eV)")
        logger.info("="*60)
        
        # 保存L3 AIE结果
        well0.aie_l3_hartree = aie_l3_hartree
        well0.aie_l3_ev = aie_l3_ev
        well0.aie_l3_neutral_energy = neutral_l3_energy  # 仅电子能
        well0.aie_l3_cation_energy = cation_l3_energy    # 仅电子能
        well0.aie_l3_neutral_zpe = neutral_zpe           # L2零点能
        well0.aie_l3_cation_zpe = cation_zpe             # L2零点能
        well0.aie_l3_neutral_total = neutral_total_energy # L3电子能+L2零点能
        well0.aie_l3_cation_total = cation_total_energy   # L3电子能+L2零点能
        well0.aie_l3_status = "completed"
        well0.aie_l3_method = f"L3_{single_point_qc}_with_L2_ZPE"
        
        return True
        
    except Exception as e:
        logger.error(f"L3级别AIE计算过程中发生错误: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return False



def submit_l3_single_point_kinbot(species, geom, par, qc, species_type, base_chemid):
    """
    提交L3单点能计算到队列（非阻塞）
    
    参数:
    species: StationaryPoint对象
    geom: 几何结构
    par: 参数字典
    qc: QuantumChemistry对象
    species_type: 'neutral' 或 'cation'
    base_chemid: 基础化学ID
    
    返回:
    job_name: 作业名称，失败时返回None
    """
    logger = logging.getLogger('KinBot_Conformer_AIE')
    
    try:
        single_point_qc = par.get('single_point_qc', 'molpro')
        logger.info(f"[{species_type.upper()}] 准备L3计算，使用{single_point_qc}")
        
        # 确保几何结构正确设置
        species.geom = geom
        
        # 生成作业名称
        job_name = f"{base_chemid}_{species_type}_l3"
        
        # ✅ 添加重复计算检查
        logger.info(f"[{species_type.upper()}] 检查是否存在已完成的L3计算...")
        completed, existing_energy = check_l3_calculation_completed(par, job_name, species_type)
        
        if completed and existing_energy is not None:
            logger.info(f"[{species_type.upper()}] ✓ 跳过提交：发现已完成的L3计算 ({existing_energy:.8f} Hartree)")
            return job_name  # 返回作业名称，表示"计算已完成"
        else:
            logger.info(f"[{species_type.upper()}] 未发现已完成的计算，开始提交新的L3计算...")
        
        # 创建输入文件
        success = create_l3_input_file(species, par, job_name, species_type)
        if not success:
            logger.error(f"[{species_type.upper()}] 创建L3输入文件失败")
            return None
        
        # 创建队列脚本
        success = create_l3_queue_script(species, par, job_name, species_type, f"{job_name}.inp")
        if not success:
            logger.error(f"[{species_type.upper()}] 创建队列脚本失败")
            return None
        
        # 提交到队列（非阻塞）
        if par.get('queuing', 'local') != 'local':
            success = submit_l3_calculation(par, f"{job_name}.{par.get('queuing', 'local')}", species_type, job_name)
            if not success:
                logger.error(f"[{species_type.upper()}] 提交L3计算到队列失败")
                return None
            logger.info(f"[{species_type.upper()}] 已提交L3计算到队列: {job_name}")
        else:
            # 本地执行
            success = execute_l3_locally(par, 'local', species_type, job_name)
            if not success:
                logger.error(f"[{species_type.upper()}] 启动本地L3计算失败")
                return None
            logger.info(f"[{species_type.upper()}] 已启动本地L3计算: {job_name}")
        
        return job_name
        
    except Exception as e:
        logger.error(f"[{species_type.upper()}] 提交L3计算时发生错误: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return None


def wait_for_l3_completion_and_extract_energy(job_name, qc, species_type):
    """
    等待L3计算完成并提取能量
    
    参数:
    job_name: 作业名称
    qc: QuantumChemistry对象
    species_type: 'neutral' 或 'cation'
    
    返回:
    energy: L3单点能 (Hartree)，失败时返回None
    """
    logger = logging.getLogger('KinBot_Conformer_AIE')
    
    try:
        import time
        import os
        
        output_file = f"{job_name}.out"
        check_interval = 60   # 检查间隔（秒）
        
        logger.info(f"[{species_type.upper()}] 等待L3计算完成: {job_name}")
        
        while True:
            # 检查输出文件是否存在且计算完成
            if os.path.exists(output_file):
                # 检查计算是否完成 - 修复参数不匹配问题
                try:
                    # 需要传递par参数，这里使用基本配置
                    basic_par = {'single_point_key': 'MYENERGY'}
                    completed, energy = check_l3_calculation_completed(basic_par, job_name, species_type)
                    if completed:
                        if energy is not None:
                            logger.info(f"[{species_type.upper()}] L3计算完成: {energy:.8f} Hartree")
                            return energy
                        else:
                            logger.error(f"[{species_type.upper()}] L3计算完成但无法提取能量")
                            return None
                except Exception as e:
                    logger.error(f"[{species_type.upper()}] 检查计算状态时发生错误: {e}")
                    # 继续循环，不要因为检查错误而退出
            
            time.sleep(check_interval)
        
    except Exception as e:
        logger.error(f"[{species_type.upper()}] 等待L3计算完成时发生错误: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return None


def wait_for_parallel_l3_completion(neutral_job_name, cation_job_name, qc):
    """
    智能并行等待两个L3计算完成
    
    参数:
    neutral_job_name: 中性分子作业名称
    cation_job_name: 阳离子作业名称
    qc: QuantumChemistry对象
    
    返回:
    (neutral_energy, cation_energy): 两个能量值的元组，失败时对应位置为None
    """
    logger = logging.getLogger('KinBot_Conformer_AIE')
    
    try:
        import time
        import os
        
        check_interval = 60   # 检查间隔（秒），减少频率
        
        neutral_energy = None
        cation_energy = None
        neutral_completed = False
        cation_completed = False
        
        logger.info("开始并行监控L3计算...")
        logger.info(f"  中性分子作业: {neutral_job_name}")
        logger.info(f"  阳离子作业: {cation_job_name}")
        
        while not neutral_completed or not cation_completed:
            # 检查中性分子计算
            if not neutral_completed:
                neutral_output = f"{neutral_job_name}.out"
                if os.path.exists(neutral_output):
                    completed, energy = check_l3_calculation_completed({'single_point_key': 'MYENERGY'}, neutral_job_name, 'neutral')
                    if completed:
                        if energy is not None:
                            neutral_energy = energy
                            neutral_completed = True
                            logger.info(f"中性分子L3计算完成: {energy:.8f} Hartree")
                        else:
                            logger.error("中性分子L3计算完成但无法提取能量")
                            neutral_completed = True
            
            # 检查阳离子计算
            if not cation_completed:
                cation_output = f"{cation_job_name}.out"
                if os.path.exists(cation_output):
                    completed, energy = check_l3_calculation_completed({'single_point_key': 'MYENERGY'}, cation_job_name, 'cation')
                    if completed:
                        if energy is not None:
                            cation_energy = energy
                            cation_completed = True
                            logger.info(f"阳离子L3计算完成: {energy:.8f} Hartree")
                        else:
                            logger.error("阳离子L3计算完成但无法提取能量")
                            cation_completed = True
            
            # 如果两个都完成了，退出循环
            if neutral_completed and cation_completed:
                break
            
            # 等待下次检查
            time.sleep(check_interval)
        
        # 总结结果
        if neutral_completed and cation_completed:
            if neutral_energy is not None and cation_energy is not None:
                logger.info("两个L3计算都成功完成!")
            else:
                logger.warning("L3计算完成但部分能量提取失败")
        
        return neutral_energy, cation_energy
        
    except Exception as e:
        logger.error(f"并行等待L3计算时发生错误: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return None, None


def execute_l3_locally_background(job_name, par, species_type):
    """
    在后台执行本地L3计算
    
    参数:
    job_name: 作业名称
    par: 参数字典
    species_type: 'neutral' 或 'cation'
    
    返回:
    success: 是否成功启动
    """
    logger = logging.getLogger('KinBot_Conformer_AIE')
    
    try:
        import subprocess
        import os
        
        single_point_qc = par.get('single_point_qc', 'molpro')
        input_file = f"{job_name}.inp"
        output_file = f"{job_name}.out"
        
        if not os.path.exists(input_file):
            logger.error(f"[{species_type.upper()}] 输入文件不存在: {input_file}")
            return False
        
        # 构建命令
        if single_point_qc.lower() == 'molpro':
            # Molpro命令
            nproc = par.get('nproc', 4)
            cmd = f"molpro -n {nproc} {input_file}"
        else:
            logger.error(f"[{species_type.upper()}] 不支持的量子化学软件: {single_point_qc}")
            return False
        
        # 后台执行
        logger.info(f"[{species_type.upper()}] 后台执行命令: {cmd}")
        process = subprocess.Popen(
            cmd,
            shell=True,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            cwd=os.getcwd()
        )
        
        logger.info(f"[{species_type.upper()}] 已启动后台进程 PID: {process.pid}")
        return True
        
    except Exception as e:
        logger.error(f"[{species_type.upper()}] 启动本地L3计算时发生错误: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return False


def execute_l3_single_point_kinbot(species, geom, par, qc, species_type, base_chemid=None):
    """
    执行L3单点能计算：生成inp和pbs文件，自动提交，等待完成并提取能量
    
    参数:
    species: StationaryPoint对象
    geom: 几何结构
    par: 参数字典
    qc: QuantumChemistry对象
    species_type: 'neutral' 或 'cation'
    base_chemid: 基础chemid（用于统一目录，避免阳离子创建新目录）
    
    返回:
    energy: L3单点能 (Hartree)，失败时返回None
    """
    logger = logging.getLogger('KinBot_Conformer_AIE')
    logger.info(f"执行{species_type}的L3单点能计算...")
    
    try:
        import os
        
        # 确保几何结构正确设置
        species.geom = geom
        
        # 生成唯一的作业名称
        if base_chemid is not None:
            job_name = f'{base_chemid}_{species_type}_l3'
        else:
            job_name = f'{species.chemid}_{species_type}_l3'
        
        logger.info(f"[{species_type.upper()}] 作业名称: {job_name}")
        logger.info(f"[{species_type.upper()}] 分子信息: 电荷={species.charge}, 多重度={species.mult}, 原子数={species.natom}")
        
        # 0. 首先检查是否已经有完成的计算
        logger.info(f"[{species_type.upper()}] 检查是否存在已完成的L3计算...")
        completed, existing_energy = check_l3_calculation_completed(par, job_name, species_type)
        
        if completed and existing_energy is not None:
            logger.info(f"[{species_type.upper()}] 跳过计算：发现已完成的L3结果")
            return existing_energy
        else:
            logger.info(f"[{species_type.upper()}] 未发现已完成的计算，开始新的L3计算...")
        
        # 1. 创建L3输入文件
        inp_file = create_l3_input_file(species, par, job_name, species_type)
        if not inp_file:
            logger.error(f"[{species_type.upper()}] 创建L3输入文件失败")
            return None
        
        # 2. 创建队列提交脚本
        script_file = create_l3_queue_script(species, par, job_name, species_type, inp_file)
        if not script_file:
            logger.error(f"[{species_type.upper()}] 创建队列脚本失败")
            return None
        
        # 3. 提交计算
        success = submit_l3_calculation(par, script_file, species_type, job_name)
        if not success:
            logger.error(f"[{species_type.upper()}] 提交L3计算失败")
            return None
        
        # 4. 等待计算完成并获取能量
        logger.info(f"[{species_type.upper()}] 等待L3计算完成...")
        energy = wait_for_l3_completion_and_extract_energy(par, job_name, species_type)
        
        if energy is not None:
            logger.info(f'[{species_type.upper()}] L3能量获取成功: {energy:.8f} Hartree')
            return energy
        else:
            logger.error(f"[{species_type.upper()}] L3计算失败或能量提取失败")
            return None
        
    except Exception as e:
        logger.error(f"L3单点能计算失败: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return None


def create_l3_input_file(species, par, job_name, species_type):
    """
    创建L3输入文件
    """
    logger = logging.getLogger('KinBot_Conformer_AIE')
    
    try:
        import os
        from kinbot import constants
        
        # 获取L3计算参数
        single_point_qc = par.get('single_point_qc', 'molpro').lower()
        template_file = par.get('single_point_template', '')
        energy_key = par.get('single_point_key', 'MYENERGY')
        
        logger.info(f"[{species_type.upper()}] 使用{single_point_qc}进行L3计算")
        
        # 准备几何结构
        geom_lines = []
        for i in range(species.natom):
            atom = species.atom[i]
            x, y, z = species.geom[i]
            geom_line = f"{atom} {x:.6f} {y:.6f} {z:.6f}"
            geom_lines.append(geom_line)
        geometry_str = '\n'.join(geom_lines)
        
        # 计算电子数和自旋
        nelec = sum([constants.znumber[atom] for atom in species.atom]) - species.charge
        spin = species.mult - 1
        
        # 读取或使用默认模板
        if template_file and os.path.exists(template_file):
            with open(template_file, 'r') as f:
                template_content = f.read()
            logger.info(f"[{species_type.upper()}] 使用用户提供的模板文件: {template_file}")
        else:
            # 使用默认的CCSD(T)-F12模板
            template_content = """***,{name}
memory,3000,M
geomtyp=xyz
geometry={{{{
{natom}
{name}
{geom}
}}}}

basis=vtz-F12
{{{{hf;wf,{nelectron},1,{spin},{charge}}}}}
CCSD(T)-F12
{energy_key}=energy"""
            logger.info(f"[{species_type.upper()}] 使用默认CCSD(T)-F12模板")
        
        # 替换模板中的占位符
        input_content = template_content.format(
            name=job_name,
            natom=species.natom,
            geom=geometry_str,
            nelectron=nelec,
            spin=spin,
            charge=species.charge,
            energy_key=energy_key
        )
        
        # 创建输入文件
        input_file = f"{job_name}.inp"
        with open(input_file, 'w') as f:
            f.write(input_content)
        
        logger.info(f"[{species_type.upper()}] L3输入文件已创建: {input_file}")
        
        # 验证输入文件
        if os.path.exists(input_file) and os.path.getsize(input_file) > 0:
            return input_file
        else:
            logger.error(f"[{species_type.upper()}] 输入文件创建失败或为空")
            return None
        
    except Exception as e:
        logger.error(f"创建L3输入文件时发生错误: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return None


def create_l3_queue_script(species, par, job_name, species_type, inp_file):
    """
    创建队列提交脚本（采用KinBot原生的两部分模板系统）
    """
    logger = logging.getLogger('KinBot_Conformer_AIE')
    
    try:
        import os
        
        queuing = par.get('queuing', 'local').lower()
        
        if queuing == 'local':
            # 本地执行不需要队列脚本
            return 'local'
        
        queue_name = par.get('queue_name', 'default')
        queue_template = par.get('queue_template', '')
        ppn = par.get('single_point_ppn', par.get('ppn', 4))
        username = par.get('username', 'user')
        command = par.get('single_point_command', 'molpro')
        
        # 创建队列提交脚本
        script_file = f"{job_name}.{queuing}"
        
        # 第一部分：队列头部模板（用户提供或默认）
        if queue_template and os.path.exists(queue_template):
            logger.info(f"[{species_type.upper()}] 使用用户提供的队列模板: {queue_template}")
            with open(queue_template, 'r') as f:
                queue_head_template = f.read()
        else:
            logger.info(f"[{species_type.upper()}] 使用默认{queuing}队列头部模板")
            if queuing == 'pbs':
                queue_head_template = """#! /bin/bash -f
#PBS -N {name}
#PBS -l nodes=1:ppn={ppn}
#PBS -q {queue_name}
#PBS -o {errdir}/$PBS_JOBNAME.stdout
#PBS -e {errdir}/$PBS_JOBNAME.err
#PBS -m n

"""
            elif queuing == 'slurm':
                queue_head_template = """#! /bin/bash -f

#SBATCH -N 1
#SBATCH -c {ppn}
#SBATCH -q {queue_name}
#SBATCH -o {errdir}/{name}.stdout
#SBATCH -e {errdir}/{name}.err
{slurm_feature}

"""
            else:
                logger.error(f"[{species_type.upper()}] 不支持的队列系统: {queuing}")
                return None
        
        # 第二部分：L3执行模板（类似KinBot的python模板，但执行Molpro）
        if queuing == 'pbs':
            l3_exec_template = """cd ${{PBS_O_WORKDIR}}

# 执行L3 Molpro计算
echo "开始L3计算: {name}"
echo "工作目录: $PWD"
echo "输入文件: {input_file}"
echo "处理器数: {ppn}"
echo "执行命令: {molpro_command}"

{molpro_command}

# 检查计算状态
if [ $? -eq 0 ]; then
    echo "L3计算成功完成"
else
    echo "L3计算失败，退出码: $?"
fi
"""
        elif queuing == 'slurm':
            l3_exec_template = """# 执行L3 Molpro计算
echo "开始L3计算: {name}"
echo "工作目录: $PWD"
echo "输入文件: {input_file}"
echo "处理器数: {ppn}"
echo "执行命令: {molpro_command}"

{molpro_command}

# 检查计算状态
if [ $? -eq 0 ]; then
    echo "L3计算成功完成"
else
    echo "L3计算失败，退出码: $?"
fi
"""
        
        # 构建Molpro执行命令
        if 'molpro' in command.lower():
            # 确保Molpro命令包含-n参数
            if '-n' not in command:
                molpro_exec_cmd = f"{command} -n {ppn} {inp_file}"
            else:
                molpro_exec_cmd = f"{command} {inp_file}"
        else:
            molpro_exec_cmd = f"{command} {inp_file}"
        
        logger.info(f"[{species_type.upper()}] Molpro执行命令: {molpro_exec_cmd}")
        
        # 合并两部分模板（类似KinBot的做法）
        full_template = queue_head_template + l3_exec_template
        
        # 替换模板中的占位符
        script_content = full_template.format(
            name=job_name,
            queue_name=queue_name,
            ppn=ppn,
            username=username,
            molpro_command=molpro_exec_cmd,
            input_file=inp_file,
            errdir=os.getcwd(),
            slurm_feature=par.get('slurm_feature', '')
        )
        
        # 写入队列脚本
        with open(script_file, 'w') as f:
            f.write(script_content)
        
        # 设置执行权限
        os.chmod(script_file, 0o755)
        
        logger.info(f"[{species_type.upper()}] 队列脚本已创建: {script_file}")
        
        # 显示生成的脚本内容（用于调试）
        if par.get('verbose', 0) > 0:
            logger.info(f"[{species_type.upper()}] 生成的队列脚本内容:")
            for i, line in enumerate(script_content.split('\n'), 1):
                logger.info(f"[{species_type.upper()}] {i:2d}: {line}")
        
        return script_file
        
    except Exception as e:
        logger.error(f"[{species_type.upper()}] 创建队列脚本时发生错误: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return None


def submit_l3_calculation(par, script_file, species_type, job_name):
    """
    提交L3计算
    """
    logger = logging.getLogger('KinBot_Conformer_AIE')
    
    try:
        import subprocess
        import os
        
        queuing = par.get('queuing', 'local').lower()
        
        if queuing == 'local':
            # 本地执行
            logger.info(f"[{species_type.upper()}] 本地执行L3计算...")
            return execute_l3_locally(par, script_file, species_type, job_name)
        else:
            # 队列系统执行
            if queuing == 'pbs':
                submit_cmd = ['qsub', script_file]
            elif queuing == 'slurm':
                submit_cmd = ['sbatch', script_file]
            else:
                logger.error(f"[{species_type.upper()}] 不支持的队列系统: {queuing}")
                return False
            
            logger.info(f"[{species_type.upper()}] 提交命令: {' '.join(submit_cmd)}")
            
            result = subprocess.run(submit_cmd, capture_output=True, text=True)
            
            if result.returncode == 0:
                job_id = result.stdout.strip()
                logger.info(f"[{species_type.upper()}] 作业提交成功，作业ID: {job_id}")
                return True
            else:
                logger.error(f"[{species_type.upper()}] 作业提交失败: {result.stderr}")
                return False
        
    except Exception as e:
        logger.error(f"[{species_type.upper()}] 提交L3计算时发生错误: {e}")
        return False


def execute_l3_locally(par, script_file, species_type, job_name):
    """
    本地执行L3计算
    """
    logger = logging.getLogger('KinBot_Conformer_AIE')
    
    try:
        import subprocess
        import os
        
        # 获取L3计算命令
        command = par.get('single_point_command', 'molpro')
        ppn = par.get('single_point_ppn', par.get('ppn', 4))
        
        input_file = f"{job_name}.inp"
        output_file = f"{job_name}.out"
        
        # 构建命令
        if 'molpro' in command.lower():
            # Molpro命令
            if '-n' in command:
                cmd = command.split() + [os.path.basename(input_file)]
            else:
                cmd = [command, '-n', str(ppn), os.path.basename(input_file)]
        else:
            # 其他软件
            cmd = [command, input_file]
        
        logger.info(f"[{species_type.upper()}] 执行命令: {' '.join(cmd)}")
        
        # 执行计算（同步执行，等待完成）
        with open(output_file, 'w') as f:
            result = subprocess.run(cmd, stdout=f, stderr=subprocess.STDOUT, 
                                  cwd=os.getcwd())
        
        if result.returncode == 0:
            logger.info(f"[{species_type.upper()}] L3计算完成")
            return True
        else:
            logger.error(f"[{species_type.upper()}] L3计算失败，返回码: {result.returncode}")
            # 显示输出内容
            if os.path.exists(output_file):
                with open(output_file, 'r') as f:
                    content = f.read()
                logger.error(f"[{species_type.upper()}] 输出内容:\n{content[-1000:]}")  # 显示最后1000字符
            return False
        
    except Exception as e:
        logger.error(f"[{species_type.upper()}] 本地执行L3计算时发生错误: {e}")
        return False


def wait_for_l3_completion_and_extract_energy(par, job_name, species_type):
    """
    等待L3计算完成并提取能量
    每隔1小时提醒一次，无时间限制
    
    参数:
    par: 参数字典
    job_name: 作业名称
    species_type: 'neutral' 或 'cation'
    
    返回:
    energy: L3单点能 (Hartree)，失败时返回None
    """
    logger = logging.getLogger('KinBot_Conformer_AIE')
    
    try:
        import time
        import os
        
        output_file = f"{job_name}.out"
        energy_key = par.get('single_point_key', 'MYENERGY')
        queuing = par.get('queuing', 'local').lower()
        
        # 首先检查是否已经完成（避免不必要的等待）
        logger.info(f"[{species_type.upper()}] 检查L3计算是否已完成...")
        completed, existing_energy = check_l3_calculation_completed(par, job_name, species_type)
        
        if completed and existing_energy is not None:
            logger.info(f"[{species_type.upper()}] L3计算已完成，直接返回结果")
            return existing_energy
        
        # 检查间隔：30秒
        check_interval = 30
        # 提醒间隔：1小时 = 3600秒
        reminder_interval = 3600
        
        start_time = time.time()
        last_reminder_time = start_time
        check_count = 0
        
        logger.info(f"[{species_type.upper()}] 开始等待L3计算完成...")
        logger.info(f"[{species_type.upper()}] 输出文件: {output_file}")
        logger.info(f"[{species_type.upper()}] 能量关键词: {energy_key}")
        
        while True:
            check_count += 1
            current_time = time.time()
            
            # 检查输出文件是否存在
            if os.path.exists(output_file):
                try:
                    with open(output_file, 'r') as f:
                        content = f.read()
                    
                    # 检查是否有错误
                    if 'GLOBAL ERROR' in content or 'ERROR EXIT' in content or 'FATAL ERROR' in content:
                        logger.error(f"[{species_type.upper()}] L3计算出现错误")
                        # 显示错误信息
                        error_lines = []
                        for line in content.split('\n'):
                            if 'ERROR' in line or 'FATAL' in line:
                                error_lines.append(line.strip())
                        if error_lines:
                            logger.error(f"[{species_type.upper()}] 错误信息:")
                            for error_line in error_lines[-5:]:  # 显示最后5个错误
                                logger.error(f"[{species_type.upper()}]   {error_line}")
                        return None
                    
                    # 检查是否完成
                    completion_indicators = [
                        'Variable memory released',
                        'PROGRAMS   *        TOTAL',
                        'Molpro calculation terminated',
                        'Normal termination',
                        '*** OPTIMIZATION CONVERGED ***'
                    ]
                    
                    calculation_completed = any(indicator in content for indicator in completion_indicators)
                    
                    if calculation_completed:
                        logger.info(f"[{species_type.upper()}] L3计算已完成，开始提取能量...")
                        
                        # 提取能量
                        energy = extract_molpro_energy(output_file, energy_key)
                        
                        if energy is not None:
                            elapsed_time = current_time - start_time
                            logger.info(f"[{species_type.upper()}] ✓ L3能量提取成功: {energy:.8f} Hartree")
                            logger.info(f"[{species_type.upper()}] 总计算时间: {elapsed_time/3600:.2f} 小时")
                            return energy
                        else:
                            logger.error(f"[{species_type.upper()}] 计算完成但能量提取失败")
                            logger.error(f"[{species_type.upper()}] 输出文件末尾内容:")
                            lines = content.split('\n')
                            for line in lines[-20:]:  # 显示最后20行
                                if line.strip():
                                    logger.error(f"[{species_type.upper()}]   {line}")
                            return None
                    
                except Exception as e:
                    logger.warning(f"[{species_type.upper()}] 读取输出文件时出错: {e}")
            
            # 每隔1小时提醒一次
            if current_time - last_reminder_time >= reminder_interval:
                elapsed_hours = (current_time - start_time) / 3600
                logger.info(f"[{species_type.upper()}] ⏰ L3计算进行中... (已等待 {elapsed_hours:.1f} 小时)")
                
                # 如果是队列系统，可以检查作业状态
                if queuing in ['pbs', 'slurm']:
                    job_status = check_queue_job_status(queuing, job_name)
                    if job_status:
                        logger.info(f"[{species_type.upper()}] 队列作业状态: {job_status}")
                
                # 显示输出文件大小（如果存在）
                if os.path.exists(output_file):
                    file_size = os.path.getsize(output_file)
                    logger.info(f"[{species_type.upper()}] 输出文件大小: {file_size} bytes")
                else:
                    logger.info(f"[{species_type.upper()}] 输出文件尚未生成")
                
                last_reminder_time = current_time
            
            # 等待下次检查
            time.sleep(check_interval)
        
    except KeyboardInterrupt:
        logger.info(f"[{species_type.upper()}] 用户中断了等待过程")
        return None
    except Exception as e:
        logger.error(f"[{species_type.upper()}] 等待L3计算完成时发生错误: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return None


def check_queue_job_status(queuing, job_name):
    """
    检查队列作业状态
    
    参数:
    queuing: 队列系统类型 ('pbs' 或 'slurm')
    job_name: 作业名称
    
    返回:
    status: 作业状态字符串，失败时返回None
    """
    try:
        import subprocess
        
        if queuing == 'pbs':
            # 使用qstat检查PBS作业状态
            cmd = ['qstat', '-u', '$USER']
        elif queuing == 'slurm':
            # 使用squeue检查SLURM作业状态
            cmd = ['squeue', '-u', '$USER', '--name', job_name]
        else:
            return None
        
        result = subprocess.run(cmd, capture_output=True, text=True)
        
        if result.returncode == 0:
            output = result.stdout
            # 查找包含job_name的行
            for line in output.split('\n'):
                if job_name in line:
                    return line.strip()
            return "作业未在队列中找到（可能已完成或失败）"
        else:
            return f"无法查询作业状态: {result.stderr}"
    
    except Exception as e:
        return f"查询作业状态时出错: {e}"


# 旧的复杂函数已被简化的新函数替代


# 旧的自定义函数已被新的KinBot集成函数替代


def check_cation_l3_files(well0, qc, par):
    """
    检查阳离子L3计算文件是否能正确生成
    
    参数:
    well0: StationaryPoint对象（中性分子）
    qc: QuantumChemistry对象
    par: 参数字典
    
    返回:
    success: 是否成功生成文件
    """
    logger = logging.getLogger('KinBot_Conformer_AIE')
    logger.info("检查阳离子L3计算文件生成...")
    
    try:
        # 获取L2级别的几何结构
        high_level_job = f"{well0.chemid}_well_high"
        err_geom, neutral_geom = qc.get_qc_geom(high_level_job, well0.natom)
        
        if err_geom != 0:
            logger.error("无法获取L2级别几何结构")
            return False
        
        # 创建阳离子
        cation_charge = well0.charge + 1
        cation_mult = well0.mult + 1 if well0.mult % 2 == 1 else well0.mult - 1
        
        cation = StationaryPoint(f'{well0.chemid}_cation',
                               cation_charge, cation_mult,
                               atom=well0.atom, geom=neutral_geom)
        try:
            cation.characterize()
            cation.calc_chemid()
            logger.info(f"阳离子chemid: {cation.chemid}")
        except Exception as e:
            logger.warning(f"阳离子表征失败: {e}")
            cation.chemid = f"{well0.chemid}_cation"
            logger.info(f"使用简化chemid: {cation.chemid}")
        
        logger.info(f"阳离子信息:")
        logger.info(f"  chemid: {cation.chemid}")
        logger.info(f"  电荷: {cation.charge}")
        logger.info(f"  多重度: {cation.mult}")
        logger.info(f"  原子数: {cation.natom}")
        logger.info(f"  原子类型: {cation.atom}")
        
        # 尝试生成阳离子L3输入文件
        logger.info("尝试生成阳离子L3输入文件...")
        inp_file = create_l3_input_file(cation, par, f"{cation.chemid}_cation_l3", 'cation')
        
        if inp_file:
            logger.info("✓ 阳离子L3输入文件生成成功")
            logger.info(f"✓ L3输入文件已生成: {inp_file}")
            logger.info(f"  文件大小: {os.path.getsize(inp_file)} bytes")
            
            # 显示文件内容的前几行
            with open(inp_file, 'r') as f:
                lines = f.readlines()
            logger.info(f"文件内容预览:\n{''.join(lines[:10])}")
            
            return True
        else:
            logger.error("✗ 阳离子L3输入文件生成失败")
            return False
        
    except Exception as e:
        logger.error(f"检查阳离子L3文件生成时发生错误: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return False


def check_l3_calculation_completed(par, job_name, species_type):
    """
    检查L3计算是否已经完成并且可以提取能量
    
    参数:
    par: 参数字典
    job_name: 作业名称
    species_type: 'neutral' 或 'cation'
    
    返回:
    (completed, energy): 
    - completed: 布尔值，表示是否已完成
    - energy: 如果已完成，返回能量值；否则返回None
    """
    logger = logging.getLogger('KinBot_Conformer_AIE')
    
    try:
        import os
        
        # 构建输出文件路径 - 直接在当前目录（ID文件夹）中
        output_file = f'{job_name}.out'
        
        # 检查文件是否存在
        if not os.path.exists(output_file):
            # 尝试在molpro目录中查找（保持兼容性）
            molpro_output = f'{job_name}.out'
            if os.path.exists(molpro_output):
                output_file = molpro_output
            else:
                logger.debug(f"[{species_type.upper()}] L3输出文件不存在: {output_file}")
                return False, None
        
        # 检查文件是否完整（包含计算完成标志）
        with open(output_file, 'r') as f:
            content = f.read()
        
        # 检查是否有错误
        if 'GLOBAL ERROR' in content or 'ERROR EXIT' in content or 'FATAL ERROR' in content:
            logger.debug(f"[{species_type.upper()}] L3计算文件包含错误，需要重新计算")
            return False, None
        
        # 检查计算是否完成
        calculation_completed = False
        if 'molpro calculation terminated' in content.lower():
            calculation_completed = True
        elif 'variable memory released' in content.lower():
            calculation_completed = True
        elif 'programs   total' in content.lower():
            calculation_completed = True
        
        if not calculation_completed:
            logger.debug(f"[{species_type.upper()}] L3计算尚未完成")
            return False, None
        
        # 尝试提取能量
        energy_key = par.get('single_point_key', 'MYENERGY')
        energy = extract_molpro_energy(output_file, energy_key)
        
        if energy is not None:
            logger.info(f"[{species_type.upper()}] ✓ 发现已完成的L3计算，能量: {energy:.8f} Hartree")
            return True, energy
        else:
            logger.debug(f"[{species_type.upper()}] L3计算文件存在但无法提取能量，需要重新计算")
            return False, None
            
    except Exception as e:
        logger.debug(f"[{species_type.upper()}] 检查L3计算完成状态时发生错误: {e}")
        return False, None


def extract_molpro_energy(output_file, energy_key):
    """从Molpro输出文件中提取能量"""
    import logging
    import os
    
    logger = logging.getLogger('KinBot_Conformer_AIE')
    
    try:
        # 如果文件不存在，尝试在molpro目录中查找
        if not os.path.exists(output_file):
            molpro_output = f'molpro/{os.path.basename(output_file)}'
            if os.path.exists(molpro_output):
                output_file = molpro_output
        
        with open(output_file, 'r') as f:
            content = f.read()
        
        # 首先查找SETTING格式的能量关键词（Molpro常用格式）
        # 格式：SETTING MYENERGY       =      -158.21815780  AU
        for line in content.split('\n'):
            if f'SETTING {energy_key}' in line and '=' in line:
                try:
                    # 提取等号后面的数值部分
                    parts = line.split('=')[1].strip().split()
                    energy_str = parts[0]  # 第一个部分是能量值
                    energy = float(energy_str)
                    logger.info(f"找到SETTING格式能量 '{energy_key}': {energy}")
                    return energy
                except (ValueError, IndexError) as e:
                    logger.debug(f"SETTING格式解析失败: {e}")
                    continue
        
        # 查找简单的赋值格式：MYENERGY=value
        for line in content.split('\n'):
            if energy_key in line and '=' in line and 'SETTING' not in line:
                try:
                    energy_str = line.split('=')[1].strip()
                    # 移除可能的单位（如AU）
                    energy_str = energy_str.split()[0]
                    energy = float(energy_str)
                    logger.info(f"找到赋值格式能量 '{energy_key}': {energy}")
                    return energy
                except (ValueError, IndexError) as e:
                    logger.debug(f"赋值格式解析失败: {e}")
                    continue
        
        # 如果没有找到自定义关键词，尝试查找标准CCSD(T)能量
        patterns = [
            ('PNO-LCCSD(T)-F12', 'energy='),  # PNO-LCCSD(T)-F12/cc-pVTZ-F12 energy=   -158.218157798096
            ('PNO-CCSD(T)-F12', 'energy='),
            ('LCCSD(T)-F12', 'energy='),
            ('CCSD(T)-F12', 'energy='),
            ('CCSD(T)', 'total energy'),
            ('Total energy', '=')
        ]
        
        for pattern, separator in patterns:
            for line in content.split('\n'):
                if pattern in line and separator in line:
                    try:
                        if '=' in separator:
                            energy_str = line.split('=')[-1].strip()
                        else:
                            energy_str = line.split()[-1]
                        
                        # 移除可能的单位
                        energy_str = energy_str.split()[0] if ' ' in energy_str else energy_str
                        energy = float(energy_str)
                        logger.info(f"找到标准能量模式 '{pattern}': {energy}")
                        return energy
                    except (ValueError, IndexError) as e:
                        logger.debug(f"标准格式解析失败 {pattern}: {e}")
                        continue
        
        logger.error(f"无法从Molpro输出文件中提取能量 (关键词: {energy_key})")
        logger.error(f"输出文件: {output_file}")
        
        # 显示包含能量关键词的行
        logger.error("包含能量关键词的行:")
        for i, line in enumerate(content.split('\n')):
            if energy_key.lower() in line.lower() or 'energy' in line.lower():
                logger.error(f"  第{i+1}行: {line}")
        
        logger.error(f"输出文件末尾内容（最后20行）:")
        lines = content.split('\n')
        for line in lines[-20:]:
            if line.strip():
                logger.error(f"  {line}")
        return None
        
    except Exception as e:
        logger.error(f"读取Molpro输出文件时发生错误: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return None





def calculate_aie_manual_integrated(well0, qc, par, custom_params):
    """
    手动计算AIE，基于high_level优化结果
    整合版本，直接在主脚本中实现
    
    参数:
    well0: StationaryPoint对象（中性分子）
    qc: QuantumChemistry对象
    par: 参数字典
    custom_params: 自定义参数字典
    
    返回:
    success: 是否成功
    """
    logger = logging.getLogger('KinBot_Conformer_AIE')
    
    if not custom_params.get('aie_only_lowest', True):
        logger.info("跳过AIE计算（未启用aie_only_lowest）")
        return True
    
    logger.info("开始手动AIE计算（基于high_level结果）...")
    
    # 使用high_level优化后的几何结构和能量
    high_level_job = f"{well0.chemid}_well_high"
    
    # 检查high_level计算是否完成
    high_status = qc.check_qc(high_level_job)
    if high_status != 'normal':
        logger.warning(f"High_level计算状态异常: {high_status}，使用当前几何结构")
        neutral_geom = well0.geom
        neutral_energy = well0.energy
    else:
        # 读取high_level优化后的几何结构和能量
        err_geom, neutral_geom = qc.get_qc_geom(high_level_job, well0.natom)
        err_energy, neutral_energy = qc.get_qc_energy(high_level_job)
        
        if err_geom != 0 or err_energy != 0:
            logger.warning("无法读取high_level结果，使用当前几何结构")
            neutral_geom = well0.geom
            neutral_energy = well0.energy
        else:
            logger.info(f"使用high_level结果 (中性分子能量: {neutral_energy:.6f} Hartree)")
    
    # 计算阳离子（电荷+1，自旋多重度调整）
    cation_charge = well0.charge + 1
    # 阳离子多重度计算
    if well0.mult % 2 == 1:  # 奇数多重度（偶数电子，通常闭壳层）
        cation_mult = well0.mult + 1  # 失去电子变成开壳层
    else:  # 偶数多重度（奇数电子，开壳层）
        cation_mult = well0.mult - 1  # 失去电子，多重度减少
    
    # 确保多重度至少为1
    if cation_mult < 1:
        cation_mult = 1
    
    logger.info(f"优化阳离子结构 (电荷={cation_charge}, 多重度={cation_mult})")
    
    # 创建阳离子StationaryPoint对象
    cation = StationaryPoint(f'{well0.chemid}_cation',
                           cation_charge,
                           cation_mult,
                           atom=well0.atom,
                           geom=neutral_geom)
    
    # 表征阳离子分子结构并计算chemid
    try:
        cation.characterize()
        cation.calc_chemid()
        logger.info(f"阳离子chemid: {cation.chemid}")
    except Exception as e:
        logger.warning(f"阳离子表征失败: {e}")
        # 如果表征失败，使用简单的命名方式
        cation.chemid = f"{well0.chemid}_cation"
        logger.info(f"使用简单命名: {cation.chemid}")
    
    # 优化阳离子几何结构
    qc.qc_opt(cation, neutral_geom, high_level=1)
    
    # 等待阳离子优化完成
    cation_job = f"{cation.chemid}_well_high"
    logger.info(f"等待阳离子优化完成: {cation_job}")
    
    # 检查阳离子优化状态（等待完成）
    import time
    while True:
        cation_status = qc.check_qc(cation_job)
        if cation_status == 'normal':
            break
        elif cation_status == 'error':
            logger.error("阳离子优化失败")
            return False
        else:
            time.sleep(10)
            logger.info(f"等待阳离子优化完成...")  # 移除时间显示
    
    if cation_status == 'normal':
        # 读取阳离子能量
        err_cation, cation_energy = qc.get_qc_energy(cation_job)
        if err_cation == 0:
            # 计算AIE = E(cation) - E(neutral)
            aie_hartree = cation_energy - neutral_energy
            aie_ev = aie_hartree * 27.2114  # 转换为eV
            
            logger.info(f"电离能计算完成:")
            logger.info(f"  中性分子能量: {neutral_energy:.6f} Hartree")
            logger.info(f"  阳离子能量: {cation_energy:.6f} Hartree")
            logger.info(f"  电离能: {aie_hartree:.6f} Hartree ({aie_ev:.4f} eV)")
            
            # 保存AIE结果到species对象
            well0.aie_hartree = aie_hartree
            well0.aie_ev = aie_ev
            well0.aie_neutral_energy = neutral_energy
            well0.aie_cation_energy = cation_energy
            well0.aie_method = "manual_high_level"
            well0.aie_cation_charge = cation_charge
            well0.aie_cation_mult = cation_mult
            
            return True
        else:
            logger.error("无法读取阳离子能量")
            return False
    else:
        logger.error(f"阳离子优化超时或失败: {cation_status}")
        return False


def run_conformer_aie_calculation(kinbot_params, custom_params, output_dir=None):
    """
    运行构象搜索和电离能计算
    
    参数:
    kinbot_params: kinbot原生参数字典
    custom_params: 自定义参数字典
    output_dir: 输出目录
    """
    
    # 设置日志
    logger = config_log('KinBot_Conformer_AIE')
    logger.info("开始构象搜索和电离能计算")
    logger.info(license_message.message)
    
    # 设置工作目录
    if output_dir:
        os.makedirs(output_dir, exist_ok=True)
        os.chdir(output_dir)
    
    # 自动拷贝template文件到工作目录
    import shutil
    
    # 尝试多个可能的template目录位置
    script_dir = os.path.dirname(os.path.abspath(__file__))
    possible_template_dirs = [
        os.path.join(script_dir, 'kinbot', 'tpl'),  # 脚本同级目录下的kinbot/tpl
        os.path.join(os.getcwd(), 'kinbot', 'tpl'),  # 当前工作目录下的kinbot/tpl
        './kinbot/tpl',  # 相对路径
    ]
    
    template_dir = None
    for tdir in possible_template_dirs:
        if os.path.exists(tdir):
            template_dir = tdir
            break
    
    if template_dir:
        logger.info(f"找到template目录: {template_dir}")
        logger.info("拷贝kinbot template文件到工作目录...")
        for template_file in os.listdir(template_dir):
            if template_file.endswith(('.tpl', '.tpl.py', '.inp')):
                src = os.path.join(template_dir, template_file)
                dst = os.path.join('.', template_file)
                try:
                    shutil.copy2(src, dst)
                    logger.debug(f"拷贝template文件: {template_file}")
                except Exception as e:
                    logger.warning(f"拷贝template文件失败 {template_file}: {e}")
    else:
        logger.warning("未找到kinbot template目录")
    
    # 拷贝用户自定义的队列模板和单点能模板文件
    logger.info("拷贝用户自定义模板文件...")
    
    # 根据配置文件中指定的模板文件来确定需要拷贝的文件
    user_template_files = []
    
    # 添加队列模板文件
    if kinbot_params.get('queue_template'):
        user_template_files.append(kinbot_params['queue_template'])
        logger.debug(f"添加队列模板文件: {kinbot_params['queue_template']}")
    else:
        # 如果没有指定，使用默认的队列模板文件
        user_template_files.append('queue_template.yaml')
        logger.debug("使用默认队列模板文件: queue_template.yaml")
    
    # 添加单点能模板文件
    if kinbot_params.get('single_point_template'):
        user_template_files.append(kinbot_params['single_point_template'])
        logger.debug(f"添加单点能模板文件: {kinbot_params['single_point_template']}")
    
    # 添加其他可能的模板文件（如果在参数中指定）
    other_template_params = ['molpro_template', 'orca_template', 'gaussian_template']
    for param in other_template_params:
        if kinbot_params.get(param):
            user_template_files.append(kinbot_params[param])
            logger.debug(f"添加{param}: {kinbot_params[param]}")
    
    logger.info(f"需要拷贝的用户模板文件: {user_template_files}")
    
    for template_file in user_template_files:
        src = os.path.join(script_dir, template_file)
        dst = os.path.join('.', template_file)
        
        if os.path.exists(src):
            try:
                shutil.copy2(src, dst)
                logger.info(f"拷贝用户模板文件: {template_file}")
            except Exception as e:
                logger.warning(f"拷贝用户模板文件失败 {template_file}: {e}")
        else:
            # 检查是否是队列模板文件（通过参数来源判断）
            is_queue_template = (
                template_file == kinbot_params.get('queue_template') or 
                template_file == 'queue_template.yaml'
            )
            
            if is_queue_template:
                # 对于队列模板文件，尝试使用示例文件
                example_src = os.path.join(script_dir, 'tmp_rovodev_queue_template_example.yaml')
                if os.path.exists(example_src):
                    try:
                        shutil.copy2(example_src, dst)
                        logger.info(f"使用示例队列模板文件: {template_file}")
                    except Exception as e:
                        logger.warning(f"拷贝示例队列模板失败: {e}")
                        # 创建一个最基本的队列模板文件
                        try:
                            with open(dst, 'w') as f:
                                f.write("# 基本队列模板文件\nlocal:\n  max_jobs: 4\n  timeout: 86400\n")
                            logger.info("创建了基本的队列模板文件")
                        except Exception as e2:
                            logger.warning(f"创建基本队列模板失败: {e2}")
                else:
                    # 直接创建基本队列模板文件
                    try:
                        with open(dst, 'w') as f:
                            f.write("# 基本队列模板文件\nlocal:\n  max_jobs: 4\n  timeout: 86400\n")
                        logger.info("创建了基本的队列模板文件")
                    except Exception as e:
                        logger.warning(f"创建基本队列模板失败: {e}")
            else:
                logger.debug(f"用户模板文件不存在: {template_file}")
    
    # 拷贝AIE计算模块到工作目录
    logger.info("拷贝AIE计算模块到工作目录...")
    aie_module_file = 'tmp_rovodev_aie_manual.py'
    aie_src = os.path.join(script_dir, aie_module_file)
    aie_dst = os.path.join('.', aie_module_file)
    
    if os.path.exists(aie_src):
        try:
            shutil.copy2(aie_src, aie_dst)
            logger.info(f"拷贝AIE计算模块: {aie_module_file}")
        except Exception as e:
            logger.warning(f"拷贝AIE计算模块失败: {e}")
    else:
        logger.warning(f"AIE计算模块不存在: {aie_src}")
    
    try:
        # 导入必要的模块
        from kinbot.stationary_pt import StationaryPoint
        
        # 创建临时输入文件（只包含kinbot原生参数）
        input_file = "kinbot_input.json"
        with open(input_file, 'w') as f:
            json.dump(kinbot_params, f, indent=2)
        
        # 初始化参数
        logger.info("初始化计算参数...")
        masterpar = Parameters(input_file, show_warnings=True)
        par = masterpar.par
        
        # 将自定义参数添加到par字典中（用于后续逻辑判断）
        par.update(custom_params)
        
        # 创建必要的目录
        make_dirs(par)
        
        # 初始化分子
        if par.get('smiles'):
            logger.info(f"初始化分子: {par['smiles']}")
            well0 = StationaryPoint('well0',
                                   par['charge'],
                                   par['mult'],
                                   smiles=par['smiles'])
        elif par.get('structure'):
            logger.info(f"初始化分子: 使用几何结构")
            # 计算原子数量
            natom = len(par['structure']) // 4
            logger.info(f"检测到 {natom} 个原子")
            well0 = StationaryPoint('well0',
                                   par['charge'],
                                   par['mult'],
                                   structure=par['structure'],
                                   natom=natom)
        else:
            logger.error("必须提供SMILES或几何结构")
            return False
        well0.short_name = 'w1'
        
        # 写入初始几何结构
        with open('initial_geometry.xyz', 'w') as geom_out:
            geom_out.write('{}\n\n'.format(well0.natom))
            for i, at in enumerate(well0.atom):
                x, y, z = well0.geom[i]
                geom_out.write('{} {:.6f} {:.6f} {:.6f}\n'.format(at, x, y, z))
            geom_out.write('\n\n')
        
        # 表征分子
        logger.info("表征分子结构...")
        well0.characterize()
        well0.name = str(well0.chemid)
        
        # 初始化量子化学计算
        logger.info("初始化量子化学计算...")
        qc = QuantumChemistry(par)
        
        # 清理文件
        if par['do_clean']:
            clean_files()
        
        # 初始结构优化
        logger.info("开始初始结构优化...")
        qc.qc_opt(well0, well0.geom)
        err, well0.geom = qc.get_qc_geom(str(well0.chemid) + '_well',
                                         well0.natom, wait=1)
        
        if err < 0:
            logger.error('初始结构优化失败')
            return False
        
        logger.info("获取初始结构频率...")
        err, well0.freq = qc.get_qc_freq(str(well0.chemid) + '_well',
                                         well0.natom, wait=1)
        
        if err < 0:
            logger.error('初始结构频率计算失败')
            return False
        
        # 检查频率
        if well0.freq[0] <= 0:
            logger.warning(f'第一个频率为负值: {well0.freq[0]}')
            well0.freq[0] *= -1.
        
        # 重新表征优化后的结构
        well0 = StationaryPoint('well0',
                               par['charge'],
                               par['mult'],
                               atom=well0.atom.copy(),
                               geom=well0.geom.copy())
        well0.short_name = 'w1'
        well0.characterize()
        well0.name = str(well0.chemid)
        
        # 获取能量和零点能
        err, well0.energy = qc.get_qc_energy(str(well0.chemid) + '_well', 1)
        err, well0.zpe = qc.get_qc_zpe(str(well0.chemid) + '_well', 1)
        well0.start_energy = well0.energy
        well0.start_zpe = well0.zpe
        
        # 开始构象搜索和高精度优化
        logger.info("开始构象搜索和高精度优化...")
        well_opt = Optimize(well0, par, qc, wait=1)
        well_opt.do_optimization()
        
        if well_opt.shigh == -999:
            logger.error('高精度优化失败')
            return False
        
        # 手动AIE计算 - 直接在主脚本中实现
        logger.info("开始手动AIE计算（基于high_level结果）...")
        aie_success = calculate_aie_manual_integrated(well0, qc, par, custom_params)
        if not aie_success:
            logger.warning("AIE计算失败，但继续执行其他步骤")
        
        # L3级别AIE计算
        logger.info(f"检查L3计算参数: calc_aie_l3={custom_params.get('calc_aie_l3', False)}, L3_calc={par.get('L3_calc', 0)}, aie_success={aie_success}")
        
        if (custom_params.get('calc_aie_l3', False) or par.get('L3_calc', 0)) and aie_success:
            logger.info("开始L3级别AIE计算...")
            logger.info(f"L3计算参数: single_point_qc={par.get('single_point_qc', 'None')}")
            logger.info(f"L3计算模板: single_point_template={par.get('single_point_template', 'None')}")
            logger.info(f"L3能量关键词: single_point_key={par.get('single_point_key', 'None')}")
            
            # 首先检查阳离子L3文件生成
            logger.info("=" * 60)
            logger.info("检查阳离子L3计算文件生成")
            logger.info("=" * 60)
            check_success = check_cation_l3_files(well0, qc, par)
            logger.info("=" * 60)
            
            if check_success:
                logger.info("阳离子L3文件检查通过，开始完整L3计算...")
                success_l3 = calculate_aie_l3_auto(well0, qc, par)
                if success_l3:
                    logger.info("L3级别AIE计算完成")
                else:
                    logger.warning("L3级别AIE计算失败")
            else:
                logger.error("阳离子L3文件生成失败，跳过L3计算")
        elif not aie_success:
            logger.warning("跳过L3计算：AIE计算未成功")
        else:
            logger.info("跳过L3计算：未启用L3计算参数")

        logger.info("计算完成！")
        logger.info(f"分子化学ID: {well0.chemid}")
        logger.info(f"构象数量: {len(well0.conformer_geom) if hasattr(well0, 'conformer_geom') else 0}")
        
        # 保存结果摘要
        save_results_summary(well0, qc, par)
        
        return True
        
    except Exception as e:
        logger.error(f"计算过程中发生错误: {e}")
        return False


def save_results_summary(species, qc, par):
    """
    保存计算结果摘要
    """
    summary = {
        "molecule_info": {
            "smiles": par['smiles'],
            "chemid": species.chemid,
            "charge": species.charge,
            "multiplicity": species.mult,
            "natom": species.natom
        },
        "calculation_settings": {
            "method_L1": f"{par['method']}/{par['basis']}",
            "method_L2": f"{par['high_level_method']}/{par['high_level_basis']}",
            "conformer_search": par['conformer_search'],
            "calc_aie": par['calc_aie']
        },
        "results": {
            "energy_hartree": getattr(species, 'energy', None),
            "zpe_hartree": getattr(species, 'zpe', None),
            "frequencies_cm-1": getattr(species, 'freq', []),
            "num_conformers": len(getattr(species, 'conformer_geom', [])),
            "conformer_energies": getattr(species, 'conformer_energy', []),
            "conformer_indices": getattr(species, 'conformer_index', [])
        },
        "aie_results": {
            "aie_hartree": getattr(species, 'aie_hartree', None),
            "aie_ev": getattr(species, 'aie_ev', None),
            "neutral_energy_hartree": getattr(species, 'aie_neutral_energy', None),
            "cation_energy_hartree": getattr(species, 'aie_cation_energy', None),
            "aie_conformer_index": getattr(species, 'aie_conformer_index', None),
            "aie_l3_hartree": getattr(species, 'aie_l3_hartree', None),
            "aie_l3_ev": getattr(species, 'aie_l3_ev', None),
            "aie_l3_neutral_energy": getattr(species, 'aie_l3_neutral_energy', None),
            "aie_l3_cation_energy": getattr(species, 'aie_l3_cation_energy', None),
            "aie_l3_neutral_zpe": getattr(species, 'aie_l3_neutral_zpe', None),
            "aie_l3_cation_zpe": getattr(species, 'aie_l3_cation_zpe', None),
            "aie_l3_neutral_total": getattr(species, 'aie_l3_neutral_total', None),
            "aie_l3_cation_total": getattr(species, 'aie_l3_cation_total', None),
            "aie_l3_status": getattr(species, 'aie_l3_status', None),
            "aie_l3_method": getattr(species, 'aie_l3_method', None)
        }
    }
    
    with open('calculation_summary.json', 'w') as f:
        json.dump(summary, f, indent=2, default=str)
    
    print("\n" + "="*60)
    print("计算结果摘要")
    print("="*60)
    print(f"分子SMILES: {summary['molecule_info']['smiles']}")
    print(f"化学ID: {summary['molecule_info']['chemid']}")
    print(f"原子数: {summary['molecule_info']['natom']}")
    print(f"电荷: {summary['molecule_info']['charge']}")
    print(f"多重度: {summary['molecule_info']['multiplicity']}")
    print(f"计算方法: {summary['calculation_settings']['method_L1']} -> {summary['calculation_settings']['method_L2']}")
    print(f"构象数量: {summary['results']['num_conformers']}")
    if summary['results']['energy_hartree']:
        print(f"能量: {summary['results']['energy_hartree']:.8f} Hartree")
    if summary['results']['zpe_hartree']:
        print(f"零点能: {summary['results']['zpe_hartree']:.8f} Hartree")
    
    # 显示AIE计算结果
    if summary['aie_results']['aie_hartree'] is not None:
        print(f"电离能 (L2): {summary['aie_results']['aie_hartree']:.6f} Hartree ({summary['aie_results']['aie_ev']:.3f} eV)")
        print(f"中性分子能量: {summary['aie_results']['neutral_energy_hartree']:.8f} Hartree")
        print(f"阳离子能量: {summary['aie_results']['cation_energy_hartree']:.8f} Hartree")
        print(f"AIE计算构象: {summary['aie_results']['aie_conformer_index']}")
        
        # 显示L3电离能结果
        if summary['aie_results']['aie_l3_hartree'] is not None:
            print(f"电离能 (L3): {summary['aie_results']['aie_l3_hartree']:.6f} Hartree ({summary['aie_results']['aie_l3_ev']:.3f} eV)")
            print(f"L3计算方法: {summary['aie_results']['aie_l3_method']}")
            
            # 显示详细的能量分解
            if summary['aie_results']['aie_l3_neutral_energy'] is not None:
                print("L3能量分解:")
                print(f"  中性分子L3电子能: {summary['aie_results']['aie_l3_neutral_energy']:.8f} Hartree")
                if summary['aie_results']['aie_l3_neutral_zpe'] is not None:
                    print(f"  中性分子L2零点能: {summary['aie_results']['aie_l3_neutral_zpe']:.8f} Hartree")
                if summary['aie_results']['aie_l3_neutral_total'] is not None:
                    print(f"  中性分子总能量: {summary['aie_results']['aie_l3_neutral_total']:.8f} Hartree")
                    
                print(f"  阳离子L3电子能: {summary['aie_results']['aie_l3_cation_energy']:.8f} Hartree")
                if summary['aie_results']['aie_l3_cation_zpe'] is not None:
                    print(f"  阳离子L2零点能: {summary['aie_results']['aie_l3_cation_zpe']:.8f} Hartree")
                if summary['aie_results']['aie_l3_cation_total'] is not None:
                    print(f"  阳离子总能量: {summary['aie_results']['aie_l3_cation_total']:.8f} Hartree")
            
            l2_l3_diff = summary['aie_results']['aie_l3_ev'] - summary['aie_results']['aie_ev']
            print(f"L3-L2差异: {l2_l3_diff:.3f} eV")
        else:
            print("L3电离能: 未计算")
    else:
        print("电离能: 未计算")
    
    print("="*60)


def main():
    """
    主函数
    """
    # 初始化基本日志（用于自动生成目录时的日志输出）
    import logging
    logging.basicConfig(level=logging.INFO, format='%(asctime)s-%(levelname)s: %(message)s', 
                       datefmt='%d-%b-%y %H:%M:%S')
    logger = logging.getLogger('KinBot')
    
    parser = argparse.ArgumentParser(
        description="KinBot构象搜索和电离能计算脚本",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
示例用法:
  python kinbot_conformer_aie.py --smiles "CCO" --charge 0 --mult 1
  python kinbot_conformer_aie.py --smiles "C1=CC=CC=C1" --charge 0 --mult 1 --method m062x --basis "6-31+g*"
  python kinbot_conformer_aie.py --config config.json
        """
    )
    
    # 基本参数
    parser.add_argument('--smiles', type=str, help='分子的SMILES字符串')
    parser.add_argument('--charge', type=int, default=0, help='分子电荷 (默认: 0)')
    parser.add_argument('--mult', type=int, default=1, help='分子多重度 (默认: 1)')
    parser.add_argument('--title', type=str, help='计算标题')
    
    # 计算方法参数
    parser.add_argument('--qc', type=str, default='gauss', choices=['gauss', 'qchem', 'nwchem'], 
                       help='量子化学程序 (默认: gauss)')
    parser.add_argument('--method', type=str, default='b3lyp', help='L1级别计算方法 (默认: b3lyp)')
    parser.add_argument('--basis', type=str, default='6-31+g*', help='L1级别基组 (默认: 6-31+g*)')
    parser.add_argument('--high-level-method', type=str, default='M062X', help='L2级别计算方法 (默认: M062X)')
    parser.add_argument('--high-level-basis', type=str, default='6-311++G(d,p)', help='L2级别基组 (默认: 6-311++G(d,p))')
    
    # 构象搜索参数
    parser.add_argument('--conf-grid', type=int, default=3, help='构象搜索网格 (默认: 3)')
    parser.add_argument('--max-dihed', type=int, default=5, help='最大二面角数 (默认: 5)')
    parser.add_argument('--random-conf', type=int, default=500, help='随机构象数 (默认: 500)')
    parser.add_argument('--difference-threshold', type=float, default=0.1, help='构象差异阈值 kcal/mol (默认: 0.1)')
    
    # 队列系统参数
    parser.add_argument('--queuing', type=str, default='local', choices=['slurm', 'pbs', 'local'],
                       help='队列系统 (默认: local)')
    parser.add_argument('--queue-name', type=str, default='local', help='队列名称 (默认: local)')
    parser.add_argument('--ppn', type=int, default=4, help='每个作业的处理器数 (默认: 4)')
    parser.add_argument('--queue-template', type=str, default='', help='队列模板文件路径')
    parser.add_argument('--username', type=str, default='', help='用户名')
    parser.add_argument('--queue-job-limit', type=int, default=50, help='队列作业限制 (默认: 50)')
    
    # AIE计算参数
    parser.add_argument('--aie-only-lowest', action='store_true', default=True, 
                       help='只计算最低能量构象的电离能 (默认: True)')
    parser.add_argument('--aie-all-conformers', action='store_true', 
                       help='计算所有构象的电离能 (覆盖 --aie-only-lowest)')
    parser.add_argument('--calc-aie-l3', action='store_true', 
                       help='开启L3高精度电离能计算')
    
    # L3计算参数
    parser.add_argument('--l3-calc', action='store_true', help='开启L3计算')
    parser.add_argument('--single-point-qc', type=str, default='molpro', 
                       choices=['molpro', 'orca', 'gauss'], help='L3计算软件 (默认: molpro)')
    parser.add_argument('--single-point-template', type=str, default='', 
                       help='L3计算模板文件路径')
    parser.add_argument('--single-point-key', type=str, default='MYENERGY', 
                       help='L3能量关键词 (默认: MYENERGY)')
    parser.add_argument('--single-point-command', type=str, default='molpro', 
                       help='L3计算命令 (默认: molpro)')
    parser.add_argument('--single-point-ppn', type=int, default=4, 
                       help='L3计算处理器数 (默认: 4)')
    
    # 优化器设置
    parser.add_argument('--use-sella', action='store_true', 
                       help='使用Sella优化器 (默认: False)')
    
    # 环状分子参数
    parser.add_argument('--flat-ring-dih-angle', type=float, default=5.0,
                       help='平面环二面角阈值 (默认: 5.0)')
    
    # 其他参数
    parser.add_argument('--qc-command', type=str, default='g16', help='量子化学程序命令 (默认: g16)')
    parser.add_argument('--output-dir', type=str, help='输出目录')
    parser.add_argument('--config', type=str, help='配置文件路径 (JSON格式)')
    parser.add_argument('--verbose', action='store_true', help='详细输出')
    
    args = parser.parse_args()
    
    # 确定AIE计算策略
    aie_only_lowest = not args.aie_all_conformers if hasattr(args, 'aie_all_conformers') else True
    
    # 从配置文件读取参数
    if args.config:
        if not os.path.exists(args.config):
            print(f"错误：配置文件 {args.config} 不存在")
            sys.exit(1)
        
        with open(args.config, 'r') as f:
            config_params = json.load(f)
        
        # 只有显式提供的命令行参数才覆盖配置文件参数
        # 获取解析器的默认值
        parser_defaults = {}
        for action in parser._actions:
            if action.dest != 'help':
                parser_defaults[action.dest] = action.default
        
        # 只覆盖非默认值的命令行参数
        for key, value in vars(args).items():
            if (value is not None and 
                key not in ['config', 'output_dir', 'aie_all_conformers'] and
                value != parser_defaults.get(key)):
                config_params[key.replace('_', '_')] = value
        
        # 从配置文件创建参数，添加自定义参数
        # 提取基本参数，避免重复传递
        smiles = config_params.pop('smiles', None)
        charge = config_params.pop('charge', 0)
        mult = config_params.pop('mult', 1)
        
        # 添加自定义参数
        config_params['aie_only_lowest'] = aie_only_lowest
        config_params['calc_aie_l3'] = args.calc_aie_l3 or config_params.get('L3_calc', 0)
        # 如果配置文件中有L3_calc=1，自动启用calc_aie_l3
        if config_params.get('L3_calc', 0):
            config_params['calc_aie_l3'] = True
        if args.l3_calc:
            config_params['L3_calc'] = 1
            config_params['calc_aie_l3'] = True
        
        kinbot_params, custom_params = create_input_file(
            smiles=smiles,
            charge=charge,
            mult=mult,
            **config_params
        )
    else:
        # 检查必需参数
        if not args.smiles:
            print("错误：必须提供SMILES字符串")
            parser.print_help()
            sys.exit(1)
        
        # 创建输入参数
        kinbot_params, custom_params = create_input_file(
            smiles=args.smiles,
            charge=args.charge,
            mult=args.mult,
            title=args.title,
            qc=args.qc,
            method=args.method,
            basis=args.basis,
            high_level_method=args.high_level_method,
            high_level_basis=args.high_level_basis,
            conf_grid=args.conf_grid,
            max_dihed=args.max_dihed,
            random_conf=args.random_conf,
            difference_threshold=args.difference_threshold,
            queuing=args.queuing,
            queue_name=args.queue_name,
            ppn=args.ppn,
            queue_template=args.queue_template,
            username=args.username,
            queue_job_limit=args.queue_job_limit,
            qc_command=args.qc_command,
            aie_only_lowest=aie_only_lowest,
            calc_aie_l3=args.calc_aie_l3,
            L3_calc=1 if args.l3_calc else 0,
            single_point_qc=args.single_point_qc,
            single_point_template=args.single_point_template,
            single_point_key=args.single_point_key,
            single_point_command=args.single_point_command,
            single_point_ppn=args.single_point_ppn,
            use_sella=args.use_sella,
            flat_ring_dih_angle=args.flat_ring_dih_angle,
            verbose=1 if args.verbose else 0
        )
    
    # 自动生成输出目录（基于化合物ID）
    if not args.output_dir:
        # 导入必要的模块
        from kinbot.stationary_pt import StationaryPoint
        
        # 创建临时StationaryPoint对象来生成化合物ID
        
        if kinbot_params.get('smiles'):
            temp_well = StationaryPoint('temp', kinbot_params['charge'], kinbot_params['mult'], 
                                      smiles=kinbot_params['smiles'])
        elif kinbot_params.get('structure'):
            temp_well = StationaryPoint('temp', kinbot_params['charge'], kinbot_params['mult'], 
                                      structure=kinbot_params['structure'])
        else:
            temp_well = StationaryPoint('temp', kinbot_params['charge'], kinbot_params['mult'])
        
        # 需要先表征分子结构才能计算chemid
        temp_well.characterize()
        temp_well.calc_chemid()
        
        # 使用化合物ID作为输出目录
        output_dir = str(temp_well.chemid)
        logger.info(f"自动生成输出目录: {output_dir}")
    else:
        output_dir = args.output_dir
    
    # 运行计算
    success = run_conformer_aie_calculation(kinbot_params, custom_params, output_dir)
    
    if success:
        print("\n计算成功完成！")
        print("结果文件:")
        print("- calculation_summary.json: 计算结果摘要")
        print("- initial_geometry.xyz: 初始几何结构")
        print("- conf/: 构象搜索结果")
        print("- aie/: 电离能计算结果")
        sys.exit(0)
    else:
        print("\n计算失败！请检查日志文件获取详细信息。")
        sys.exit(1)


if __name__ == "__main__":
    main()