#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试化合物查询对话框的修改
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from PyQt6.QtWidgets import QApplication
from ms_modules.compound_dialog import CompoundDialog

def test_compound_dialog():
    """测试化合物查询对话框"""
    app = QApplication(sys.argv)
    
    # 创建对话框
    dialog = CompoundDialog("C2H6O")  # 乙醇的分子式
    
    # 显示对话框
    dialog.show()
    
    print("化合物查询对话框已启动")
    print("请检查以下修改：")
    print("1. 底部只有'关闭'按钮")
    print("2. PSI4电离能计算标签页中有'复制化合物信息'和'清除所有电离能标记'按钮")
    print("3. 如果进行PSI4计算并保存到数据库，备注字段应该显示正确的方法/基组格式")
    
    # 运行应用
    sys.exit(app.exec())

if __name__ == "__main__":
    test_compound_dialog()
