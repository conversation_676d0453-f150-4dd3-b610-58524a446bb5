"""
项目管理器模块
用于保存和加载项目，包括质量校准参数、峰设置、文件路径和积分结果等
"""

import os
import json
import pickle
import numpy as np
import datetime
from PyQt6.QtWidgets import QFileDialog, QMessageBox

class ProjectManager:
    """项目管理器类，用于保存和加载项目"""

    def __init__(self, main_window):
        """初始化

        参数:
            main_window: 主窗口实例
        """
        self.main_window = main_window
        self.current_project_path = None

    def save_project(self, path=None):
        """保存项目

        参数:
            path: 项目文件路径，如果为None则弹出保存对话框

        返回:
            保存是否成功
        """
        try:
            # 如果没有提供路径，弹出保存对话框
            if path is None:
                path, _ = QFileDialog.getSaveFileName(
                    self.main_window,
                    "保存项目",
                    "",
                    "质谱项目文件 (*.msproj);;所有文件 (*)"
                )

                # 用户取消
                if not path:
                    return False

                # 确保文件扩展名正确
                if not path.endswith(".msproj"):
                    path += ".msproj"

            # 创建项目数据字典
            project_data = {
                # 版本信息，用于兼容性检查
                "version": "1.0.0",
                "created_at": datetime.datetime.now().isoformat(),

                # 收集设置信息
                "settings": self.main_window.settings_widget.getSettings(),

                # 收集峰信息
                "peaks": self.main_window.peak_editor.getPeaks(),

                # 收集校准参数
                "calibration_params": self.main_window.peak_editor.getCalibrationParams(),

                # 收集同位素校正状态
                "isotope_correction_applied": hasattr(self.main_window, 'isotope_correction_applied') and self.main_window.isotope_correction_applied,

                # 收集批处理结果的元数据
                "batch_results_meta": {}
            }

            # 创建数据目录（如果不存在）
            project_dir = os.path.splitext(path)[0] + "_data"
            os.makedirs(project_dir, exist_ok=True)

            # 收集批处理结果的元数据并保存结果数据到单独的文件
            for data_dir, results_data in self.main_window.batch_results.items():
                dir_id = hash(data_dir) % 10000  # 使用目录路径的哈希作为标识符

                # 添加元数据
                project_data["batch_results_meta"][data_dir] = {
                    "dir_id": dir_id,
                    "file_names": results_data["file_names"],
                    "peaks": results_data["peaks"],
                    "isotope_info": results_data["isotope_info"],
                    "file_metadata": results_data["file_metadata"]
                }

                # 保存numpy数组和原始数据到单独的文件
                np.save(os.path.join(project_dir, f"results_{dir_id}.npy"), results_data["results"])

                # 原始数据可能很大，保存为pickle文件
                with open(os.path.join(project_dir, f"raw_data_{dir_id}.pkl"), 'wb') as f:
                    pickle.dump(results_data["raw_data"], f)

            # 保存项目元数据
            with open(path, 'w', encoding='utf-8') as f:
                json.dump(project_data, f, ensure_ascii=False, indent=2)

            # 记录当前项目路径
            self.current_project_path = path

            print(f"项目已保存到 {path}")
            return True

        except Exception as e:
            print(f"保存项目时出错: {str(e)}")
            import traceback
            traceback.print_exc()

            QMessageBox.critical(
                self.main_window,
                "保存错误",
                f"保存项目时出错：\n{str(e)}"
            )
            return False

    def load_project(self, path=None):
        """加载项目

        参数:
            path: 项目文件路径，如果为None则弹出打开对话框

        返回:
            加载是否成功
        """
        try:
            # 如果没有提供路径，弹出打开对话框
            if path is None:
                path, _ = QFileDialog.getOpenFileName(
                    self.main_window,
                    "打开项目",
                    "",
                    "质谱项目文件 (*.msproj);;所有文件 (*)"
                )

                # 用户取消
                if not path:
                    return False

            # 加载项目元数据
            with open(path, 'r', encoding='utf-8') as f:
                project_data = json.load(f)

            # 检查版本兼容性
            version = project_data.get("version", "0.0.0")
            if version != "1.0.0":
                # 这里可以添加版本兼容性处理逻辑
                print(f"警告：项目版本 {version} 可能与当前版本不兼容")

            # 应用设置
            settings = project_data.get("settings", {})
            # 确保光强校正默认开启
            if 'photon_intensity_correction' not in settings:
                settings['photon_intensity_correction'] = True
            self.main_window.settings_widget.setSettings(settings)

            # 应用峰信息
            peaks = project_data.get("peaks", [])
            self.main_window.peak_editor.setPeaks(peaks)

            # 应用校准参数
            calibration_params = project_data.get("calibration_params", {})
            self.main_window.peak_editor.setCalibrationParams(calibration_params)

            # 加载同位素校正状态
            isotope_correction_applied = project_data.get("isotope_correction_applied", False)
            self.main_window.isotope_correction_applied = isotope_correction_applied

            # 更新同位素校正状态指示器
            if hasattr(self.main_window, 'isotope_indicator'):
                if isotope_correction_applied:
                    self.main_window.isotope_indicator.setText("同位素校正: 已应用")
                    self.main_window.isotope_indicator.setStyleSheet("color: green; font-weight: bold;")
                else:
                    self.main_window.isotope_indicator.setText("同位素校正: 未应用")
                    self.main_window.isotope_indicator.setStyleSheet("color: gray;")

            # 加载批处理结果
            self.main_window.batch_results = {}
            project_dir = os.path.splitext(path)[0] + "_data"

            for data_dir, meta in project_data.get("batch_results_meta", {}).items():
                dir_id = meta["dir_id"]

                try:
                    # 加载结果数组
                    results_path = os.path.join(project_dir, f"results_{dir_id}.npy")
                    results = np.load(results_path)

                    # 加载原始数据
                    raw_data_path = os.path.join(project_dir, f"raw_data_{dir_id}.pkl")
                    with open(raw_data_path, 'rb') as f:
                        raw_data = pickle.load(f)

                    # 重建批处理结果
                    self.main_window.batch_results[data_dir] = {
                        "results": results,
                        "file_names": meta["file_names"],
                        "peaks": meta["peaks"],
                        "isotope_info": meta["isotope_info"],
                        "raw_data": raw_data,
                        "file_metadata": meta["file_metadata"]
                    }

                except Exception as e:
                    print(f"加载目录 {data_dir} 的结果时出错: {str(e)}")

            # 更新结果显示
            if self.main_window.batch_results:
                self.main_window.results_widget.updateBatchResults(
                    self.main_window.batch_results,
                    peaks
                )

            # 记录当前项目路径
            self.current_project_path = path

            print(f"项目已从 {path} 加载")
            return True

        except Exception as e:
            print(f"加载项目时出错: {str(e)}")
            import traceback
            traceback.print_exc()

            QMessageBox.critical(
                self.main_window,
                "加载错误",
                f"加载项目时出错：\n{str(e)}"
            )
            return False

    def check_save_before_close(self):
        """在关闭前检查是否需要保存

        返回:
            True: 可以关闭
            False: 取消关闭
        """
        # 检查是否有未保存的更改
        if self.main_window.batch_results:
            reply = QMessageBox.question(
                self.main_window,
                "保存项目",
                "是否保存当前项目？",
                QMessageBox.StandardButton.Save |
                QMessageBox.StandardButton.Discard |
                QMessageBox.StandardButton.Cancel,
                QMessageBox.StandardButton.Save
            )

            if reply == QMessageBox.StandardButton.Save:
                return self.save_project(self.current_project_path)
            elif reply == QMessageBox.StandardButton.Cancel:
                return False

        return True