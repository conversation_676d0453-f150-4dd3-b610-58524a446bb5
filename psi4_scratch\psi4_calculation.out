
  Memory set to   7.451 GiB by Python driver.
  Threads set to 10 by Python driver.

Scratch directory: D:\BaiduSyncdisk\py\MS\psi4_scratch/
gradient() will perform analytic gradient computation.
   => Libint2 <=

    Primary   basis highest AM E, G, H:  6, 6, -
    Auxiliary basis highest AM E, G, H:  7, 7, -
    Onebody   basis highest AM E, G, H:  -, -, -
    Solid Harmonics ordering:            Gaussian

*** tstart() called on iCarus
*** at Sun Jun 29 21:43:30 2025

   => Loading Basis Set <=

    Name: 6-31G(D)
    Role: ORBITAL
    Keyword: BASIS
    atoms 1   entry C          line   111 file D:\anaconda3\envs\nistpy\Library\share\psi4\basis\6-31g_d_.gbs 
    atoms 2-5 entry H          line    44 file D:\anaconda3\envs\nistpy\Library\share\psi4\basis\6-31g_d_.gbs 


         ---------------------------------------------------------
                                   SCF
               by <PERSON>, <PERSON>, <PERSON>
                          and <PERSON>
                              RKS Reference
                       10 Threads,   -562 MiB Core
         ---------------------------------------------------------

  ==> Geometry <==

    Molecular point group: c1
    Full point group: C1

    Geometry (in Angstrom), charge = 0, multiplicity = 1:

       Center              X                  Y                   Z               Mass       
    ------------   -----------------  -----------------  -----------------  -----------------
         C            0.000000000000     0.000000000000     0.000000000000    12.000000000000
         H           -0.675343000000     0.854123000000    -0.085361000000     1.007825032230
         H           -0.394946000000    -0.835938000000    -0.581485000000     1.007825032230
         H            0.084754000000    -0.293741000000     1.048538000000     1.007825032230
         H            0.985535000000     0.275556000000    -0.381692000000     1.007825032230

  Running in c1 symmetry.

  Rotational constants: A =      5.25822  B =      5.25822  C =      5.25822 [cm^-1]
  Rotational constants: A = 157637.54748  B = 157637.44145  C = 157637.40228 [MHz]
  Nuclear repulsion =   13.408333629033770

  Charge       = 0
  Multiplicity = 1
  Electrons    = 10
  Nalpha       = 5
  Nbeta        = 5

  ==> Algorithm <==

  SCF Algorithm Type is DF.
  DIIS enabled.
  MOM disabled.
  Fractional occupation disabled.
  Guess Type is SAD.
  Energy threshold   = 1.00e-08
  Density threshold  = 1.00e-08
  Integral threshold = 1.00e-12

  ==> Primary Basis <==

  Basis Set: 6-31G(D)
    Blend: 6-31G(D)
    Number of shells: 14
    Number of basis functions: 23
    Number of Cartesian functions: 23
    Spherical Harmonics?: false
    Max angular momentum: 2

  ==> DFT Potential <==

   => LibXC <=

    Version 7.0.0
    S. Lehtola, C. Steigemann, M. J.T. Oliveira, and M. A.L. Marques.,  SoftwareX 7, 1–5 (2018) (10.1016/j.softx.2017.11.002)

   => Composite Functional: B3LYP <= 

    B3LYP Hyb-GGA Exchange-Correlation Functional

    P. J. Stephens, F. J. Devlin, C. F. Chabalowski, and M. J. Frisch.,  J. Phys. Chem. 98, 11623 (1994) (10.1021/j100096a001)

    Deriv               =              1
    GGA                 =           TRUE
    Meta                =          FALSE

    Exchange Hybrid     =           TRUE
    MP2 Hybrid          =          FALSE

   => Exchange Functionals <=

    0.0800   Slater exchange
    0.7200         Becke 88

   => Exact (HF) Exchange <=

    0.2000               HF 

   => Correlation Functionals <=

    0.1900   Vosko, Wilk & Nusair (VWN5_RPA)
    0.8100   Lee, Yang & Parr

   => LibXC Density Thresholds  <==

    XC_HYB_GGA_XC_B3LYP:  1.00E-15 

   => Molecular Quadrature <=

    Radial Scheme          =       TREUTLER
    Pruning Scheme         =           NONE
    Nuclear Scheme         =       TREUTLER

    Blocking Scheme        =         OCTREE
    BS radius alpha        =              1
    Pruning alpha          =              1
    Radial Points          =             75
    Spherical Points       =            302
    Total Points           =         109203
    Total Blocks           =            878
    Max Points             =            255
    Max Functions          =             23
    Weights Tolerance      =       1.00E-15

   => Loading Basis Set <=

    Name: (6-31G(D) AUX)
    Role: JKFIT
    Keyword: DF_BASIS_SCF
    atoms 1   entry C          line   121 file D:\anaconda3\envs\nistpy\Library\share\psi4\basis\cc-pvdz-jkfit.gbs 
    atoms 2-5 entry H          line    51 file D:\anaconda3\envs\nistpy\Library\share\psi4\basis\cc-pvdz-jkfit.gbs 

  ==> Integral Setup <==

  DFHelper Memory: AOs need 0.001 GiB; user supplied 5.515 GiB. 
  Using in-core AOs.

  ==> MemDFJK: Density-Fitted J/K Matrices <==

    J tasked:                   Yes
    K tasked:                   Yes
    wK tasked:                   No
    OpenMP threads:              10
    Memory [MiB]:              5647
    Algorithm:                 Core
    Schwarz Cutoff:           1E-12
    Mask sparsity (%):       0.0000
    Fitting Condition:        1E-10

   => Auxiliary Basis Set <=

  Basis Set: (6-31G(D) AUX)
    Blend: CC-PVDZ-JKFIT
    Number of shells: 60
    Number of basis functions: 181
    Number of Cartesian functions: 181
    Spherical Harmonics?: false
    Max angular momentum: 3

  Cached 100.0% of DFT collocation blocks in 0.073 [GiB].

  Minimum eigenvalue in the overlap matrix is 1.7897030891E-02.
  Reciprocal condition number of the overlap matrix is 3.1910812166E-03.
    Using symmetric orthogonalization.

  ==> Pre-Iterations <==

  SCF Guess: Superposition of Atomic Densities via on-the-fly atomic UHF (no occupation information).

   -------------------------
    Irrep   Nso     Nmo    
   -------------------------
     A         23      23 
   -------------------------
    Total      23      23
   -------------------------

  ==> Iterations <==

                           Total Energy        Delta E     RMS |[F,P]|

   @DF-RKS iter SAD:   -40.09362093565436   -4.00936e+01   0.00000e+00 
   @DF-RKS iter   1:   -40.35147126380739   -2.57850e-01   2.49153e-02 ADIIS/DIIS
   @DF-RKS iter   2:   -40.31077246903417    4.06988e-02   2.63750e-02 ADIIS/DIIS
   @DF-RKS iter   3:   -40.51838948507617   -2.07617e-01   2.47275e-04 ADIIS/DIIS
   @DF-RKS iter   4:   -40.51840391582202   -1.44307e-05   6.00555e-05 DIIS
   @DF-RKS iter   5:   -40.51840487628670   -9.60465e-07   3.41993e-06 DIIS
   @DF-RKS iter   6:   -40.51840487925724   -2.97054e-09   9.03350e-08 DIIS
   @DF-RKS iter   7:   -40.51840487925971   -2.47269e-12   2.63329e-09 DIIS
  Energy and wave function converged.


  ==> Post-Iterations <==

   Electrons on quadrature grid:
      Ntotal   =   10.0000048809 ; deviation = 4.881e-06 

    Orbital Energies [Eh]
    ---------------------

    Doubly Occupied:                                                      

       1A    -10.166480     2A     -0.690613     3A     -0.389244  
       4A     -0.389243     5A     -0.389243  

    Virtual:                                                              

       6A      0.118182     7A      0.176898     8A      0.176898  
       9A      0.176899    10A      0.532897    11A      0.532898  
      12A      0.532899    13A      0.898636    14A      0.898636  
      15A      0.898636    16A      0.949080    17A      1.100191  
      18A      1.656826    19A      1.656827    20A      2.244882  
      21A      2.244883    22A      2.244884    23A      4.179964  

    Final Occupation by Irrep:
              A 
    DOCC [     5 ]
    NA   [     5 ]
    NB   [     5 ]

  @DF-RKS Final Energy:   -40.51840487925971

   => Energetics <=

    Nuclear Repulsion Energy =             13.4083336290337698
    One-Electron Energy =                 -79.7835434868684672
    Two-Electron Energy =                  31.4448883221927247
    DFT Exchange-Correlation Energy =      -5.5880833436177308
    Empirical Dispersion Energy =           0.0000000000000000
    VV10 Nonlocal Energy =                  0.0000000000000000
    Total Energy =                        -40.5184048792597125

Computation Completed


Properties will be evaluated at   0.000000,   0.000000,   0.000000 [a0]

Properties computed using the SCF density matrix


 Multipole Moments:

 ------------------------------------------------------------------------------------
     Multipole            Electronic (a.u.)      Nuclear  (a.u.)        Total (a.u.)
 ------------------------------------------------------------------------------------

 L = 1.  Multiply by 2.5417464519 to convert [e a0] to [Debye]
 Dipole X            :         -0.0000011            0.0000000           -0.0000011
 Dipole Y            :          0.0000002            0.0000000            0.0000002
 Dipole Z            :          0.0000005            0.0000000            0.0000005
 Magnitude           :                                                    0.0000012

 ------------------------------------------------------------------------------------

*** tstop() called on iCarus at Sun Jun 29 21:43:30 2025
Module time:
	user time   =        nan seconds =        nan minutes
	system time =        nan seconds =        nan minutes
	total time  =          0 seconds =       0.00 minutes
Total time:
	user time   =        nan seconds =        nan minutes
	system time =        nan seconds =        nan minutes
	total time  =          0 seconds =       0.00 minutes

*** tstart() called on iCarus
*** at Sun Jun 29 21:43:30 2025


         ------------------------------------------------------------
                                   SCF GRAD                          
                          Rob Parrish, Justin Turney,                
                       Andy Simmonett, and Alex Sokolov              
         ------------------------------------------------------------

  ==> Geometry <==

    Molecular point group: c1
    Full point group: C1

    Geometry (in Angstrom), charge = 0, multiplicity = 1:

       Center              X                  Y                   Z               Mass       
    ------------   -----------------  -----------------  -----------------  -----------------
         C            0.000000000000     0.000000000000     0.000000000000    12.000000000000
         H           -0.675343000000     0.854123000000    -0.085361000000     1.007825032230
         H           -0.394946000000    -0.835938000000    -0.581485000000     1.007825032230
         H            0.084754000000    -0.293741000000     1.048538000000     1.007825032230
         H            0.985535000000     0.275556000000    -0.381692000000     1.007825032230

  Nuclear repulsion =   13.408333629033770

  ==> Basis Set <==

  Basis Set: 6-31G(D)
    Blend: 6-31G(D)
    Number of shells: 14
    Number of basis functions: 23
    Number of Cartesian functions: 23
    Spherical Harmonics?: false
    Max angular momentum: 2

  ==> DFJKGrad: Density-Fitted SCF Gradients <==

    Gradient:                    1
    J tasked:                  Yes
    K tasked:                  Yes
    wK tasked:                  No
    OpenMP threads:             10
    Integrals threads:          10
    Memory [MiB]:             -422
    Schwarz Cutoff:          1E-12
    Fitting Condition:       1E-10

   => Auxiliary Basis Set <=

  Basis Set: (6-31G(D) AUX)
    Blend: CC-PVDZ-JKFIT
    Number of shells: 60
    Number of basis functions: 181
    Number of Cartesian functions: 181
    Spherical Harmonics?: false
    Max angular momentum: 3

  ==> DFT Potential <==

   => LibXC <=

    Version 7.0.0
    S. Lehtola, C. Steigemann, M. J.T. Oliveira, and M. A.L. Marques.,  SoftwareX 7, 1–5 (2018) (10.1016/j.softx.2017.11.002)

   => Composite Functional: B3LYP <= 

    B3LYP Hyb-GGA Exchange-Correlation Functional

    P. J. Stephens, F. J. Devlin, C. F. Chabalowski, and M. J. Frisch.,  J. Phys. Chem. 98, 11623 (1994) (10.1021/j100096a001)

    Deriv               =              1
    GGA                 =           TRUE
    Meta                =          FALSE

    Exchange Hybrid     =           TRUE
    MP2 Hybrid          =          FALSE

   => Exchange Functionals <=

    0.0800   Slater exchange
    0.7200         Becke 88

   => Exact (HF) Exchange <=

    0.2000               HF 

   => Correlation Functionals <=

    0.1900   Vosko, Wilk & Nusair (VWN5_RPA)
    0.8100   Lee, Yang & Parr

   => LibXC Density Thresholds  <==

    XC_HYB_GGA_XC_B3LYP:  1.00E-15 

   => Molecular Quadrature <=

    Radial Scheme          =       TREUTLER
    Pruning Scheme         =           NONE
    Nuclear Scheme         =       TREUTLER

    Blocking Scheme        =         OCTREE
    BS radius alpha        =              1
    Pruning alpha          =              1
    Radial Points          =             75
    Spherical Points       =            302
    Total Points           =         109203
    Total Blocks           =            878
    Max Points             =            255
    Max Functions          =             23
    Weights Tolerance      =       1.00E-15


  -Total Gradient:
     Atom            X                  Y                   Z
    ------   -----------------  -----------------  -----------------
       1       -0.000000954964     0.000000204670     0.000000630357
       2        0.000441385434    -0.000558500085     0.000055599770
       3        0.000257760229     0.000546140211     0.000380391486
       4       -0.000054434493     0.000193299589    -0.000686471085
       5       -0.000644793868    -0.000179996104     0.000249137321


*** tstop() called on iCarus at Sun Jun 29 21:43:30 2025
Module time:
	user time   =        nan seconds =        nan minutes
	system time =        nan seconds =        nan minutes
	total time  =          0 seconds =       0.00 minutes
Total time:
	user time   =        nan seconds =        nan minutes
	system time =        nan seconds =        nan minutes
	total time  =          0 seconds =       0.00 minutes

    			-----------------------------------------

    			 OPTKING 3.0: for geometry optimizations 

    			     By R.A. King, Bethel University     

    			        with contributions from          

    			    A.V. Copan, J. Cayton, A. Heide      

    			-----------------------------------------

    
	                            ===> Fragment 1 <== 

	 Z (Atomic Numbers)          Masses                          Geom                  
	       6.000000            12.000000          0.00000000  0.00000000  0.00000000   
	       1.000000             1.007825         -1.27621331  1.61405855 -0.16130891   
	       1.000000             1.007825         -0.74633977 -1.57969388 -1.09884740   
	       1.000000             1.007825          0.16016185 -0.55509004  1.98144965   
	       1.000000             1.007825          1.86239124  0.52072537 -0.72129334   

	 - Coordinate -           - BOHR/RAD -       - ANG/DEG -
	 R(1,2)           =         2.063959           1.092200
	 R(1,3)           =         2.063958           1.092199
	 R(1,4)           =         2.063957           1.092199
	 R(1,5)           =         2.063957           1.092199
	 B(2,1,3)         =         1.910634         109.471238
	 B(2,1,4)         =         1.910633         109.471217
	 B(2,1,5)         =         1.910634         109.471247
	 B(3,1,4)         =         1.910633         109.471222
	 B(3,1,5)         =         1.910633         109.471198
	 B(4,1,5)         =         1.910633         109.471201
	 O(2,1,3,4)       =         0.955316          54.735596
	 O(2,1,3,5)       =        -0.955316         -54.735578
	 O(2,1,4,5)       =         0.955316          54.735597
	 O(3,1,2,4)       =        -0.955316         -54.735593
	 O(3,1,2,5)       =         0.955316          54.735602
	 O(3,1,4,5)       =        -0.955317         -54.735641
	 O(4,1,2,3)       =         0.955316          54.735603
	 O(4,1,2,5)       =        -0.955317         -54.735620
	 O(4,1,3,5)       =         0.955317          54.735639
	 O(5,1,2,3)       =        -0.955316         -54.735598
	 O(5,1,2,4)       =         0.955317          54.735605
	 O(5,1,3,4)       =        -0.955317         -54.735651


	        --- Internal Coordinate Step in ANG or DEG, aJ/ANG or AJ/DEG ---
	-------------------------------------------------------------------------------
	           Coordinate      Previous         Force          Change          New 
	           ----------      --------        ------          ------        ------
	               R(1,2)       1.09220        0.00589        0.00109       1.09329
	               R(1,3)       1.09220        0.00588        0.00109       1.09329
	               R(1,4)       1.09220        0.00589        0.00109       1.09329
	               R(1,5)       1.09220        0.00588        0.00109       1.09329
	             B(2,1,3)     109.47124        0.00000        0.00000     109.47124
	             B(2,1,4)     109.47122       -0.00000       -0.00006     109.47116
	             B(2,1,5)     109.47125       -0.00000       -0.00002     109.47123
	             B(3,1,4)     109.47122       -0.00000       -0.00010     109.47112
	             B(3,1,5)     109.47120       -0.00000       -0.00004     109.47116
	             B(4,1,5)     109.47120        0.00000        0.00021     109.47141
	           O(2,1,3,4)      54.73560        0.00000        0.00010      54.73570
	           O(2,1,3,5)     -54.73558       -0.00000       -0.00003     -54.73561
	           O(2,1,4,5)      54.73560        0.00000       -0.00003      54.73557
	           O(3,1,2,4)     -54.73559       -0.00000       -0.00013     -54.73572
	           O(3,1,2,5)      54.73560        0.00000        0.00004      54.73564
	           O(3,1,4,5)     -54.73564       -0.00000       -0.00004     -54.73568
	           O(4,1,2,3)      54.73560        0.00000        0.00016      54.73576
	           O(4,1,2,5)     -54.73562        0.00000        0.00014     -54.73548
	           O(4,1,3,5)      54.73564       -0.00000       -0.00009      54.73555
	           O(5,1,2,3)     -54.73560       -0.00000       -0.00005     -54.73565
	           O(5,1,2,4)      54.73561       -0.00000       -0.00016      54.73544
	           O(5,1,3,4)     -54.73565        0.00000        0.00012     -54.73553
	-------------------------------------------------------------------------------

	                                 ==> Convergence Check <==                                  
    
	Measures of convergence in internal coordinates in au.
    
	Criteria marked as inactive (o), active & met (*), and active & unmet ( ).

	----------------------------------------------------------------------------------------------~
	   Step    Total Energy     Delta E     Max Force     RMS Force      Max Disp      RMS Disp   ~
	----------------------------------------------------------------------------------------------~
	  Convergence Criteria     1.00e-06 *    3.00e-04 *             o    1.20e-03 *             o~
	----------------------------------------------------------------------------------------------~
	     1     -40.51840488   -4.05e+01      7.15e-04      3.05e-04 o    2.07e-03      8.81e-04 o  ~
	----------------------------------------------------------------------------------------------

Next Geometry in Ang 
	Fragment 1 (Ang)

	    C   0.0000005796   0.0000001226  -0.0000001355
	    H  -0.6760190081   0.8549783037  -0.0854463846
	    H  -0.3953406851  -0.8367745554  -0.5820664696
	    H   0.0848373938  -0.2940360390   1.0495888969
	    H   0.9865217198   0.2758321681  -0.3820759072


    Structure for next step:
    Molecular point group: c1
    Full point group: C1

    Geometry (in Angstrom), charge = 0, multiplicity = 1:

    C            0.000000579599     0.000000122629    -0.000000135519
    H           -0.676019008148     0.854978303651    -0.085446384613
    H           -0.395340685054    -0.836774555424    -0.582066469605
    H            0.084837393849    -0.294036038958     1.049588896912
    H            0.986521719754     0.275832168102    -0.382075907175


Scratch directory: D:\BaiduSyncdisk\py\MS\psi4_scratch/
gradient() will perform analytic gradient computation.
   => Libint2 <=

    Primary   basis highest AM E, G, H:  6, 6, -
    Auxiliary basis highest AM E, G, H:  7, 7, -
    Onebody   basis highest AM E, G, H:  -, -, -
    Solid Harmonics ordering:            Gaussian

*** tstart() called on iCarus
*** at Sun Jun 29 21:43:31 2025

   => Loading Basis Set <=

    Name: 6-31G(D)
    Role: ORBITAL
    Keyword: BASIS
    atoms 1   entry C          line   111 file D:\anaconda3\envs\nistpy\Library\share\psi4\basis\6-31g_d_.gbs 
    atoms 2-5 entry H          line    44 file D:\anaconda3\envs\nistpy\Library\share\psi4\basis\6-31g_d_.gbs 


         ---------------------------------------------------------
                                   SCF
               by Justin Turney, Rob Parrish, Andy Simmonett
                          and Daniel G. A. Smith
                              RKS Reference
                       10 Threads,   -562 MiB Core
         ---------------------------------------------------------

  ==> Geometry <==

    Molecular point group: c1
    Full point group: C1

    Geometry (in Angstrom), charge = 0, multiplicity = 1:

       Center              X                  Y                   Z               Mass       
    ------------   -----------------  -----------------  -----------------  -----------------
         C            0.000000579599     0.000000122629    -0.000000135519    12.000000000000
         H           -0.676019008148     0.854978303651    -0.085446384613     1.007825032230
         H           -0.395340685054    -0.836774555424    -0.582066469605     1.007825032230
         H            0.084837393849    -0.294036038958     1.049588896912     1.007825032230
         H            0.986521719754     0.275832168102    -0.382075907175     1.007825032230

  Running in c1 symmetry.

  Rotational constants: A =      5.24771  B =      5.24771  C =      5.24770 [cm^-1]
  Rotational constants: A = 157322.42722  B = 157322.26094  C = 157321.95423 [MHz]
  Nuclear repulsion =   13.394919662523526

  Charge       = 0
  Multiplicity = 1
  Electrons    = 10
  Nalpha       = 5
  Nbeta        = 5

  ==> Algorithm <==

  SCF Algorithm Type is DF.
  DIIS enabled.
  MOM disabled.
  Fractional occupation disabled.
  Guess Type is READ.
  Energy threshold   = 1.00e-08
  Density threshold  = 1.00e-08
  Integral threshold = 1.00e-12

  ==> Primary Basis <==

  Basis Set: 6-31G(D)
    Blend: 6-31G(D)
    Number of shells: 14
    Number of basis functions: 23
    Number of Cartesian functions: 23
    Spherical Harmonics?: false
    Max angular momentum: 2

  ==> DFT Potential <==

   => LibXC <=

    Version 7.0.0
    S. Lehtola, C. Steigemann, M. J.T. Oliveira, and M. A.L. Marques.,  SoftwareX 7, 1–5 (2018) (10.1016/j.softx.2017.11.002)

   => Composite Functional: B3LYP <= 

    B3LYP Hyb-GGA Exchange-Correlation Functional

    P. J. Stephens, F. J. Devlin, C. F. Chabalowski, and M. J. Frisch.,  J. Phys. Chem. 98, 11623 (1994) (10.1021/j100096a001)

    Deriv               =              1
    GGA                 =           TRUE
    Meta                =          FALSE

    Exchange Hybrid     =           TRUE
    MP2 Hybrid          =          FALSE

   => Exchange Functionals <=

    0.0800   Slater exchange
    0.7200         Becke 88

   => Exact (HF) Exchange <=

    0.2000               HF 

   => Correlation Functionals <=

    0.1900   Vosko, Wilk & Nusair (VWN5_RPA)
    0.8100   Lee, Yang & Parr

   => LibXC Density Thresholds  <==

    XC_HYB_GGA_XC_B3LYP:  1.00E-15 

   => Molecular Quadrature <=

    Radial Scheme          =       TREUTLER
    Pruning Scheme         =           NONE
    Nuclear Scheme         =       TREUTLER

    Blocking Scheme        =         OCTREE
    BS radius alpha        =              1
    Pruning alpha          =              1
    Radial Points          =             75
    Spherical Points       =            302
    Total Points           =         109195
    Total Blocks           =            882
    Max Points             =            256
    Max Functions          =             23
    Weights Tolerance      =       1.00E-15

   => Loading Basis Set <=

    Name: (6-31G(D) AUX)
    Role: JKFIT
    Keyword: DF_BASIS_SCF
    atoms 1   entry C          line   121 file D:\anaconda3\envs\nistpy\Library\share\psi4\basis\cc-pvdz-jkfit.gbs 
    atoms 2-5 entry H          line    51 file D:\anaconda3\envs\nistpy\Library\share\psi4\basis\cc-pvdz-jkfit.gbs 

   => Loading Basis Set <=

    Name: 6-31G(D)
    Role: ORBITAL
    Keyword: BASIS
    atoms 1   entry C          line   111 file D:\anaconda3\envs\nistpy\Library\share\psi4\basis\6-31g_d_.gbs 
    atoms 2-5 entry H          line    44 file D:\anaconda3\envs\nistpy\Library\share\psi4\basis\6-31g_d_.gbs 

  Reading orbitals from file D:\BaiduSyncdisk\py\MS\psi4_scratch/psi4_calculation.default.34212.180.npy, no projection.

  ==> Integral Setup <==

  DFHelper Memory: AOs need 0.001 GiB; user supplied 5.515 GiB. 
  Using in-core AOs.

  ==> MemDFJK: Density-Fitted J/K Matrices <==

    J tasked:                   Yes
    K tasked:                   Yes
    wK tasked:                   No
    OpenMP threads:              10
    Memory [MiB]:              5647
    Algorithm:                 Core
    Schwarz Cutoff:           1E-12
    Mask sparsity (%):       0.0000
    Fitting Condition:        1E-10

   => Auxiliary Basis Set <=

  Basis Set: (6-31G(D) AUX)
    Blend: CC-PVDZ-JKFIT
    Number of shells: 60
    Number of basis functions: 181
    Number of Cartesian functions: 181
    Spherical Harmonics?: false
    Max angular momentum: 3

  Cached 100.0% of DFT collocation blocks in 0.073 [GiB].

  Minimum eigenvalue in the overlap matrix is 1.7960254000E-02.
  Reciprocal condition number of the overlap matrix is 3.2047277001E-03.
    Using symmetric orthogonalization.

  ==> Pre-Iterations <==

  SCF Guess: Orbitals guess was supplied from a previous computation.

   -------------------------------------------------------
    Irrep   Nso     Nmo     Nalpha   Nbeta   Ndocc  Nsocc
   -------------------------------------------------------
     A         23      23       5       5       5       0
   -------------------------------------------------------
    Total      23      23       5       5       5       0
   -------------------------------------------------------

  ==> Iterations <==

                           Total Energy        Delta E     RMS |[F,P]|

   @DF-RKS iter   0:   -40.51729045145341   -4.05173e+01   7.30947e-05 
   @DF-RKS iter   1:   -40.51840706152847   -1.11661e-03   7.36676e-06 DIIS
   @DF-RKS iter   2:   -40.51840707630843   -1.47800e-08   4.76415e-06 DIIS
   @DF-RKS iter   3:   -40.51840708150836   -5.19993e-09   2.04586e-06 DIIS
   @DF-RKS iter   4:   -40.51840708259675   -1.08839e-09   5.45552e-08 DIIS
   @DF-RKS iter   5:   -40.51840708259748   -7.31859e-13   5.54142e-10 DIIS
  Energy and wave function converged.


  ==> Post-Iterations <==

   Electrons on quadrature grid:
      Ntotal   =   10.0000006238 ; deviation = 6.238e-07 

    Orbital Energies [Eh]
    ---------------------

    Doubly Occupied:                                                      

       1A    -10.166938     2A     -0.690279     3A     -0.389061  
       4A     -0.389061     5A     -0.389060  

    Virtual:                                                              

       6A      0.117914     7A      0.176556     8A      0.176557  
       9A      0.176557    10A      0.533224    11A      0.533225  
      12A      0.533227    13A      0.897799    14A      0.897800  
      15A      0.897801    16A      0.947455    17A      1.099862  
      18A      1.657043    19A      1.657044    20A      2.243407  
      21A      2.243408    22A      2.243409    23A      4.179167  

    Final Occupation by Irrep:
              A 
    DOCC [     5 ]
    NA   [     5 ]
    NB   [     5 ]

  @DF-RKS Final Energy:   -40.51840708259748

   => Energetics <=

    Nuclear Repulsion Energy =             13.3949196625235256
    One-Electron Energy =                 -79.7584354306665091
    Two-Electron Energy =                  31.4319451703108967
    DFT Exchange-Correlation Energy =      -5.5868364847653949
    Empirical Dispersion Energy =           0.0000000000000000
    VV10 Nonlocal Energy =                  0.0000000000000000
    Total Energy =                        -40.5184070825974842

Computation Completed


Properties will be evaluated at   0.000000,   0.000000,   0.000000 [a0]

Properties computed using the SCF density matrix


 Multipole Moments:

 ------------------------------------------------------------------------------------
     Multipole            Electronic (a.u.)      Nuclear  (a.u.)        Total (a.u.)
 ------------------------------------------------------------------------------------

 L = 1.  Multiply by 2.5417464519 to convert [e a0] to [Debye]
 Dipole X            :         -0.0000070            0.0000055           -0.0000015
 Dipole Y            :         -0.0000020            0.0000012           -0.0000008
 Dipole Z            :          0.0000022           -0.0000013            0.0000009
 Magnitude           :                                                    0.0000019

 ------------------------------------------------------------------------------------

*** tstop() called on iCarus at Sun Jun 29 21:43:31 2025
Module time:
	user time   =        nan seconds =        nan minutes
	system time =        nan seconds =        nan minutes
	total time  =          0 seconds =       0.00 minutes
Total time:
	user time   =        nan seconds =        nan minutes
	system time =        nan seconds =        nan minutes
	total time  =          1 seconds =       0.02 minutes

*** tstart() called on iCarus
*** at Sun Jun 29 21:43:31 2025


         ------------------------------------------------------------
                                   SCF GRAD                          
                          Rob Parrish, Justin Turney,                
                       Andy Simmonett, and Alex Sokolov              
         ------------------------------------------------------------

  ==> Geometry <==

    Molecular point group: c1
    Full point group: C1

    Geometry (in Angstrom), charge = 0, multiplicity = 1:

       Center              X                  Y                   Z               Mass       
    ------------   -----------------  -----------------  -----------------  -----------------
         C            0.000000579599     0.000000122629    -0.000000135519    12.000000000000
         H           -0.676019008148     0.854978303651    -0.085446384613     1.007825032230
         H           -0.395340685054    -0.836774555424    -0.582066469605     1.007825032230
         H            0.084837393849    -0.294036038958     1.049588896912     1.007825032230
         H            0.986521719754     0.275832168102    -0.382075907175     1.007825032230

  Nuclear repulsion =   13.394919662523526

  ==> Basis Set <==

  Basis Set: 6-31G(D)
    Blend: 6-31G(D)
    Number of shells: 14
    Number of basis functions: 23
    Number of Cartesian functions: 23
    Spherical Harmonics?: false
    Max angular momentum: 2

  ==> DFJKGrad: Density-Fitted SCF Gradients <==

    Gradient:                    1
    J tasked:                  Yes
    K tasked:                  Yes
    wK tasked:                  No
    OpenMP threads:             10
    Integrals threads:          10
    Memory [MiB]:             -422
    Schwarz Cutoff:          1E-12
    Fitting Condition:       1E-10

   => Auxiliary Basis Set <=

  Basis Set: (6-31G(D) AUX)
    Blend: CC-PVDZ-JKFIT
    Number of shells: 60
    Number of basis functions: 181
    Number of Cartesian functions: 181
    Spherical Harmonics?: false
    Max angular momentum: 3

  ==> DFT Potential <==

   => LibXC <=

    Version 7.0.0
    S. Lehtola, C. Steigemann, M. J.T. Oliveira, and M. A.L. Marques.,  SoftwareX 7, 1–5 (2018) (10.1016/j.softx.2017.11.002)

   => Composite Functional: B3LYP <= 

    B3LYP Hyb-GGA Exchange-Correlation Functional

    P. J. Stephens, F. J. Devlin, C. F. Chabalowski, and M. J. Frisch.,  J. Phys. Chem. 98, 11623 (1994) (10.1021/j100096a001)

    Deriv               =              1
    GGA                 =           TRUE
    Meta                =          FALSE

    Exchange Hybrid     =           TRUE
    MP2 Hybrid          =          FALSE

   => Exchange Functionals <=

    0.0800   Slater exchange
    0.7200         Becke 88

   => Exact (HF) Exchange <=

    0.2000               HF 

   => Correlation Functionals <=

    0.1900   Vosko, Wilk & Nusair (VWN5_RPA)
    0.8100   Lee, Yang & Parr

   => LibXC Density Thresholds  <==

    XC_HYB_GGA_XC_B3LYP:  1.00E-15 

   => Molecular Quadrature <=

    Radial Scheme          =       TREUTLER
    Pruning Scheme         =           NONE
    Nuclear Scheme         =       TREUTLER

    Blocking Scheme        =         OCTREE
    BS radius alpha        =              1
    Pruning alpha          =              1
    Radial Points          =             75
    Spherical Points       =            302
    Total Points           =         109195
    Total Blocks           =            882
    Max Points             =            256
    Max Functions          =             23
    Weights Tolerance      =       1.00E-15


  -Total Gradient:
     Atom            X                  Y                   Z
    ------   -----------------  -----------------  -----------------
       1        0.000000424134    -0.000000329911    -0.000001842693
       2       -0.000014431819     0.000018010813    -0.000002048601
       3       -0.000007486872    -0.000016960595    -0.000012307309
       4        0.000001972416    -0.000006156186     0.000022245371
       5        0.000019715039     0.000004886396    -0.000007786844


*** tstop() called on iCarus at Sun Jun 29 21:43:31 2025
Module time:
	user time   =        nan seconds =        nan minutes
	system time =        nan seconds =        nan minutes
	total time  =          0 seconds =       0.00 minutes
Total time:
	user time   =        nan seconds =        nan minutes
	system time =        nan seconds =        nan minutes
	total time  =          1 seconds =       0.02 minutes

    			-----------------------------------------

    			 OPTKING 3.0: for geometry optimizations 

    			     By R.A. King, Bethel University     

    			        with contributions from          

    			    A.V. Copan, J. Cayton, A. Heide      

    			-----------------------------------------

    
	                            ===> Fragment 1 <== 

	 Z (Atomic Numbers)          Masses                          Geom                  
	       6.000000            12.000000          0.00000110  0.00000023 -0.00000026   
	       1.000000             1.007825         -1.27749078  1.61567484 -0.16147027   
	       1.000000             1.007825         -0.74708562 -1.58127474 -1.09994621   
	       1.000000             1.007825          0.16031944 -0.55564758  1.98343556   
	       1.000000             1.007825          1.86425587  0.52124725 -0.72201882   

	 - Coordinate -           - BOHR/RAD -       - ANG/DEG -
	 R(1,2)           =         2.066026           1.093294
	 R(1,3)           =         2.066023           1.093292
	 R(1,4)           =         2.066026           1.093294
	 R(1,5)           =         2.066024           1.093293
	 B(2,1,3)         =         1.910634         109.471243
	 B(2,1,4)         =         1.910632         109.471160
	 B(2,1,5)         =         1.910633         109.471232
	 B(3,1,4)         =         1.910631         109.471119
	 B(3,1,5)         =         1.910632         109.471162
	 B(4,1,5)         =         1.910637         109.471408
	 O(2,1,3,4)       =         0.955318          54.735700
	 O(2,1,3,5)       =        -0.955317         -54.735606
	 O(2,1,4,5)       =         0.955316          54.735566
	 O(3,1,2,4)       =        -0.955319         -54.735720
	 O(3,1,2,5)       =         0.955317          54.735641
	 O(3,1,4,5)       =        -0.955318         -54.735677
	 O(4,1,2,3)       =         0.955319          54.735761
	 O(4,1,2,5)       =        -0.955314         -54.735478
	 O(4,1,3,5)       =         0.955316          54.735554
	 O(5,1,2,3)       =        -0.955317         -54.735647
	 O(5,1,2,4)       =         0.955314          54.735442
	 O(5,1,3,4)       =        -0.955315         -54.735532


	        --- Internal Coordinate Step in ANG or DEG, aJ/ANG or AJ/DEG ---
	-------------------------------------------------------------------------------
	           Coordinate      Previous         Force          Change          New 
	           ----------      --------        ------          ------        ------
	               R(1,2)       1.09329       -0.00019       -0.00003       1.09326
	               R(1,3)       1.09329       -0.00018       -0.00003       1.09326
	               R(1,4)       1.09329       -0.00019       -0.00003       1.09326
	               R(1,5)       1.09329       -0.00018       -0.00003       1.09326
	             B(2,1,3)     109.47124       -0.00000       -0.00004     109.47120
	             B(2,1,4)     109.47116        0.00000        0.00007     109.47123
	             B(2,1,5)     109.47123       -0.00000       -0.00014     109.47110
	             B(3,1,4)     109.47112       -0.00000       -0.00011     109.47101
	             B(3,1,5)     109.47116        0.00000        0.00015     109.47131
	             B(4,1,5)     109.47141        0.00000        0.00008     109.47148
	           O(2,1,3,4)      54.73570       -0.00000        0.00003      54.73573
	           O(2,1,3,5)     -54.73561       -0.00000       -0.00010     -54.73571
	           O(2,1,4,5)      54.73557        0.00000        0.00003      54.73560
	           O(3,1,2,4)     -54.73572       -0.00000       -0.00012     -54.73584
	           O(3,1,2,5)      54.73564       -0.00000       -0.00004      54.73560
	           O(3,1,4,5)     -54.73568        0.00000        0.00007     -54.73560
	           O(4,1,2,3)      54.73576        0.00000        0.00007      54.73583
	           O(4,1,2,5)     -54.73548        0.00000        0.00007     -54.73540
	           O(4,1,3,5)      54.73555        0.00000       -0.00004      54.73552
	           O(5,1,2,3)     -54.73565       -0.00000       -0.00001     -54.73566
	           O(5,1,2,4)      54.73544        0.00000        0.00003      54.73547
	           O(5,1,3,4)     -54.73553        0.00000        0.00017     -54.73536
	-------------------------------------------------------------------------------

	                                 ==> Convergence Check <==                                  
    
	Measures of convergence in internal coordinates in au.
    
	Criteria marked as inactive (o), active & met (*), and active & unmet ( ).

	----------------------------------------------------------------------------------------------
	   Step    Total Energy     Delta E     Max Force     RMS Force      Max Disp      RMS Disp   
	----------------------------------------------------------------------------------------------
	  Convergence Criteria     1.00e-06 *    3.00e-04 *             o    1.20e-03 *             o
	----------------------------------------------------------------------------------------------
	     2     -40.51840708   -2.20e-06      2.35e-05 *    9.63e-06 o    6.58e-05 *    2.71e-05 o  ~
	----------------------------------------------------------------------------------------------

Next Geometry in Ang 
	Fragment 1 (Ang)

	    C   0.0000006928   0.0000000852   0.0000003238
	    H  -0.6759971189   0.8549515341  -0.0854441497
	    H  -0.3953300449  -0.8367497391  -0.5820476012
	    H   0.0848340903  -0.2940276552   1.0495557133
	    H   0.9864923806   0.2758257750  -0.3820642863


    Final optimized geometry and variables:
    Molecular point group: c1
    Full point group: C1

    Geometry (in Angstrom), charge = 0, multiplicity = 1:

    C            0.000000579599     0.000000122629    -0.000000135519
    H           -0.676019008148     0.854978303651    -0.085446384613
    H           -0.395340685054    -0.836774555424    -0.582066469605
    H            0.084837393849    -0.294036038958     1.049588896912
    H            0.986521719754     0.275832168102    -0.382075907175


Scratch directory: D:\BaiduSyncdisk\py\MS\psi4_scratch/
gradient() will perform analytic gradient computation.
   => Libint2 <=

    Primary   basis highest AM E, G, H:  6, 6, -
    Auxiliary basis highest AM E, G, H:  7, 7, -
    Onebody   basis highest AM E, G, H:  -, -, -
    Solid Harmonics ordering:            Gaussian

*** tstart() called on iCarus
*** at Sun Jun 29 21:43:31 2025

   => Loading Basis Set <=

    Name: 6-31G(D)
    Role: ORBITAL
    Keyword: BASIS
    atoms 1   entry C          line   111 file D:\anaconda3\envs\nistpy\Library\share\psi4\basis\6-31g_d_.gbs 
    atoms 2-5 entry H          line    44 file D:\anaconda3\envs\nistpy\Library\share\psi4\basis\6-31g_d_.gbs 


         ---------------------------------------------------------
                                   SCF
               by Justin Turney, Rob Parrish, Andy Simmonett
                          and Daniel G. A. Smith
                              RKS Reference
                       10 Threads,   -562 MiB Core
         ---------------------------------------------------------

  ==> Geometry <==

    Molecular point group: c1
    Full point group: C1

    Geometry (in Angstrom), charge = 0, multiplicity = 1:

       Center              X                  Y                   Z               Mass       
    ------------   -----------------  -----------------  -----------------  -----------------
         C            0.000000579599     0.000000122629    -0.000000135519    12.000000000000
         H           -0.676019008148     0.854978303651    -0.085446384613     1.007825032230
         H           -0.395340685054    -0.836774555424    -0.582066469605     1.007825032230
         H            0.084837393849    -0.294036038958     1.049588896912     1.007825032230
         H            0.986521719754     0.275832168102    -0.382075907175     1.007825032230

  Running in c1 symmetry.

  Rotational constants: A =      5.24771  B =      5.24771  C =      5.24770 [cm^-1]
  Rotational constants: A = 157322.42722  B = 157322.26094  C = 157321.95423 [MHz]
  Nuclear repulsion =   13.394919662525909

  Charge       = 0
  Multiplicity = 1
  Electrons    = 10
  Nalpha       = 5
  Nbeta        = 5

  ==> Algorithm <==

  SCF Algorithm Type is DF.
  DIIS enabled.
  MOM disabled.
  Fractional occupation disabled.
  Guess Type is SAD.
  Energy threshold   = 1.00e-08
  Density threshold  = 1.00e-08
  Integral threshold = 1.00e-12

  ==> Primary Basis <==

  Basis Set: 6-31G(D)
    Blend: 6-31G(D)
    Number of shells: 14
    Number of basis functions: 23
    Number of Cartesian functions: 23
    Spherical Harmonics?: false
    Max angular momentum: 2

  ==> DFT Potential <==

   => LibXC <=

    Version 7.0.0
    S. Lehtola, C. Steigemann, M. J.T. Oliveira, and M. A.L. Marques.,  SoftwareX 7, 1–5 (2018) (10.1016/j.softx.2017.11.002)

   => Composite Functional: B3LYP <= 

    B3LYP Hyb-GGA Exchange-Correlation Functional

    P. J. Stephens, F. J. Devlin, C. F. Chabalowski, and M. J. Frisch.,  J. Phys. Chem. 98, 11623 (1994) (10.1021/j100096a001)

    Deriv               =              1
    GGA                 =           TRUE
    Meta                =          FALSE

    Exchange Hybrid     =           TRUE
    MP2 Hybrid          =          FALSE

   => Exchange Functionals <=

    0.0800   Slater exchange
    0.7200         Becke 88

   => Exact (HF) Exchange <=

    0.2000               HF 

   => Correlation Functionals <=

    0.1900   Vosko, Wilk & Nusair (VWN5_RPA)
    0.8100   Lee, Yang & Parr

   => LibXC Density Thresholds  <==

    XC_HYB_GGA_XC_B3LYP:  1.00E-15 

   => Molecular Quadrature <=

    Radial Scheme          =       TREUTLER
    Pruning Scheme         =           NONE
    Nuclear Scheme         =       TREUTLER

    Blocking Scheme        =         OCTREE
    BS radius alpha        =              1
    Pruning alpha          =              1
    Radial Points          =             75
    Spherical Points       =            302
    Total Points           =         109196
    Total Blocks           =            879
    Max Points             =            256
    Max Functions          =             23
    Weights Tolerance      =       1.00E-15

   => Loading Basis Set <=

    Name: (6-31G(D) AUX)
    Role: JKFIT
    Keyword: DF_BASIS_SCF
    atoms 1   entry C          line   121 file D:\anaconda3\envs\nistpy\Library\share\psi4\basis\cc-pvdz-jkfit.gbs 
    atoms 2-5 entry H          line    51 file D:\anaconda3\envs\nistpy\Library\share\psi4\basis\cc-pvdz-jkfit.gbs 

  ==> Integral Setup <==

  DFHelper Memory: AOs need 0.001 GiB; user supplied 5.515 GiB. 
  Using in-core AOs.

  ==> MemDFJK: Density-Fitted J/K Matrices <==

    J tasked:                   Yes
    K tasked:                   Yes
    wK tasked:                   No
    OpenMP threads:              10
    Memory [MiB]:              5647
    Algorithm:                 Core
    Schwarz Cutoff:           1E-12
    Mask sparsity (%):       0.0000
    Fitting Condition:        1E-10

   => Auxiliary Basis Set <=

  Basis Set: (6-31G(D) AUX)
    Blend: CC-PVDZ-JKFIT
    Number of shells: 60
    Number of basis functions: 181
    Number of Cartesian functions: 181
    Spherical Harmonics?: false
    Max angular momentum: 3

  Cached 100.0% of DFT collocation blocks in 0.073 [GiB].

  Minimum eigenvalue in the overlap matrix is 1.7960254000E-02.
  Reciprocal condition number of the overlap matrix is 3.2047277001E-03.
    Using symmetric orthogonalization.

  ==> Pre-Iterations <==

  SCF Guess: Superposition of Atomic Densities via on-the-fly atomic UHF (no occupation information).

   -------------------------
    Irrep   Nso     Nmo    
   -------------------------
     A         23      23 
   -------------------------
    Total      23      23
   -------------------------

  ==> Iterations <==

                           Total Energy        Delta E     RMS |[F,P]|

   @DF-RKS iter SAD:   -40.09237109608831   -4.00924e+01   0.00000e+00 
   @DF-RKS iter   1:   -40.35182816237500   -2.59457e-01   2.48833e-02 ADIIS/DIIS
   @DF-RKS iter   2:   -40.31075873379709    4.10694e-02   2.63668e-02 ADIIS/DIIS
   @DF-RKS iter   3:   -40.51839164389247   -2.07633e-01   2.48288e-04 ADIIS/DIIS
   @DF-RKS iter   4:   -40.51840611713330   -1.44732e-05   6.01082e-05 DIIS
   @DF-RKS iter   5:   -40.51840707962309   -9.62490e-07   3.42334e-06 DIIS
   @DF-RKS iter   6:   -40.51840708260177   -2.97868e-09   9.02578e-08 DIIS
   @DF-RKS iter   7:   -40.51840708260422   -2.45137e-12   2.68214e-09 DIIS
  Energy and wave function converged.


  ==> Post-Iterations <==

   Electrons on quadrature grid:
      Ntotal   =   10.0000006238 ; deviation = 6.238e-07 

    Orbital Energies [Eh]
    ---------------------

    Doubly Occupied:                                                      

       1A    -10.166938     2A     -0.690279     3A     -0.389061  
       4A     -0.389061     5A     -0.389060  

    Virtual:                                                              

       6A      0.117914     7A      0.176556     8A      0.176557  
       9A      0.176557    10A      0.533224    11A      0.533225  
      12A      0.533227    13A      0.897799    14A      0.897800  
      15A      0.897801    16A      0.947455    17A      1.099862  
      18A      1.657043    19A      1.657044    20A      2.243407  
      21A      2.243408    22A      2.243409    23A      4.179167  

    Final Occupation by Irrep:
              A 
    DOCC [     5 ]
    NA   [     5 ]
    NB   [     5 ]

  @DF-RKS Final Energy:   -40.51840708260422

   => Energetics <=

    Nuclear Repulsion Energy =             13.3949196625259095
    One-Electron Energy =                 -79.7584351946705681
    Two-Electron Energy =                  31.4319449141967979
    DFT Exchange-Correlation Energy =      -5.5868364646563604
    Empirical Dispersion Energy =           0.0000000000000000
    VV10 Nonlocal Energy =                  0.0000000000000000
    Total Energy =                        -40.5184070826042202

Computation Completed


Properties will be evaluated at   0.000000,   0.000000,   0.000000 [a0]

Properties computed using the SCF density matrix


 Multipole Moments:

 ------------------------------------------------------------------------------------
     Multipole            Electronic (a.u.)      Nuclear  (a.u.)        Total (a.u.)
 ------------------------------------------------------------------------------------

 L = 1.  Multiply by 2.5417464519 to convert [e a0] to [Debye]
 Dipole X            :         -0.0000070            0.0000055           -0.0000015
 Dipole Y            :         -0.0000020            0.0000012           -0.0000008
 Dipole Z            :          0.0000022           -0.0000013            0.0000009
 Magnitude           :                                                    0.0000020

 ------------------------------------------------------------------------------------

*** tstop() called on iCarus at Sun Jun 29 21:43:31 2025
Module time:
	user time   =        nan seconds =        nan minutes
	system time =        nan seconds =        nan minutes
	total time  =          0 seconds =       0.00 minutes
Total time:
	user time   =        nan seconds =        nan minutes
	system time =        nan seconds =        nan minutes
	total time  =          1 seconds =       0.02 minutes

*** tstart() called on iCarus
*** at Sun Jun 29 21:43:31 2025


         ------------------------------------------------------------
                                   SCF GRAD                          
                          Rob Parrish, Justin Turney,                
                       Andy Simmonett, and Alex Sokolov              
         ------------------------------------------------------------

  ==> Geometry <==

    Molecular point group: c1
    Full point group: C1

    Geometry (in Angstrom), charge = 0, multiplicity = 1:

       Center              X                  Y                   Z               Mass       
    ------------   -----------------  -----------------  -----------------  -----------------
         C            0.000000579599     0.000000122629    -0.000000135519    12.000000000000
         H           -0.676019008148     0.854978303651    -0.085446384613     1.007825032230
         H           -0.395340685054    -0.836774555424    -0.582066469605     1.007825032230
         H            0.084837393849    -0.294036038958     1.049588896912     1.007825032230
         H            0.986521719754     0.275832168102    -0.382075907175     1.007825032230

  Nuclear repulsion =   13.394919662525909

  ==> Basis Set <==

  Basis Set: 6-31G(D)
    Blend: 6-31G(D)
    Number of shells: 14
    Number of basis functions: 23
    Number of Cartesian functions: 23
    Spherical Harmonics?: false
    Max angular momentum: 2

  ==> DFJKGrad: Density-Fitted SCF Gradients <==

    Gradient:                    1
    J tasked:                  Yes
    K tasked:                  Yes
    wK tasked:                  No
    OpenMP threads:             10
    Integrals threads:          10
    Memory [MiB]:             -422
    Schwarz Cutoff:          1E-12
    Fitting Condition:       1E-10

   => Auxiliary Basis Set <=

  Basis Set: (6-31G(D) AUX)
    Blend: CC-PVDZ-JKFIT
    Number of shells: 60
    Number of basis functions: 181
    Number of Cartesian functions: 181
    Spherical Harmonics?: false
    Max angular momentum: 3

  ==> DFT Potential <==

   => LibXC <=

    Version 7.0.0
    S. Lehtola, C. Steigemann, M. J.T. Oliveira, and M. A.L. Marques.,  SoftwareX 7, 1–5 (2018) (10.1016/j.softx.2017.11.002)

   => Composite Functional: B3LYP <= 

    B3LYP Hyb-GGA Exchange-Correlation Functional

    P. J. Stephens, F. J. Devlin, C. F. Chabalowski, and M. J. Frisch.,  J. Phys. Chem. 98, 11623 (1994) (10.1021/j100096a001)

    Deriv               =              1
    GGA                 =           TRUE
    Meta                =          FALSE

    Exchange Hybrid     =           TRUE
    MP2 Hybrid          =          FALSE

   => Exchange Functionals <=

    0.0800   Slater exchange
    0.7200         Becke 88

   => Exact (HF) Exchange <=

    0.2000               HF 

   => Correlation Functionals <=

    0.1900   Vosko, Wilk & Nusair (VWN5_RPA)
    0.8100   Lee, Yang & Parr

   => LibXC Density Thresholds  <==

    XC_HYB_GGA_XC_B3LYP:  1.00E-15 

   => Molecular Quadrature <=

    Radial Scheme          =       TREUTLER
    Pruning Scheme         =           NONE
    Nuclear Scheme         =       TREUTLER

    Blocking Scheme        =         OCTREE
    BS radius alpha        =              1
    Pruning alpha          =              1
    Radial Points          =             75
    Spherical Points       =            302
    Total Points           =         109196
    Total Blocks           =            879
    Max Points             =            256
    Max Functions          =             23
    Weights Tolerance      =       1.00E-15


  -Total Gradient:
     Atom            X                  Y                   Z
    ------   -----------------  -----------------  -----------------
       1        0.000000425332    -0.000000330211    -0.000001844748
       2       -0.000014431519     0.000018010336    -0.000002048397
       3       -0.000007486421    -0.000016959406    -0.000012306345
       4        0.000001972310    -0.000006156134     0.000022245426
       5        0.000019713197     0.000004885936    -0.000007786010


*** tstop() called on iCarus at Sun Jun 29 21:43:32 2025
Module time:
	user time   =        nan seconds =        nan minutes
	system time =        nan seconds =        nan minutes
	total time  =          1 seconds =       0.02 minutes
Total time:
	user time   =        nan seconds =        nan minutes
	system time =        nan seconds =        nan minutes
	total time  =          2 seconds =       0.03 minutes

    			-----------------------------------------

    			 OPTKING 3.0: for geometry optimizations 

    			     By R.A. King, Bethel University     

    			        with contributions from          

    			    A.V. Copan, J. Cayton, A. Heide      

    			-----------------------------------------

    
	                            ===> Fragment 1 <== 

	 Z (Atomic Numbers)          Masses                          Geom                  
	       6.000000            12.000000          0.00000110  0.00000023 -0.00000026   
	       1.000000             1.007825         -1.27749078  1.61567484 -0.16147027   
	       1.000000             1.007825         -0.74708562 -1.58127474 -1.09994621   
	       1.000000             1.007825          0.16031944 -0.55564758  1.98343556   
	       1.000000             1.007825          1.86425587  0.52124725 -0.72201882   

	 - Coordinate -           - BOHR/RAD -       - ANG/DEG -
	 R(1,2)           =         2.066026           1.093294
	 R(1,3)           =         2.066023           1.093292
	 R(1,4)           =         2.066026           1.093294
	 R(1,5)           =         2.066024           1.093293
	 B(2,1,3)         =         1.910634         109.471243
	 B(2,1,4)         =         1.910632         109.471160
	 B(2,1,5)         =         1.910633         109.471232
	 B(3,1,4)         =         1.910631         109.471119
	 B(3,1,5)         =         1.910632         109.471162
	 B(4,1,5)         =         1.910637         109.471408
	 O(2,1,3,4)       =         0.955318          54.735700
	 O(2,1,3,5)       =        -0.955317         -54.735606
	 O(2,1,4,5)       =         0.955316          54.735566
	 O(3,1,2,4)       =        -0.955319         -54.735720
	 O(3,1,2,5)       =         0.955317          54.735641
	 O(3,1,4,5)       =        -0.955318         -54.735677
	 O(4,1,2,3)       =         0.955319          54.735761
	 O(4,1,2,5)       =        -0.955314         -54.735478
	 O(4,1,3,5)       =         0.955316          54.735554
	 O(5,1,2,3)       =        -0.955317         -54.735647
	 O(5,1,2,4)       =         0.955314          54.735442
	 O(5,1,3,4)       =        -0.955315         -54.735532


	        --- Internal Coordinate Step in ANG or DEG, aJ/ANG or AJ/DEG ---
	-------------------------------------------------------------------------------
	           Coordinate      Previous         Force          Change          New 
	           ----------      --------        ------          ------        ------
	               R(1,2)       1.09329       -0.00019       -0.00004       1.09326
	               R(1,3)       1.09329       -0.00018       -0.00003       1.09326
	               R(1,4)       1.09329       -0.00019       -0.00004       1.09326
	               R(1,5)       1.09329       -0.00018       -0.00003       1.09326
	             B(2,1,3)     109.47124       -0.00000       -0.00004     109.47120
	             B(2,1,4)     109.47116        0.00000        0.00007     109.47123
	             B(2,1,5)     109.47123       -0.00000       -0.00014     109.47109
	             B(3,1,4)     109.47112       -0.00000       -0.00012     109.47100
	             B(3,1,5)     109.47116        0.00000        0.00015     109.47131
	             B(4,1,5)     109.47141        0.00000        0.00008     109.47149
	           O(2,1,3,4)      54.73570       -0.00000        0.00003      54.73573
	           O(2,1,3,5)     -54.73561       -0.00000       -0.00011     -54.73571
	           O(2,1,4,5)      54.73557        0.00000        0.00003      54.73560
	           O(3,1,2,4)     -54.73572       -0.00000       -0.00012     -54.73584
	           O(3,1,2,5)      54.73564       -0.00000       -0.00004      54.73560
	           O(3,1,4,5)     -54.73568        0.00000        0.00008     -54.73560
	           O(4,1,2,3)      54.73576        0.00000        0.00007      54.73583
	           O(4,1,2,5)     -54.73548        0.00000        0.00008     -54.73540
	           O(4,1,3,5)      54.73555        0.00000       -0.00004      54.73552
	           O(5,1,2,3)     -54.73565       -0.00000       -0.00001     -54.73566
	           O(5,1,2,4)      54.73544        0.00000        0.00003      54.73547
	           O(5,1,3,4)     -54.73553        0.00000        0.00017     -54.73536
	-------------------------------------------------------------------------------

	                                 ==> Convergence Check <==                                  
    
	Measures of convergence in internal coordinates in au.
    
	Criteria marked as inactive (o), active & met (*), and active & unmet ( ).

	----------------------------------------------------------------------------------------------~
	   Step    Total Energy     Delta E     Max Force     RMS Force      Max Disp      RMS Disp   ~
	----------------------------------------------------------------------------------------------~
	  Convergence Criteria     1.00e-06 *    3.00e-04 *             o    1.20e-03 *             o~
	----------------------------------------------------------------------------------------------~
	     1     -40.51840708   -4.05e+01      2.35e-05 *    9.63e-06 o    6.81e-05 *    2.80e-05 o  ~
	----------------------------------------------------------------------------------------------

Next Geometry in Ang 
	Fragment 1 (Ang)

	    C   0.0000006948   0.0000000847   0.0000003408
	    H  -0.6759963474   0.8549505863  -0.0854440669
	    H  -0.3953296655  -0.8367488604  -0.5820469377
	    H   0.0848339744  -0.2940273539   1.0495545389
	    H   0.9864913438   0.2758255432  -0.3820638751


    Final optimized geometry and variables:
    Molecular point group: c1
    Full point group: C1

    Geometry (in Angstrom), charge = 0, multiplicity = 1:

    C            0.000000579599     0.000000122629    -0.000000135519
    H           -0.676019008148     0.854978303651    -0.085446384613
    H           -0.395340685054    -0.836774555424    -0.582066469605
    H            0.084837393849    -0.294036038958     1.049588896912
    H            0.986521719754     0.275832168102    -0.382075907175


Scratch directory: D:\BaiduSyncdisk\py\MS\psi4_scratch/
gradient() will perform analytic gradient computation.
   => Libint2 <=

    Primary   basis highest AM E, G, H:  6, 6, -
    Auxiliary basis highest AM E, G, H:  7, 7, -
    Onebody   basis highest AM E, G, H:  -, -, -
    Solid Harmonics ordering:            Gaussian

*** tstart() called on iCarus
*** at Sun Jun 29 21:43:32 2025

   => Loading Basis Set <=

    Name: 6-31G(D)
    Role: ORBITAL
    Keyword: BASIS
    atoms 1   entry C          line   111 file D:\anaconda3\envs\nistpy\Library\share\psi4\basis\6-31g_d_.gbs 
    atoms 2-5 entry H          line    44 file D:\anaconda3\envs\nistpy\Library\share\psi4\basis\6-31g_d_.gbs 


         ---------------------------------------------------------
                                   SCF
               by Justin Turney, Rob Parrish, Andy Simmonett
                          and Daniel G. A. Smith
                              UKS Reference
                       10 Threads,   -562 MiB Core
         ---------------------------------------------------------

  ==> Geometry <==

    Molecular point group: c1
    Full point group: C1

    Geometry (in Angstrom), charge = 1, multiplicity = 2:

       Center              X                  Y                   Z               Mass       
    ------------   -----------------  -----------------  -----------------  -----------------
         C            0.000000579599     0.000000122629    -0.000000135519    12.000000000000
         H           -0.676019008148     0.854978303651    -0.085446384613     1.007825032230
         H           -0.395340685054    -0.836774555424    -0.582066469605     1.007825032230
         H            0.084837393849    -0.294036038958     1.049588896912     1.007825032230
         H            0.986521719754     0.275832168102    -0.382075907175     1.007825032230

  Running in c1 symmetry.

  Rotational constants: A =      5.24771  B =      5.24771  C =      5.24770 [cm^-1]
  Rotational constants: A = 157322.42722  B = 157322.26094  C = 157321.95423 [MHz]
  Nuclear repulsion =   13.394919662525909

  Charge       = 1
  Multiplicity = 2
  Electrons    = 9
  Nalpha       = 5
  Nbeta        = 4

  ==> Algorithm <==

  SCF Algorithm Type is DF.
  DIIS enabled.
  MOM disabled.
  Fractional occupation disabled.
  Guess Type is SAD.
  Energy threshold   = 1.00e-08
  Density threshold  = 1.00e-08
  Integral threshold = 1.00e-12

  ==> Primary Basis <==

  Basis Set: 6-31G(D)
    Blend: 6-31G(D)
    Number of shells: 14
    Number of basis functions: 23
    Number of Cartesian functions: 23
    Spherical Harmonics?: false
    Max angular momentum: 2

  ==> DFT Potential <==

   => LibXC <=

    Version 7.0.0
    S. Lehtola, C. Steigemann, M. J.T. Oliveira, and M. A.L. Marques.,  SoftwareX 7, 1–5 (2018) (10.1016/j.softx.2017.11.002)

   => Composite Functional: B3LYP <= 

    B3LYP Hyb-GGA Exchange-Correlation Functional

    P. J. Stephens, F. J. Devlin, C. F. Chabalowski, and M. J. Frisch.,  J. Phys. Chem. 98, 11623 (1994) (10.1021/j100096a001)

    Deriv               =              1
    GGA                 =           TRUE
    Meta                =          FALSE

    Exchange Hybrid     =           TRUE
    MP2 Hybrid          =          FALSE

   => Exchange Functionals <=

    0.0800   Slater exchange
    0.7200         Becke 88

   => Exact (HF) Exchange <=

    0.2000               HF 

   => Correlation Functionals <=

    0.1900   Vosko, Wilk & Nusair (VWN5_RPA)
    0.8100   Lee, Yang & Parr

   => LibXC Density Thresholds  <==

    XC_HYB_GGA_XC_B3LYP:  1.00E-15 

   => Molecular Quadrature <=

    Radial Scheme          =       TREUTLER
    Pruning Scheme         =           NONE
    Nuclear Scheme         =       TREUTLER

    Blocking Scheme        =         OCTREE
    BS radius alpha        =              1
    Pruning alpha          =              1
    Radial Points          =             75
    Spherical Points       =            302
    Total Points           =         109196
    Total Blocks           =            879
    Max Points             =            256
    Max Functions          =             23
    Weights Tolerance      =       1.00E-15

   => Loading Basis Set <=

    Name: (6-31G(D) AUX)
    Role: JKFIT
    Keyword: DF_BASIS_SCF
    atoms 1   entry C          line   121 file D:\anaconda3\envs\nistpy\Library\share\psi4\basis\cc-pvdz-jkfit.gbs 
    atoms 2-5 entry H          line    51 file D:\anaconda3\envs\nistpy\Library\share\psi4\basis\cc-pvdz-jkfit.gbs 

  ==> Integral Setup <==

  DFHelper Memory: AOs need 0.001 GiB; user supplied 5.515 GiB. 
  Using in-core AOs.

  ==> MemDFJK: Density-Fitted J/K Matrices <==

    J tasked:                   Yes
    K tasked:                   Yes
    wK tasked:                   No
    OpenMP threads:              10
    Memory [MiB]:              5647
    Algorithm:                 Core
    Schwarz Cutoff:           1E-12
    Mask sparsity (%):       0.0000
    Fitting Condition:        1E-10

   => Auxiliary Basis Set <=

  Basis Set: (6-31G(D) AUX)
    Blend: CC-PVDZ-JKFIT
    Number of shells: 60
    Number of basis functions: 181
    Number of Cartesian functions: 181
    Spherical Harmonics?: false
    Max angular momentum: 3

  Cached 100.0% of DFT collocation blocks in 0.073 [GiB].

  Minimum eigenvalue in the overlap matrix is 1.7960254000E-02.
  Reciprocal condition number of the overlap matrix is 3.2047277001E-03.
    Using symmetric orthogonalization.

  ==> Pre-Iterations <==

  SCF Guess: Superposition of Atomic Densities via on-the-fly atomic UHF (no occupation information).

   -------------------------
    Irrep   Nso     Nmo    
   -------------------------
     A         23      23 
   -------------------------
    Total      23      23
   -------------------------

  ==> Iterations <==

                           Total Energy        Delta E     RMS |[F,P]|

   @DF-UKS iter SAD:   -40.09237109608834   -4.00924e+01   0.00000e+00 
   @DF-UKS iter   1:   -39.95958431620443    1.32787e-01   1.19156e-02 ADIIS/DIIS
   @DF-UKS iter   2:   -39.97015896188697   -1.05746e-02   1.00809e-02 ADIIS/DIIS
   @DF-UKS iter   3:   -39.99824319736537   -2.80842e-02   6.89526e-04 ADIIS/DIIS
   @DF-UKS iter   4:   -39.99856865325136   -3.25456e-04   2.12605e-04 ADIIS/DIIS
   @DF-UKS iter   5:   -39.99879685939136   -2.28206e-04   2.87589e-04 ADIIS/DIIS
   @DF-UKS iter   6:   -39.99895146338734   -1.54604e-04   2.55653e-04 ADIIS/DIIS
   @DF-UKS iter   7:   -39.99922843111432   -2.76968e-04   2.10414e-04 ADIIS/DIIS
   @DF-UKS iter   8:   -39.99957232610745   -3.43895e-04   1.13102e-04 ADIIS/DIIS
   @DF-UKS iter   9:   -39.99956679671459    5.52939e-06   6.89783e-05 DIIS
   @DF-UKS iter  10:   -39.99958072303451   -1.39263e-05   4.16684e-05 DIIS
   @DF-UKS iter  11:   -39.99958174854208   -1.02551e-06   9.87426e-06 DIIS
   @DF-UKS iter  12:   -39.99958184136291   -9.28208e-08   1.35454e-06 DIIS
   @DF-UKS iter  13:   -39.99958184402318   -2.66026e-09   6.33819e-07 DIIS
   @DF-UKS iter  14:   -39.99958184508055   -1.05737e-09   4.84527e-07 DIIS
   @DF-UKS iter  15:   -39.99958184637819   -1.29764e-09   2.05637e-07 DIIS
   @DF-UKS iter  16:   -39.99958184661302   -2.34827e-10   8.01078e-08 DIIS
   @DF-UKS iter  17:   -39.99958184663650   -2.34834e-11   2.29098e-08 DIIS
   @DF-UKS iter  18:   -39.99958184663753   -1.03029e-12   1.61833e-09 DIIS
  Energy and wave function converged.


  ==> Post-Iterations <==

   Electrons on quadrature grid:
      Nalpha   =    5.0000002658 ; deviation = 2.658e-07
      Nbeta    =    4.0000005492 ; deviation = 5.492e-07
      Ntotal   =    9.0000008150 ; deviation = 8.150e-07 

   @Spin Contamination Metric:   2.327419367E-03
   @S^2 Expected:                7.500000000E-01
   @S^2 Observed:                7.523274194E-01
   @S   Expected:                5.000000000E-01
   @S   Observed:                5.000000000E-01

    Orbital Energies [Eh]
    ---------------------

    Alpha Occupied:                                                       

       1A    -10.570186     2A     -1.090621     3A     -0.798355  
       4A     -0.769268     5A     -0.769268  

    Alpha Virtual:                                                        

       6A     -0.168518     7A     -0.138243     8A     -0.132746  
       9A     -0.132746    10A      0.190199    11A      0.205389  
      12A      0.205392    13A      0.539292    14A      0.546490  
      15A      0.546490    16A      0.573191    17A      0.773189  
      18A      1.225580    19A      1.269461    20A      1.832715  
      21A      1.832716    22A      1.859773    23A      3.783807  

    Beta Occupied:                                                        

       1A    -10.561962     2A     -1.041090     3A     -0.741625  
       4A     -0.741624  

    Beta Virtual:                                                         

       5A     -0.648744     6A     -0.154391     7A     -0.116766  
       8A     -0.116766     9A     -0.107633    10A      0.217038  
      11A      0.217041    12A      0.222786    13A      0.571971  
      14A      0.571971    15A      0.580449    16A      0.613113  
      17A      0.787500    18A      1.272421    19A      1.298000  
      20A      1.869523    21A      1.878827    22A      1.878829  
      23A      3.807865  

    Final Occupation by Irrep:
              A 
    DOCC [     4 ]
    SOCC [     1 ]
    NA   [     5 ]
    NB   [     4 ]

  @DF-UKS Final Energy:   -39.99958184663753

   => Energetics <=

    Nuclear Repulsion Energy =             13.3949196625259095
    One-Electron Energy =                 -75.8888430036099635
    Two-Electron Energy =                  27.7953364316270175
    DFT Exchange-Correlation Energy =      -5.3009949371804952
    Empirical Dispersion Energy =           0.0000000000000000
    VV10 Nonlocal Energy =                  0.0000000000000000
    Total Energy =                        -39.9995818466375326

  UHF NO Occupations:
  HONO-2 :    3  A 1.9995792
  HONO-1 :    4  A 1.9995792
  HONO-0 :    5  A 1.0000000
  LUNO+0 :    6  A 0.0004208
  LUNO+1 :    7  A 0.0004208
  LUNO+2 :    8  A 0.0003221
  LUNO+3 :    9  A 0.0000002


Computation Completed


Properties will be evaluated at   0.000000,   0.000000,   0.000000 [a0]

Properties computed using the SCF density matrix


 Multipole Moments:

 ------------------------------------------------------------------------------------
     Multipole            Electronic (a.u.)      Nuclear  (a.u.)        Total (a.u.)
 ------------------------------------------------------------------------------------

 L = 1.  Multiply by 2.5417464519 to convert [e a0] to [Debye]
 Dipole X            :         -0.0000162            0.0000055           -0.0000107
 Dipole Y            :          0.0000169            0.0000012            0.0000181
 Dipole Z            :          0.0000214           -0.0000013            0.0000201
 Magnitude           :                                                    0.0000291

 ------------------------------------------------------------------------------------

*** tstop() called on iCarus at Sun Jun 29 21:43:32 2025
Module time:
	user time   =        nan seconds =        nan minutes
	system time =        nan seconds =        nan minutes
	total time  =          0 seconds =       0.00 minutes
Total time:
	user time   =        nan seconds =        nan minutes
	system time =        nan seconds =        nan minutes
	total time  =          2 seconds =       0.03 minutes

*** tstart() called on iCarus
*** at Sun Jun 29 21:43:32 2025


         ------------------------------------------------------------
                                   SCF GRAD                          
                          Rob Parrish, Justin Turney,                
                       Andy Simmonett, and Alex Sokolov              
         ------------------------------------------------------------

  ==> Geometry <==

    Molecular point group: c1
    Full point group: C1

    Geometry (in Angstrom), charge = 1, multiplicity = 2:

       Center              X                  Y                   Z               Mass       
    ------------   -----------------  -----------------  -----------------  -----------------
         C            0.000000579599     0.000000122629    -0.000000135519    12.000000000000
         H           -0.676019008148     0.854978303651    -0.085446384613     1.007825032230
         H           -0.395340685054    -0.836774555424    -0.582066469605     1.007825032230
         H            0.084837393849    -0.294036038958     1.049588896912     1.007825032230
         H            0.986521719754     0.275832168102    -0.382075907175     1.007825032230

  Nuclear repulsion =   13.394919662525909

  ==> Basis Set <==

  Basis Set: 6-31G(D)
    Blend: 6-31G(D)
    Number of shells: 14
    Number of basis functions: 23
    Number of Cartesian functions: 23
    Spherical Harmonics?: false
    Max angular momentum: 2

  ==> DFJKGrad: Density-Fitted SCF Gradients <==

    Gradient:                    1
    J tasked:                  Yes
    K tasked:                  Yes
    wK tasked:                  No
    OpenMP threads:             10
    Integrals threads:          10
    Memory [MiB]:             -422
    Schwarz Cutoff:          1E-12
    Fitting Condition:       1E-10

   => Auxiliary Basis Set <=

  Basis Set: (6-31G(D) AUX)
    Blend: CC-PVDZ-JKFIT
    Number of shells: 60
    Number of basis functions: 181
    Number of Cartesian functions: 181
    Spherical Harmonics?: false
    Max angular momentum: 3

  ==> DFT Potential <==

   => LibXC <=

    Version 7.0.0
    S. Lehtola, C. Steigemann, M. J.T. Oliveira, and M. A.L. Marques.,  SoftwareX 7, 1–5 (2018) (10.1016/j.softx.2017.11.002)

   => Composite Functional: B3LYP <= 

    B3LYP Hyb-GGA Exchange-Correlation Functional

    P. J. Stephens, F. J. Devlin, C. F. Chabalowski, and M. J. Frisch.,  J. Phys. Chem. 98, 11623 (1994) (10.1021/j100096a001)

    Deriv               =              1
    GGA                 =           TRUE
    Meta                =          FALSE

    Exchange Hybrid     =           TRUE
    MP2 Hybrid          =          FALSE

   => Exchange Functionals <=

    0.0800   Slater exchange
    0.7200         Becke 88

   => Exact (HF) Exchange <=

    0.2000               HF 

   => Correlation Functionals <=

    0.1900   Vosko, Wilk & Nusair (VWN5_RPA)
    0.8100   Lee, Yang & Parr

   => LibXC Density Thresholds  <==

    XC_HYB_GGA_XC_B3LYP:  1.00E-15 

   => Molecular Quadrature <=

    Radial Scheme          =       TREUTLER
    Pruning Scheme         =           NONE
    Nuclear Scheme         =       TREUTLER

    Blocking Scheme        =         OCTREE
    BS radius alpha        =              1
    Pruning alpha          =              1
    Radial Points          =             75
    Spherical Points       =            302
    Total Points           =         109196
    Total Blocks           =            879
    Max Points             =            256
    Max Functions          =             23
    Weights Tolerance      =       1.00E-15


  -Total Gradient:
     Atom            X                  Y                   Z
    ------   -----------------  -----------------  -----------------
       1       -0.000004518413     0.000007528872     0.000006340646
       2       -0.009220532922    -0.049674590581    -0.025581622975
       3       -0.025801452784     0.050264214136     0.003755228185
       4        0.044146487169     0.016533402592    -0.031377917482
       5       -0.009119829001    -0.017131924720     0.053196285912


*** tstop() called on iCarus at Sun Jun 29 21:43:33 2025
Module time:
	user time   =        nan seconds =        nan minutes
	system time =        nan seconds =        nan minutes
	total time  =          1 seconds =       0.02 minutes
Total time:
	user time   =        nan seconds =        nan minutes
	system time =        nan seconds =        nan minutes
	total time  =          3 seconds =       0.05 minutes

    			-----------------------------------------

    			 OPTKING 3.0: for geometry optimizations 

    			     By R.A. King, Bethel University     

    			        with contributions from          

    			    A.V. Copan, J. Cayton, A. Heide      

    			-----------------------------------------

    
	                            ===> Fragment 1 <== 

	 Z (Atomic Numbers)          Masses                          Geom                  
	       6.000000            12.000000          0.00000110  0.00000023 -0.00000026   
	       1.000000             1.007825         -1.27749078  1.61567484 -0.16147027   
	       1.000000             1.007825         -0.74708562 -1.58127474 -1.09994621   
	       1.000000             1.007825          0.16031944 -0.55564758  1.98343556   
	       1.000000             1.007825          1.86425587  0.52124725 -0.72201882   

	 - Coordinate -           - BOHR/RAD -       - ANG/DEG -
	 R(1,2)           =         2.066026           1.093294
	 R(1,3)           =         2.066023           1.093292
	 R(1,4)           =         2.066026           1.093294
	 R(1,5)           =         2.066024           1.093293
	 B(2,1,3)         =         1.910634         109.471243
	 B(2,1,4)         =         1.910632         109.471160
	 B(2,1,5)         =         1.910633         109.471232
	 B(3,1,4)         =         1.910631         109.471119
	 B(3,1,5)         =         1.910632         109.471162
	 B(4,1,5)         =         1.910637         109.471408
	 O(2,1,3,4)       =         0.955318          54.735700
	 O(2,1,3,5)       =        -0.955317         -54.735606
	 O(2,1,4,5)       =         0.955316          54.735566
	 O(3,1,2,4)       =        -0.955319         -54.735720
	 O(3,1,2,5)       =         0.955317          54.735641
	 O(3,1,4,5)       =        -0.955318         -54.735677
	 O(4,1,2,3)       =         0.955319          54.735761
	 O(4,1,2,5)       =        -0.955314         -54.735478
	 O(4,1,3,5)       =         0.955316          54.735554
	 O(5,1,2,3)       =        -0.955317         -54.735647
	 O(5,1,2,4)       =         0.955314          54.735442
	 O(5,1,3,4)       =        -0.955315         -54.735532


	        --- Internal Coordinate Step in ANG or DEG, aJ/ANG or AJ/DEG ---
	-------------------------------------------------------------------------------
	           Coordinate      Previous         Force          Change          New 
	           ----------      --------        ------          ------        ------
	               R(1,2)       1.09329        0.25660        0.03941       1.13270
	               R(1,3)       1.09329        0.25656        0.03940       1.13270
	               R(1,4)       1.09329        0.25659        0.03941       1.13270
	               R(1,5)       1.09329        0.25657        0.03941       1.13270
	             B(2,1,3)     109.47124        0.00330       11.64575     121.11699
	             B(2,1,4)     109.47116       -0.00165       -5.49010     103.98106
	             B(2,1,5)     109.47123       -0.00165       -5.49000     103.98123
	             B(3,1,4)     109.47112       -0.00165       -5.48984     103.98128
	             B(3,1,5)     109.47116       -0.00165       -5.48944     103.98172
	             B(4,1,5)     109.47141        0.00330       11.64579     121.11720
	           O(2,1,3,4)      54.73570       -0.00083       -4.53257      50.20313
	           O(2,1,3,5)     -54.73561        0.00083        4.53281     -50.20280
	           O(2,1,4,5)      54.73557        0.00165        5.82333      60.55889
	           O(3,1,2,4)     -54.73572        0.00083        4.53265     -50.20307
	           O(3,1,2,5)      54.73564       -0.00083       -4.53299      50.20265
	           O(3,1,4,5)     -54.73568       -0.00165       -5.82242     -60.55810
	           O(4,1,2,3)      54.73576        0.00165        5.82318      60.55894
	           O(4,1,2,5)     -54.73548        0.00083        4.53254     -50.20294
	           O(4,1,3,5)      54.73555       -0.00083       -4.53301      50.20254
	           O(5,1,2,3)     -54.73565       -0.00165       -5.82261     -60.55825
	           O(5,1,2,4)      54.73544       -0.00083       -4.53256      50.20289
	           O(5,1,3,4)     -54.73553        0.00083        4.53312     -50.20241
	-------------------------------------------------------------------------------

	                                 ==> Convergence Check <==                                  
    
	Measures of convergence in internal coordinates in au.
    
	Criteria marked as inactive (o), active & met (*), and active & unmet ( ).

	----------------------------------------------------------------------------------------------~
	   Step    Total Energy     Delta E     Max Force     RMS Force      Max Disp      RMS Disp   ~
	----------------------------------------------------------------------------------------------~
	  Convergence Criteria     1.00e-06 *    3.00e-04 *             o    1.20e-03 *             o~
	----------------------------------------------------------------------------------------------~
	     1     -39.99958185   -4.00e+01      4.34e-02      2.37e-02 o    2.03e-01      1.03e-01 o  ~
	----------------------------------------------------------------------------------------------

Next Geometry in Ang 
	Fragment 1 (Ang)

	    C   0.0000019808  -0.0000021937  -0.0000021399
	    H  -0.6275748759   0.9427425041  -0.0200015481
	    H  -0.3174192702  -0.9266847204  -0.5687768994
	    H  -0.0256953429  -0.3228875221   1.0854016787
	    H   0.9706875082   0.3068319320  -0.4966210912


    Structure for next step:
    Molecular point group: c1
    Full point group: C1

    Geometry (in Angstrom), charge = 1, multiplicity = 2:

    C            0.000001980781    -0.000002193669    -0.000002139913
    H           -0.627574875850     0.942742504125    -0.020001548068
    H           -0.317419270226    -0.926684720357    -0.568776899431
    H           -0.025695342932    -0.322887522080     1.085401678651
    H            0.970687508228     0.306831931981    -0.496621091238


Scratch directory: D:\BaiduSyncdisk\py\MS\psi4_scratch/
gradient() will perform analytic gradient computation.
   => Libint2 <=

    Primary   basis highest AM E, G, H:  6, 6, -
    Auxiliary basis highest AM E, G, H:  7, 7, -
    Onebody   basis highest AM E, G, H:  -, -, -
    Solid Harmonics ordering:            Gaussian

*** tstart() called on iCarus
*** at Sun Jun 29 21:43:33 2025

   => Loading Basis Set <=

    Name: 6-31G(D)
    Role: ORBITAL
    Keyword: BASIS
    atoms 1   entry C          line   111 file D:\anaconda3\envs\nistpy\Library\share\psi4\basis\6-31g_d_.gbs 
    atoms 2-5 entry H          line    44 file D:\anaconda3\envs\nistpy\Library\share\psi4\basis\6-31g_d_.gbs 


         ---------------------------------------------------------
                                   SCF
               by Justin Turney, Rob Parrish, Andy Simmonett
                          and Daniel G. A. Smith
                              UKS Reference
                       10 Threads,   -562 MiB Core
         ---------------------------------------------------------

  ==> Geometry <==

    Molecular point group: c1
    Full point group: C1

    Geometry (in Angstrom), charge = 1, multiplicity = 2:

       Center              X                  Y                   Z               Mass       
    ------------   -----------------  -----------------  -----------------  -----------------
         C            0.000001980781    -0.000002193669    -0.000002139913    12.000000000000
         H           -0.627574875850     0.942742504125    -0.020001548068     1.007825032230
         H           -0.317419270226    -0.926684720357    -0.568776899431     1.007825032230
         H           -0.025695342932    -0.322887522080     1.085401678651     1.007825032230
         H            0.970687508228     0.306831931981    -0.496621091238     1.007825032230

  Running in c1 symmetry.

  Rotational constants: A =      5.25010  B =      5.25009  C =      4.29760 [cm^-1]
  Rotational constants: A = 157394.07973  B = 157393.65840  C = 128838.71035 [MHz]
  Nuclear repulsion =   12.934697646339131

  Charge       = 1
  Multiplicity = 2
  Electrons    = 9
  Nalpha       = 5
  Nbeta        = 4

  ==> Algorithm <==

  SCF Algorithm Type is DF.
  DIIS enabled.
  MOM disabled.
  Fractional occupation disabled.
  Guess Type is READ.
  Energy threshold   = 1.00e-08
  Density threshold  = 1.00e-08
  Integral threshold = 1.00e-12

  ==> Primary Basis <==

  Basis Set: 6-31G(D)
    Blend: 6-31G(D)
    Number of shells: 14
    Number of basis functions: 23
    Number of Cartesian functions: 23
    Spherical Harmonics?: false
    Max angular momentum: 2

  ==> DFT Potential <==

   => LibXC <=

    Version 7.0.0
    S. Lehtola, C. Steigemann, M. J.T. Oliveira, and M. A.L. Marques.,  SoftwareX 7, 1–5 (2018) (10.1016/j.softx.2017.11.002)

   => Composite Functional: B3LYP <= 

    B3LYP Hyb-GGA Exchange-Correlation Functional

    P. J. Stephens, F. J. Devlin, C. F. Chabalowski, and M. J. Frisch.,  J. Phys. Chem. 98, 11623 (1994) (10.1021/j100096a001)

    Deriv               =              1
    GGA                 =           TRUE
    Meta                =          FALSE

    Exchange Hybrid     =           TRUE
    MP2 Hybrid          =          FALSE

   => Exchange Functionals <=

    0.0800   Slater exchange
    0.7200         Becke 88

   => Exact (HF) Exchange <=

    0.2000               HF 

   => Correlation Functionals <=

    0.1900   Vosko, Wilk & Nusair (VWN5_RPA)
    0.8100   Lee, Yang & Parr

   => LibXC Density Thresholds  <==

    XC_HYB_GGA_XC_B3LYP:  1.00E-15 

   => Molecular Quadrature <=

    Radial Scheme          =       TREUTLER
    Pruning Scheme         =           NONE
    Nuclear Scheme         =       TREUTLER

    Blocking Scheme        =         OCTREE
    BS radius alpha        =              1
    Pruning alpha          =              1
    Radial Points          =             75
    Spherical Points       =            302
    Total Points           =         109384
    Total Blocks           =            873
    Max Points             =            256
    Max Functions          =             23
    Weights Tolerance      =       1.00E-15

   => Loading Basis Set <=

    Name: (6-31G(D) AUX)
    Role: JKFIT
    Keyword: DF_BASIS_SCF
    atoms 1   entry C          line   121 file D:\anaconda3\envs\nistpy\Library\share\psi4\basis\cc-pvdz-jkfit.gbs 
    atoms 2-5 entry H          line    51 file D:\anaconda3\envs\nistpy\Library\share\psi4\basis\cc-pvdz-jkfit.gbs 

   => Loading Basis Set <=

    Name: 6-31G(D)
    Role: ORBITAL
    Keyword: BASIS
    atoms 1   entry C          line   111 file D:\anaconda3\envs\nistpy\Library\share\psi4\basis\6-31g_d_.gbs 
    atoms 2-5 entry H          line    44 file D:\anaconda3\envs\nistpy\Library\share\psi4\basis\6-31g_d_.gbs 

  Reading orbitals from file D:\BaiduSyncdisk\py\MS\psi4_scratch/psi4_calculation.default.34212.180.npy, no projection.

  ==> Integral Setup <==

  DFHelper Memory: AOs need 0.001 GiB; user supplied 5.515 GiB. 
  Using in-core AOs.

  ==> MemDFJK: Density-Fitted J/K Matrices <==

    J tasked:                   Yes
    K tasked:                   Yes
    wK tasked:                   No
    OpenMP threads:              10
    Memory [MiB]:              5647
    Algorithm:                 Core
    Schwarz Cutoff:           1E-12
    Mask sparsity (%):       0.0000
    Fitting Condition:        1E-10

   => Auxiliary Basis Set <=

  Basis Set: (6-31G(D) AUX)
    Blend: CC-PVDZ-JKFIT
    Number of shells: 60
    Number of basis functions: 181
    Number of Cartesian functions: 181
    Spherical Harmonics?: false
    Max angular momentum: 3

  Cached 100.0% of DFT collocation blocks in 0.073 [GiB].

  Minimum eigenvalue in the overlap matrix is 2.1108196343E-02.
  Reciprocal condition number of the overlap matrix is 3.8647928623E-03.
    Using symmetric orthogonalization.

  ==> Pre-Iterations <==

  SCF Guess: Orbitals guess was supplied from a previous computation.

   -------------------------------------------------------
    Irrep   Nso     Nmo     Nalpha   Nbeta   Ndocc  Nsocc
   -------------------------------------------------------
     A         23      23       5       4       4       1
   -------------------------------------------------------
    Total      23      23       5       4       4       1
   -------------------------------------------------------

  ==> Iterations <==

                           Total Energy        Delta E     RMS |[F,P]|

   @DF-UKS iter   0:   -40.00354564349358   -4.00035e+01   2.79966e-03 
   @DF-UKS iter   1:   -40.03461408255136   -3.10684e-02   4.38168e-04 ADIIS/DIIS
   @DF-UKS iter   2:   -40.03462107504356   -6.99249e-06   3.83820e-04 ADIIS/DIIS
   @DF-UKS iter   3:   -40.03466106517694   -3.99901e-05   2.26278e-05 DIIS
   @DF-UKS iter   4:   -40.03466123842152   -1.73245e-07   2.96871e-06 DIIS
   @DF-UKS iter   5:   -40.03466124486677   -6.44525e-09   5.56666e-07 DIIS
   @DF-UKS iter   6:   -40.03466124514431   -2.77545e-10   3.57975e-08 DIIS
   @DF-UKS iter   7:   -40.03466124514516   -8.45546e-13   4.72108e-09 DIIS
  Energy and wave function converged.


  ==> Post-Iterations <==

   Electrons on quadrature grid:
      Nalpha   =    5.0000001557 ; deviation = 1.557e-07
      Nbeta    =    4.0000003234 ; deviation = 3.234e-07
      Ntotal   =    9.0000004792 ; deviation = 4.792e-07 

   @Spin Contamination Metric:   1.801258820E-03
   @S^2 Expected:                7.500000000E-01
   @S^2 Observed:                7.518012588E-01
   @S   Expected:                5.000000000E-01
   @S   Observed:                5.000000000E-01

    Orbital Energies [Eh]
    ---------------------

    Alpha Occupied:                                                       

       1A    -10.573930     2A     -1.068660     3A     -0.769947  
       4A     -0.769946     5A     -0.741766  

    Alpha Virtual:                                                        

       6A     -0.184032     7A     -0.180610     8A     -0.127261  
       9A     -0.127261    10A      0.190346    11A      0.239503  
      12A      0.239503    13A      0.497374    14A      0.523062  
      15A      0.544039    16A      0.544039    17A      0.727036  
      18A      1.272293    19A      1.311670    20A      1.746863  
      21A      1.746865    22A      1.865546    23A      3.755442  

    Beta Occupied:                                                        

       1A    -10.565457     2A     -1.022765     3A     -0.743408  
       4A     -0.743408  

    Beta Virtual:                                                         

       5A     -0.597163     6A     -0.164332     7A     -0.150548  
       8A     -0.112280     9A     -0.112280    10A      0.227372  
      11A      0.251077    12A      0.251078    13A      0.532241  
      14A      0.563979    15A      0.569120    16A      0.569121  
      17A      0.742657    18A      1.275778    19A      1.382366  
      20A      1.793338    21A      1.793340    22A      1.876009  
      23A      3.780201  

    Final Occupation by Irrep:
              A 
    DOCC [     4 ]
    SOCC [     1 ]
    NA   [     5 ]
    NB   [     4 ]

  @DF-UKS Final Energy:   -40.03466124514516

   => Energetics <=

    Nuclear Repulsion Energy =             12.9346976463391314
    One-Electron Energy =                 -75.1496781017296769
    Two-Electron Energy =                  27.4435527216737292
    DFT Exchange-Correlation Energy =      -5.2632335114283491
    Empirical Dispersion Energy =           0.0000000000000000
    VV10 Nonlocal Energy =                  0.0000000000000000
    Total Energy =                        -40.0346612451451662

  UHF NO Occupations:
  HONO-2 :    3  A 1.9996915
  HONO-1 :    4  A 1.9996915
  HONO-0 :    5  A 1.0000000
  LUNO+0 :    6  A 0.0003085
  LUNO+1 :    7  A 0.0003085
  LUNO+2 :    8  A 0.0002835
  LUNO+3 :    9  A 0.0000002


Computation Completed


Properties will be evaluated at   0.000000,   0.000000,   0.000000 [a0]

Properties computed using the SCF density matrix


 Multipole Moments:

 ------------------------------------------------------------------------------------
     Multipole            Electronic (a.u.)      Nuclear  (a.u.)        Total (a.u.)
 ------------------------------------------------------------------------------------

 L = 1.  Multiply by 2.5417464519 to convert [e a0] to [Debye]
 Dipole X            :         -0.0000238            0.0000187           -0.0000051
 Dipole Y            :          0.0000294           -0.0000207            0.0000087
 Dipole Z            :          0.0000297           -0.0000202            0.0000095
 Magnitude           :                                                    0.0000139

 ------------------------------------------------------------------------------------

*** tstop() called on iCarus at Sun Jun 29 21:43:33 2025
Module time:
	user time   =        nan seconds =        nan minutes
	system time =        nan seconds =        nan minutes
	total time  =          0 seconds =       0.00 minutes
Total time:
	user time   =        nan seconds =        nan minutes
	system time =        nan seconds =        nan minutes
	total time  =          3 seconds =       0.05 minutes

*** tstart() called on iCarus
*** at Sun Jun 29 21:43:33 2025


         ------------------------------------------------------------
                                   SCF GRAD                          
                          Rob Parrish, Justin Turney,                
                       Andy Simmonett, and Alex Sokolov              
         ------------------------------------------------------------

  ==> Geometry <==

    Molecular point group: c1
    Full point group: C1

    Geometry (in Angstrom), charge = 1, multiplicity = 2:

       Center              X                  Y                   Z               Mass       
    ------------   -----------------  -----------------  -----------------  -----------------
         C            0.000001980781    -0.000002193669    -0.000002139913    12.000000000000
         H           -0.627574875850     0.942742504125    -0.020001548068     1.007825032230
         H           -0.317419270226    -0.926684720357    -0.568776899431     1.007825032230
         H           -0.025695342932    -0.322887522080     1.085401678651     1.007825032230
         H            0.970687508228     0.306831931981    -0.496621091238     1.007825032230

  Nuclear repulsion =   12.934697646339131

  ==> Basis Set <==

  Basis Set: 6-31G(D)
    Blend: 6-31G(D)
    Number of shells: 14
    Number of basis functions: 23
    Number of Cartesian functions: 23
    Spherical Harmonics?: false
    Max angular momentum: 2

  ==> DFJKGrad: Density-Fitted SCF Gradients <==

    Gradient:                    1
    J tasked:                  Yes
    K tasked:                  Yes
    wK tasked:                  No
    OpenMP threads:             10
    Integrals threads:          10
    Memory [MiB]:             -422
    Schwarz Cutoff:          1E-12
    Fitting Condition:       1E-10

   => Auxiliary Basis Set <=

  Basis Set: (6-31G(D) AUX)
    Blend: CC-PVDZ-JKFIT
    Number of shells: 60
    Number of basis functions: 181
    Number of Cartesian functions: 181
    Spherical Harmonics?: false
    Max angular momentum: 3

  ==> DFT Potential <==

   => LibXC <=

    Version 7.0.0
    S. Lehtola, C. Steigemann, M. J.T. Oliveira, and M. A.L. Marques.,  SoftwareX 7, 1–5 (2018) (10.1016/j.softx.2017.11.002)

   => Composite Functional: B3LYP <= 

    B3LYP Hyb-GGA Exchange-Correlation Functional

    P. J. Stephens, F. J. Devlin, C. F. Chabalowski, and M. J. Frisch.,  J. Phys. Chem. 98, 11623 (1994) (10.1021/j100096a001)

    Deriv               =              1
    GGA                 =           TRUE
    Meta                =          FALSE

    Exchange Hybrid     =           TRUE
    MP2 Hybrid          =          FALSE

   => Exchange Functionals <=

    0.0800   Slater exchange
    0.7200         Becke 88

   => Exact (HF) Exchange <=

    0.2000               HF 

   => Correlation Functionals <=

    0.1900   Vosko, Wilk & Nusair (VWN5_RPA)
    0.8100   Lee, Yang & Parr

   => LibXC Density Thresholds  <==

    XC_HYB_GGA_XC_B3LYP:  1.00E-15 

   => Molecular Quadrature <=

    Radial Scheme          =       TREUTLER
    Pruning Scheme         =           NONE
    Nuclear Scheme         =       TREUTLER

    Blocking Scheme        =         OCTREE
    BS radius alpha        =              1
    Pruning alpha          =              1
    Radial Points          =             75
    Spherical Points       =            302
    Total Points           =         109384
    Total Blocks           =            873
    Max Points             =            256
    Max Functions          =             23
    Weights Tolerance      =       1.00E-15


  -Total Gradient:
     Atom            X                  Y                   Z
    ------   -----------------  -----------------  -----------------
       1        0.000000025080    -0.000000635203    -0.000000104482
       2       -0.016458689756    -0.011797723488    -0.015062140727
       3       -0.020476254569     0.012427168060    -0.007950266020
       4        0.024923320550     0.003765144881     0.001256457084
       5        0.012011598646    -0.004393954047     0.021756054208


*** tstop() called on iCarus at Sun Jun 29 21:43:33 2025
Module time:
	user time   =        nan seconds =        nan minutes
	system time =        nan seconds =        nan minutes
	total time  =          0 seconds =       0.00 minutes
Total time:
	user time   =        nan seconds =        nan minutes
	system time =        nan seconds =        nan minutes
	total time  =          3 seconds =       0.05 minutes

    			-----------------------------------------

    			 OPTKING 3.0: for geometry optimizations 

    			     By R.A. King, Bethel University     

    			        with contributions from          

    			    A.V. Copan, J. Cayton, A. Heide      

    			-----------------------------------------

    
	                            ===> Fragment 1 <== 

	 Z (Atomic Numbers)          Masses                          Geom                  
	       6.000000            12.000000          0.00000374 -0.00000415 -0.00000404   
	       1.000000             1.007825         -1.18594464  1.78152514 -0.03779745   
	       1.000000             1.007825         -0.59983549 -1.75118033 -1.07483257   
	       1.000000             1.007825         -0.04855716 -0.61016899  2.05111191   
	       1.000000             1.007825          1.83433354  0.57982832 -0.93847785   

	 - Coordinate -           - BOHR/RAD -       - ANG/DEG -
	 R(1,2)           =         2.140502           1.132705
	 R(1,3)           =         2.140486           1.132697
	 R(1,4)           =         2.140499           1.132703
	 R(1,5)           =         2.140492           1.132699
	 B(2,1,3)         =         2.113890         121.116992
	 B(2,1,4)         =         1.814812         103.981064
	 B(2,1,5)         =         1.814815         103.981234
	 B(3,1,4)         =         1.814816         103.981279
	 B(3,1,5)         =         1.814823         103.981719
	 B(4,1,5)         =         2.113894         121.117198
	 O(2,1,3,4)       =         0.876210          50.203134
	 O(2,1,3,5)       =        -0.876204         -50.202797
	 O(2,1,4,5)       =         1.056952          60.558893
	 O(3,1,2,4)       =        -0.876209         -50.203069
	 O(3,1,2,5)       =         0.876202          50.202653
	 O(3,1,4,5)       =        -1.056938         -60.558099
	 O(4,1,2,3)       =         1.056953          60.558944
	 O(4,1,2,5)       =        -0.876207         -50.202936
	 O(4,1,3,5)       =         0.876200          50.202543
	 O(5,1,2,3)       =        -1.056941         -60.558254
	 O(5,1,2,4)       =         0.876206          50.202885
	 O(5,1,3,4)       =        -0.876197         -50.202412


	        --- Internal Coordinate Step in ANG or DEG, aJ/ANG or AJ/DEG ---
	-------------------------------------------------------------------------------
	           Coordinate      Previous         Force          Change          New 
	           ----------      --------        ------          ------        ------
	               R(1,2)       1.13270        0.00358       -0.00072       1.13198
	               R(1,3)       1.13270        0.00360       -0.00072       1.13198
	               R(1,4)       1.13270        0.00358       -0.00072       1.13198
	               R(1,5)       1.13270        0.00359       -0.00072       1.13198
	             B(2,1,3)     121.11699        0.00139        9.28029     130.39728
	             B(2,1,4)     103.98106       -0.00061       -3.84710     100.13396
	             B(2,1,5)     103.98123       -0.00061       -3.84689     100.13435
	             B(3,1,4)     103.98128       -0.00061       -3.84680     100.13448
	             B(3,1,5)     103.98172       -0.00061       -3.84691     100.13481
	             B(4,1,5)     121.11720        0.00139        9.28019     130.39738
	           O(2,1,3,4)      50.20313       -0.00072       -5.59227      44.61086
	           O(2,1,3,5)     -50.20280        0.00072        5.59230     -44.61050
	           O(2,1,4,5)      60.55889        0.00069        4.64038      65.19927
	           O(3,1,2,4)     -50.20307        0.00072        5.59230     -44.61077
	           O(3,1,2,5)      50.20265       -0.00072       -5.59224      44.61041
	           O(3,1,4,5)     -60.55810       -0.00069       -4.63991     -65.19801
	           O(4,1,2,3)      60.55894        0.00069        4.64021      65.19916
	           O(4,1,2,5)     -50.20294        0.00072        5.59213     -44.61080
	           O(4,1,3,5)      50.20254       -0.00072       -5.59223      44.61031
	           O(5,1,2,3)     -60.55825       -0.00069       -4.63997     -65.19823
	           O(5,1,2,4)      50.20289       -0.00072       -5.59215      44.61074
	           O(5,1,3,4)     -50.20241        0.00072        5.59216     -44.61025
	-------------------------------------------------------------------------------

	                                 ==> Convergence Check <==                                  
    
	Measures of convergence in internal coordinates in au.
    
	Criteria marked as inactive (o), active & met (*), and active & unmet ( ).

	----------------------------------------------------------------------------------------------
	   Step    Total Energy     Delta E     Max Force     RMS Force      Max Disp      RMS Disp   
	----------------------------------------------------------------------------------------------
	  Convergence Criteria     1.00e-06 *    3.00e-04 *             o    1.20e-03 *             o
	----------------------------------------------------------------------------------------------
	     2     -40.03466125   -3.51e-02      1.82e-02      9.47e-03 o    1.62e-01      8.87e-02 o  ~
	----------------------------------------------------------------------------------------------

Next Geometry in Ang 
	Fragment 1 (Ang)

	    C   0.0000017195  -0.0000018197  -0.0000020448
	    H  -0.5645179787   0.9805566130   0.0347662571
	    H  -0.2414245880  -0.9668653932  -0.5369083774
	    H  -0.1160063766  -0.3348395439   1.0750844324
	    H   0.9219472238   0.3211501438  -0.5729402673


    Structure for next step:
    Molecular point group: c1
    Full point group: C1

    Geometry (in Angstrom), charge = 1, multiplicity = 2:

    C            0.000001719515    -0.000001819747    -0.000002044840
    H           -0.564517978749     0.980556613022     0.034766257140
    H           -0.241424588007    -0.966865393183    -0.536908377438
    H           -0.116006376586    -0.334839543860     1.075084432432
    H            0.921947223827     0.321150143768    -0.572940267294


Scratch directory: D:\BaiduSyncdisk\py\MS\psi4_scratch/
gradient() will perform analytic gradient computation.
   => Libint2 <=

    Primary   basis highest AM E, G, H:  6, 6, -
    Auxiliary basis highest AM E, G, H:  7, 7, -
    Onebody   basis highest AM E, G, H:  -, -, -
    Solid Harmonics ordering:            Gaussian

*** tstart() called on iCarus
*** at Sun Jun 29 21:43:33 2025

   => Loading Basis Set <=

    Name: 6-31G(D)
    Role: ORBITAL
    Keyword: BASIS
    atoms 1   entry C          line   111 file D:\anaconda3\envs\nistpy\Library\share\psi4\basis\6-31g_d_.gbs 
    atoms 2-5 entry H          line    44 file D:\anaconda3\envs\nistpy\Library\share\psi4\basis\6-31g_d_.gbs 


         ---------------------------------------------------------
                                   SCF
               by Justin Turney, Rob Parrish, Andy Simmonett
                          and Daniel G. A. Smith
                              UKS Reference
                       10 Threads,   -562 MiB Core
         ---------------------------------------------------------

  ==> Geometry <==

    Molecular point group: c1
    Full point group: C1

    Geometry (in Angstrom), charge = 1, multiplicity = 2:

       Center              X                  Y                   Z               Mass       
    ------------   -----------------  -----------------  -----------------  -----------------
         C            0.000001719515    -0.000001819747    -0.000002044840    12.000000000000
         H           -0.564517978749     0.980556613022     0.034766257140     1.007825032230
         H           -0.241424588007    -0.966865393183    -0.536908377438     1.007825032230
         H           -0.116006376586    -0.334839543860     1.075084432432     1.007825032230
         H            0.921947223827     0.321150143768    -0.572940267294     1.007825032230

  Running in c1 symmetry.

  Rotational constants: A =      5.55023  B =      5.55022  C =      3.96025 [cm^-1]
  Rotational constants: A = 166391.63714  B = 166391.55706  C = 118725.40505 [MHz]
  Nuclear repulsion =   12.953761038262051

  Charge       = 1
  Multiplicity = 2
  Electrons    = 9
  Nalpha       = 5
  Nbeta        = 4

  ==> Algorithm <==

  SCF Algorithm Type is DF.
  DIIS enabled.
  MOM disabled.
  Fractional occupation disabled.
  Guess Type is READ.
  Energy threshold   = 1.00e-08
  Density threshold  = 1.00e-08
  Integral threshold = 1.00e-12

  ==> Primary Basis <==

  Basis Set: 6-31G(D)
    Blend: 6-31G(D)
    Number of shells: 14
    Number of basis functions: 23
    Number of Cartesian functions: 23
    Spherical Harmonics?: false
    Max angular momentum: 2

  ==> DFT Potential <==

   => LibXC <=

    Version 7.0.0
    S. Lehtola, C. Steigemann, M. J.T. Oliveira, and M. A.L. Marques.,  SoftwareX 7, 1–5 (2018) (10.1016/j.softx.2017.11.002)

   => Composite Functional: B3LYP <= 

    B3LYP Hyb-GGA Exchange-Correlation Functional

    P. J. Stephens, F. J. Devlin, C. F. Chabalowski, and M. J. Frisch.,  J. Phys. Chem. 98, 11623 (1994) (10.1021/j100096a001)

    Deriv               =              1
    GGA                 =           TRUE
    Meta                =          FALSE

    Exchange Hybrid     =           TRUE
    MP2 Hybrid          =          FALSE

   => Exchange Functionals <=

    0.0800   Slater exchange
    0.7200         Becke 88

   => Exact (HF) Exchange <=

    0.2000               HF 

   => Correlation Functionals <=

    0.1900   Vosko, Wilk & Nusair (VWN5_RPA)
    0.8100   Lee, Yang & Parr

   => LibXC Density Thresholds  <==

    XC_HYB_GGA_XC_B3LYP:  1.00E-15 

   => Molecular Quadrature <=

    Radial Scheme          =       TREUTLER
    Pruning Scheme         =           NONE
    Nuclear Scheme         =       TREUTLER

    Blocking Scheme        =         OCTREE
    BS radius alpha        =              1
    Pruning alpha          =              1
    Radial Points          =             75
    Spherical Points       =            302
    Total Points           =         109408
    Total Blocks           =            859
    Max Points             =            256
    Max Functions          =             23
    Weights Tolerance      =       1.00E-15

   => Loading Basis Set <=

    Name: (6-31G(D) AUX)
    Role: JKFIT
    Keyword: DF_BASIS_SCF
    atoms 1   entry C          line   121 file D:\anaconda3\envs\nistpy\Library\share\psi4\basis\cc-pvdz-jkfit.gbs 
    atoms 2-5 entry H          line    51 file D:\anaconda3\envs\nistpy\Library\share\psi4\basis\cc-pvdz-jkfit.gbs 

   => Loading Basis Set <=

    Name: 6-31G(D)
    Role: ORBITAL
    Keyword: BASIS
    atoms 1   entry C          line   111 file D:\anaconda3\envs\nistpy\Library\share\psi4\basis\6-31g_d_.gbs 
    atoms 2-5 entry H          line    44 file D:\anaconda3\envs\nistpy\Library\share\psi4\basis\6-31g_d_.gbs 

  Reading orbitals from file D:\BaiduSyncdisk\py\MS\psi4_scratch/psi4_calculation.default.34212.180.npy, no projection.

  ==> Integral Setup <==

  DFHelper Memory: AOs need 0.001 GiB; user supplied 5.515 GiB. 
  Using in-core AOs.

  ==> MemDFJK: Density-Fitted J/K Matrices <==

    J tasked:                   Yes
    K tasked:                   Yes
    wK tasked:                   No
    OpenMP threads:              10
    Memory [MiB]:              5647
    Algorithm:                 Core
    Schwarz Cutoff:           1E-12
    Mask sparsity (%):       0.0000
    Fitting Condition:        1E-10

   => Auxiliary Basis Set <=

  Basis Set: (6-31G(D) AUX)
    Blend: CC-PVDZ-JKFIT
    Number of shells: 60
    Number of basis functions: 181
    Number of Cartesian functions: 181
    Spherical Harmonics?: false
    Max angular momentum: 3

  Cached 100.0% of DFT collocation blocks in 0.073 [GiB].

  Minimum eigenvalue in the overlap matrix is 2.2505125655E-02.
  Reciprocal condition number of the overlap matrix is 4.1128191198E-03.
    Using symmetric orthogonalization.

  ==> Pre-Iterations <==

  SCF Guess: Orbitals guess was supplied from a previous computation.

   -------------------------------------------------------
    Irrep   Nso     Nmo     Nalpha   Nbeta   Ndocc  Nsocc
   -------------------------------------------------------
     A         23      23       5       4       4       1
   -------------------------------------------------------
    Total      23      23       5       4       4       1
   -------------------------------------------------------

  ==> Iterations <==

                           Total Energy        Delta E     RMS |[F,P]|

   @DF-UKS iter   0:   -40.06603079760948   -4.00660e+01   1.25754e-03 
   @DF-UKS iter   1:   -40.04756491538665    1.84659e-02   2.48569e-04 ADIIS/DIIS
   @DF-UKS iter   2:   -40.04757442350181   -9.50812e-06   2.25680e-04 ADIIS/DIIS
   @DF-UKS iter   3:   -40.04758828856176   -1.38651e-05   1.74551e-05 DIIS
   @DF-UKS iter   4:   -40.04758849586636   -2.07305e-07   4.92001e-06 DIIS
   @DF-UKS iter   5:   -40.04758851779263   -2.19263e-08   6.38494e-07 DIIS
   @DF-UKS iter   6:   -40.04758851811934   -3.26708e-10   4.05627e-08 DIIS
   @DF-UKS iter   7:   -40.04758851812020   -8.59757e-13   3.12806e-09 DIIS
  Energy and wave function converged.


  ==> Post-Iterations <==

   Electrons on quadrature grid:
      Nalpha   =    4.9999989224 ; deviation = -1.078e-06
      Nbeta    =    3.9999990793 ; deviation = -9.207e-07
      Ntotal   =    8.9999980016 ; deviation = -1.998e-06 

   @Spin Contamination Metric:   1.408437787E-03
   @S^2 Expected:                7.500000000E-01
   @S^2 Observed:                7.514084378E-01
   @S   Expected:                5.000000000E-01
   @S   Observed:                5.000000000E-01

    Orbital Energies [Eh]
    ---------------------

    Alpha Occupied:                                                       

       1A    -10.572453     2A     -1.068588     3A     -0.780344  
       4A     -0.780343     5A     -0.709182  

    Alpha Virtual:                                                        

       6A     -0.215541     7A     -0.184425     8A     -0.114262  
       9A     -0.114261    10A      0.189438    11A      0.264249  
      12A      0.264249    13A      0.478048    14A      0.507503  
      15A      0.556396    16A      0.556396    17A      0.680305  
      18A      1.263072    19A      1.411836    20A      1.687132  
      21A      1.687132    22A      1.916633    23A      3.741515  

    Beta Occupied:                                                        

       1A    -10.563444     2A     -1.024283     3A     -0.754694  
       4A     -0.754694  

    Beta Virtual:                                                         

       5A     -0.565085     6A     -0.181771     7A     -0.167170  
       8A     -0.100415     9A     -0.100414    10A      0.229854  
      11A      0.276194    12A      0.276194    13A      0.508844  
      14A      0.549272    15A      0.581244    16A      0.581244  
      17A      0.698158    18A      1.267543    19A      1.482050  
      20A      1.734513    21A      1.734514    22A      1.928388  
      23A      3.767576  

    Final Occupation by Irrep:
              A 
    DOCC [     4 ]
    SOCC [     1 ]
    NA   [     5 ]
    NB   [     4 ]

  @DF-UKS Final Energy:   -40.04758851812020

   => Energetics <=

    Nuclear Repulsion Energy =             12.9537610382620514
    One-Electron Energy =                 -75.2042022706215079
    Two-Electron Energy =                  27.4673797155030428
    DFT Exchange-Correlation Energy =      -5.2645270012637884
    Empirical Dispersion Energy =           0.0000000000000000
    VV10 Nonlocal Energy =                  0.0000000000000000
    Total Energy =                        -40.0475885181202003

  UHF NO Occupations:
  HONO-2 :    3  A 1.9997907
  HONO-1 :    4  A 1.9997145
  HONO-0 :    5  A 1.0000000
  LUNO+0 :    6  A 0.0002855
  LUNO+1 :    7  A 0.0002093
  LUNO+2 :    8  A 0.0002093
  LUNO+3 :    9  A 0.0000002


Computation Completed


Properties will be evaluated at   0.000000,   0.000000,   0.000000 [a0]

Properties computed using the SCF density matrix


 Multipole Moments:

 ------------------------------------------------------------------------------------
     Multipole            Electronic (a.u.)      Nuclear  (a.u.)        Total (a.u.)
 ------------------------------------------------------------------------------------

 L = 1.  Multiply by 2.5417464519 to convert [e a0] to [Debye]
 Dipole X            :         -0.0000198            0.0000162           -0.0000036
 Dipole Y            :          0.0000218           -0.0000172            0.0000046
 Dipole Z            :          0.0000250           -0.0000193            0.0000057
 Magnitude           :                                                    0.0000081

 ------------------------------------------------------------------------------------

*** tstop() called on iCarus at Sun Jun 29 21:43:33 2025
Module time:
	user time   =        nan seconds =        nan minutes
	system time =        nan seconds =        nan minutes
	total time  =          0 seconds =       0.00 minutes
Total time:
	user time   =        nan seconds =        nan minutes
	system time =        nan seconds =        nan minutes
	total time  =          3 seconds =       0.05 minutes

*** tstart() called on iCarus
*** at Sun Jun 29 21:43:33 2025


         ------------------------------------------------------------
                                   SCF GRAD                          
                          Rob Parrish, Justin Turney,                
                       Andy Simmonett, and Alex Sokolov              
         ------------------------------------------------------------

  ==> Geometry <==

    Molecular point group: c1
    Full point group: C1

    Geometry (in Angstrom), charge = 1, multiplicity = 2:

       Center              X                  Y                   Z               Mass       
    ------------   -----------------  -----------------  -----------------  -----------------
         C            0.000001719515    -0.000001819747    -0.000002044840    12.000000000000
         H           -0.564517978749     0.980556613022     0.034766257140     1.007825032230
         H           -0.241424588007    -0.966865393183    -0.536908377438     1.007825032230
         H           -0.116006376586    -0.334839543860     1.075084432432     1.007825032230
         H            0.921947223827     0.321150143768    -0.572940267294     1.007825032230

  Nuclear repulsion =   12.953761038262051

  ==> Basis Set <==

  Basis Set: 6-31G(D)
    Blend: 6-31G(D)
    Number of shells: 14
    Number of basis functions: 23
    Number of Cartesian functions: 23
    Spherical Harmonics?: false
    Max angular momentum: 2

  ==> DFJKGrad: Density-Fitted SCF Gradients <==

    Gradient:                    1
    J tasked:                  Yes
    K tasked:                  Yes
    wK tasked:                  No
    OpenMP threads:             10
    Integrals threads:          10
    Memory [MiB]:             -422
    Schwarz Cutoff:          1E-12
    Fitting Condition:       1E-10

   => Auxiliary Basis Set <=

  Basis Set: (6-31G(D) AUX)
    Blend: CC-PVDZ-JKFIT
    Number of shells: 60
    Number of basis functions: 181
    Number of Cartesian functions: 181
    Spherical Harmonics?: false
    Max angular momentum: 3

  ==> DFT Potential <==

   => LibXC <=

    Version 7.0.0
    S. Lehtola, C. Steigemann, M. J.T. Oliveira, and M. A.L. Marques.,  SoftwareX 7, 1–5 (2018) (10.1016/j.softx.2017.11.002)

   => Composite Functional: B3LYP <= 

    B3LYP Hyb-GGA Exchange-Correlation Functional

    P. J. Stephens, F. J. Devlin, C. F. Chabalowski, and M. J. Frisch.,  J. Phys. Chem. 98, 11623 (1994) (10.1021/j100096a001)

    Deriv               =              1
    GGA                 =           TRUE
    Meta                =          FALSE

    Exchange Hybrid     =           TRUE
    MP2 Hybrid          =          FALSE

   => Exchange Functionals <=

    0.0800   Slater exchange
    0.7200         Becke 88

   => Exact (HF) Exchange <=

    0.2000               HF 

   => Correlation Functionals <=

    0.1900   Vosko, Wilk & Nusair (VWN5_RPA)
    0.8100   Lee, Yang & Parr

   => LibXC Density Thresholds  <==

    XC_HYB_GGA_XC_B3LYP:  1.00E-15 

   => Molecular Quadrature <=

    Radial Scheme          =       TREUTLER
    Pruning Scheme         =           NONE
    Nuclear Scheme         =       TREUTLER

    Blocking Scheme        =         OCTREE
    BS radius alpha        =              1
    Pruning alpha          =              1
    Radial Points          =             75
    Spherical Points       =            302
    Total Points           =         109408
    Total Blocks           =            859
    Max Points             =            256
    Max Functions          =             23
    Weights Tolerance      =       1.00E-15


  -Total Gradient:
     Atom            X                  Y                   Z
    ------   -----------------  -----------------  -----------------
       1        0.000000358601    -0.000000192444    -0.000000203164
       2       -0.009760064579    -0.002710918699    -0.007226630184
       3       -0.010717581723     0.003059209233    -0.005531960054
       4        0.011776466343     0.000798239303     0.003937930168
       5        0.008700821285    -0.001146337321     0.008820863307


*** tstop() called on iCarus at Sun Jun 29 21:43:33 2025
Module time:
	user time   =        nan seconds =        nan minutes
	system time =        nan seconds =        nan minutes
	total time  =          0 seconds =       0.00 minutes
Total time:
	user time   =        nan seconds =        nan minutes
	system time =        nan seconds =        nan minutes
	total time  =          3 seconds =       0.05 minutes

    			-----------------------------------------

    			 OPTKING 3.0: for geometry optimizations 

    			     By R.A. King, Bethel University     

    			        with contributions from          

    			    A.V. Copan, J. Cayton, A. Heide      

    			-----------------------------------------

    
	                            ===> Fragment 1 <== 

	 Z (Atomic Numbers)          Masses                          Geom                  
	       6.000000            12.000000          0.00000325 -0.00000344 -0.00000386   
	       1.000000             1.007825         -1.06678437  1.85298345  0.06569870   
	       1.000000             1.007825         -0.45622635 -1.82711079 -1.01460979   
	       1.000000             1.007825         -0.21922028 -0.63275503  2.03161514   
	       1.000000             1.007825          1.74222776  0.60688582 -1.08270019   

	 - Coordinate -           - BOHR/RAD -       - ANG/DEG -
	 R(1,2)           =         2.139138           1.131983
	 R(1,3)           =         2.139133           1.131981
	 R(1,4)           =         2.139138           1.131983
	 R(1,5)           =         2.139134           1.131981
	 B(2,1,3)         =         2.275862         130.397281
	 B(2,1,4)         =         1.747667         100.133964
	 B(2,1,5)         =         1.747674         100.134349
	 B(3,1,4)         =         1.747676         100.134476
	 B(3,1,5)         =         1.747682         100.134811
	 B(4,1,5)         =         2.275864         130.397383
	 O(2,1,3,4)       =         0.778606          44.610861
	 O(2,1,3,5)       =        -0.778600         -44.610496
	 O(2,1,4,5)       =         1.137942          65.199271
	 O(3,1,2,4)       =        -0.778605         -44.610770
	 O(3,1,2,5)       =         0.778599          44.610414
	 O(3,1,4,5)       =        -1.137920         -65.198010
	 O(4,1,2,3)       =         1.137940          65.199157
	 O(4,1,2,5)       =        -0.778605         -44.610804
	 O(4,1,3,5)       =         0.778597          44.610311
	 O(5,1,2,3)       =        -1.137924         -65.198227
	 O(5,1,2,4)       =         0.778604          44.610736
	 O(5,1,3,4)       =        -0.778596         -44.610252


	        --- Internal Coordinate Step in ANG or DEG, aJ/ANG or AJ/DEG ---
	-------------------------------------------------------------------------------
	           Coordinate      Previous         Force          Change          New 
	           ----------      --------        ------          ------        ------
	               R(1,2)       1.13198       -0.01893       -0.00536       1.12662
	               R(1,3)       1.13198       -0.01892       -0.00536       1.12662
	               R(1,4)       1.13198       -0.01892       -0.00536       1.12662
	               R(1,5)       1.13198       -0.01892       -0.00536       1.12662
	             B(2,1,3)     130.39728        0.00055        5.74704     136.14432
	             B(2,1,4)     100.13396       -0.00021       -2.11801      98.01595
	             B(2,1,5)     100.13435       -0.00021       -2.11800      98.01635
	             B(3,1,4)     100.13448       -0.00021       -2.11801      98.01647
	             B(3,1,5)     100.13481       -0.00021       -2.11805      98.01676
	             B(4,1,5)     130.39738        0.00055        5.74699     136.14437
	           O(2,1,3,4)      44.61086       -0.00037       -4.13969      40.47117
	           O(2,1,3,5)     -44.61050        0.00037        4.13963     -40.47087
	           O(2,1,4,5)      65.19927        0.00027        2.87355      68.07282
	           O(3,1,2,4)     -44.61077        0.00037        4.13967     -40.47110
	           O(3,1,2,5)      44.61041       -0.00037       -4.13960      40.47082
	           O(3,1,4,5)     -65.19801       -0.00027       -2.87349     -68.07150
	           O(4,1,2,3)      65.19916        0.00027        2.87352      68.07267
	           O(4,1,2,5)     -44.61080        0.00037        4.13964     -40.47116
	           O(4,1,3,5)      44.61031       -0.00037       -4.13955      40.47076
	           O(5,1,2,3)     -65.19823       -0.00027       -2.87347     -68.07169
	           O(5,1,2,4)      44.61074       -0.00037       -4.13962      40.47111
	           O(5,1,3,4)     -44.61025        0.00037        4.13953     -40.47072
	-------------------------------------------------------------------------------

	                                 ==> Convergence Check <==                                  
    
	Measures of convergence in internal coordinates in au.
    
	Criteria marked as inactive (o), active & met (*), and active & unmet ( ).

	----------------------------------------------------------------------------------------------
	   Step    Total Energy     Delta E     Max Force     RMS Force      Max Disp      RMS Disp   
	----------------------------------------------------------------------------------------------
	  Convergence Criteria     1.00e-06 *    3.00e-04 *             o    1.20e-03 *             o
	----------------------------------------------------------------------------------------------
	     3     -40.04758852   -1.29e-02      7.17e-03      4.24e-03 o    1.00e-01      5.95e-02 o  ~
	----------------------------------------------------------------------------------------------

Next Geometry in Ang 
	Fragment 1 (Ang)

	    C   0.0000012949  -0.0000013825  -0.0000017243
	    H  -0.5213519376   0.9963950807   0.0682576935
	    H  -0.1927447121  -0.9842655041  -0.5131750762
	    H  -0.1707871616  -0.3396569381   1.0605358174
	    H   0.8848825164   0.3275287439  -0.6156167104


    Structure for next step:
    Molecular point group: c1
    Full point group: C1

    Geometry (in Angstrom), charge = 1, multiplicity = 2:

    C            0.000001294896    -0.000001382474    -0.000001724267
    H           -0.521351937647     0.996395080735     0.068257693497
    H           -0.192744712092    -0.984265504093    -0.513175076239
    H           -0.170787161600    -0.339656938083     1.060535817441
    H            0.884882516444     0.327528743915    -0.615616710433


Scratch directory: D:\BaiduSyncdisk\py\MS\psi4_scratch/
gradient() will perform analytic gradient computation.
   => Libint2 <=

    Primary   basis highest AM E, G, H:  6, 6, -
    Auxiliary basis highest AM E, G, H:  7, 7, -
    Onebody   basis highest AM E, G, H:  -, -, -
    Solid Harmonics ordering:            Gaussian

*** tstart() called on iCarus
*** at Sun Jun 29 21:43:34 2025

   => Loading Basis Set <=

    Name: 6-31G(D)
    Role: ORBITAL
    Keyword: BASIS
    atoms 1   entry C          line   111 file D:\anaconda3\envs\nistpy\Library\share\psi4\basis\6-31g_d_.gbs 
    atoms 2-5 entry H          line    44 file D:\anaconda3\envs\nistpy\Library\share\psi4\basis\6-31g_d_.gbs 


         ---------------------------------------------------------
                                   SCF
               by Justin Turney, Rob Parrish, Andy Simmonett
                          and Daniel G. A. Smith
                              UKS Reference
                       10 Threads,   -562 MiB Core
         ---------------------------------------------------------

  ==> Geometry <==

    Molecular point group: c1
    Full point group: C1

    Geometry (in Angstrom), charge = 1, multiplicity = 2:

       Center              X                  Y                   Z               Mass       
    ------------   -----------------  -----------------  -----------------  -----------------
         C            0.000001294896    -0.000001382474    -0.000001724267    12.000000000000
         H           -0.521351937647     0.996395080735     0.068257693497     1.007825032230
         H           -0.192744712092    -0.984265504093    -0.513175076239     1.007825032230
         H           -0.170787161600    -0.339656938083     1.060535817441     1.007825032230
         H            0.884882516444     0.327528743915    -0.615616710433     1.007825032230

  Running in c1 symmetry.

  Rotational constants: A =      5.78267  B =      5.78267  C =      3.82845 [cm^-1]
  Rotational constants: A = 173360.15118  B = 173360.01160  C = 114774.06663 [MHz]
  Nuclear repulsion =   13.023780369110353

  Charge       = 1
  Multiplicity = 2
  Electrons    = 9
  Nalpha       = 5
  Nbeta        = 4

  ==> Algorithm <==

  SCF Algorithm Type is DF.
  DIIS enabled.
  MOM disabled.
  Fractional occupation disabled.
  Guess Type is READ.
  Energy threshold   = 1.00e-08
  Density threshold  = 1.00e-08
  Integral threshold = 1.00e-12

  ==> Primary Basis <==

  Basis Set: 6-31G(D)
    Blend: 6-31G(D)
    Number of shells: 14
    Number of basis functions: 23
    Number of Cartesian functions: 23
    Spherical Harmonics?: false
    Max angular momentum: 2

  ==> DFT Potential <==

   => LibXC <=

    Version 7.0.0
    S. Lehtola, C. Steigemann, M. J.T. Oliveira, and M. A.L. Marques.,  SoftwareX 7, 1–5 (2018) (10.1016/j.softx.2017.11.002)

   => Composite Functional: B3LYP <= 

    B3LYP Hyb-GGA Exchange-Correlation Functional

    P. J. Stephens, F. J. Devlin, C. F. Chabalowski, and M. J. Frisch.,  J. Phys. Chem. 98, 11623 (1994) (10.1021/j100096a001)

    Deriv               =              1
    GGA                 =           TRUE
    Meta                =          FALSE

    Exchange Hybrid     =           TRUE
    MP2 Hybrid          =          FALSE

   => Exchange Functionals <=

    0.0800   Slater exchange
    0.7200         Becke 88

   => Exact (HF) Exchange <=

    0.2000               HF 

   => Correlation Functionals <=

    0.1900   Vosko, Wilk & Nusair (VWN5_RPA)
    0.8100   Lee, Yang & Parr

   => LibXC Density Thresholds  <==

    XC_HYB_GGA_XC_B3LYP:  1.00E-15 

   => Molecular Quadrature <=

    Radial Scheme          =       TREUTLER
    Pruning Scheme         =           NONE
    Nuclear Scheme         =       TREUTLER

    Blocking Scheme        =         OCTREE
    BS radius alpha        =              1
    Pruning alpha          =              1
    Radial Points          =             75
    Spherical Points       =            302
    Total Points           =         109384
    Total Blocks           =            865
    Max Points             =            256
    Max Functions          =             23
    Weights Tolerance      =       1.00E-15

   => Loading Basis Set <=

    Name: (6-31G(D) AUX)
    Role: JKFIT
    Keyword: DF_BASIS_SCF
    atoms 1   entry C          line   121 file D:\anaconda3\envs\nistpy\Library\share\psi4\basis\cc-pvdz-jkfit.gbs 
    atoms 2-5 entry H          line    51 file D:\anaconda3\envs\nistpy\Library\share\psi4\basis\cc-pvdz-jkfit.gbs 

   => Loading Basis Set <=

    Name: 6-31G(D)
    Role: ORBITAL
    Keyword: BASIS
    atoms 1   entry C          line   111 file D:\anaconda3\envs\nistpy\Library\share\psi4\basis\6-31g_d_.gbs 
    atoms 2-5 entry H          line    44 file D:\anaconda3\envs\nistpy\Library\share\psi4\basis\6-31g_d_.gbs 

  Reading orbitals from file D:\BaiduSyncdisk\py\MS\psi4_scratch/psi4_calculation.default.34212.180.npy, no projection.

  ==> Integral Setup <==

  DFHelper Memory: AOs need 0.001 GiB; user supplied 5.515 GiB. 
  Using in-core AOs.

  ==> MemDFJK: Density-Fitted J/K Matrices <==

    J tasked:                   Yes
    K tasked:                   Yes
    wK tasked:                   No
    OpenMP threads:              10
    Memory [MiB]:              5647
    Algorithm:                 Core
    Schwarz Cutoff:           1E-12
    Mask sparsity (%):       0.0000
    Fitting Condition:        1E-10

   => Auxiliary Basis Set <=

  Basis Set: (6-31G(D) AUX)
    Blend: CC-PVDZ-JKFIT
    Number of shells: 60
    Number of basis functions: 181
    Number of Cartesian functions: 181
    Spherical Harmonics?: false
    Max angular momentum: 3

  Cached 100.0% of DFT collocation blocks in 0.073 [GiB].

  Minimum eigenvalue in the overlap matrix is 2.3243603027E-02.
  Reciprocal condition number of the overlap matrix is 4.2280179506E-03.
    Using symmetric orthogonalization.

  ==> Pre-Iterations <==

  SCF Guess: Orbitals guess was supplied from a previous computation.

   -------------------------------------------------------
    Irrep   Nso     Nmo     Nalpha   Nbeta   Ndocc  Nsocc
   -------------------------------------------------------
     A         23      23       5       4       4       1
   -------------------------------------------------------
    Total      23      23       5       4       4       1
   -------------------------------------------------------

  ==> Iterations <==

                           Total Energy        Delta E     RMS |[F,P]|

   @DF-UKS iter   0:   -40.06534005935377   -4.00653e+01   8.65924e-04 
   @DF-UKS iter   1:   -40.05129053271138    1.40495e-02   1.10545e-04 ADIIS/DIIS
   @DF-UKS iter   2:   -40.05129588365887   -5.35095e-06   2.66006e-05 DIIS
   @DF-UKS iter   3:   -40.05129622336945   -3.39711e-07   1.75186e-05 DIIS
   @DF-UKS iter   4:   -40.05129632525438   -1.01885e-07   5.46918e-06 DIIS
   @DF-UKS iter   5:   -40.05129634214869   -1.68943e-08   4.70333e-07 DIIS
   @DF-UKS iter   6:   -40.05129634230316   -1.54472e-10   3.08079e-08 DIIS
   @DF-UKS iter   7:   -40.05129634230371   -5.54223e-13   2.27356e-09 DIIS
  Energy and wave function converged.


  ==> Post-Iterations <==

   Electrons on quadrature grid:
      Nalpha   =    5.0000011397 ; deviation = 1.140e-06
      Nbeta    =    4.0000002824 ; deviation = 2.824e-07
      Ntotal   =    9.0000014221 ; deviation = 1.422e-06 

   @Spin Contamination Metric:   1.220611881E-03
   @S^2 Expected:                7.500000000E-01
   @S^2 Observed:                7.512206119E-01
   @S   Expected:                5.000000000E-01
   @S   Observed:                5.000000000E-01

    Orbital Energies [Eh]
    ---------------------

    Alpha Occupied:                                                       

       1A    -10.570633     2A     -1.071326     3A     -0.787885  
       4A     -0.787885     5A     -0.690239  

    Alpha Virtual:                                                        

       6A     -0.234879     7A     -0.185971     8A     -0.105560  
       9A     -0.105560    10A      0.190812    11A      0.283239  
      12A      0.283239    13A      0.466567    14A      0.495458  
      15A      0.566966    16A      0.566966    17A      0.658202  
      18A      1.255695    19A      1.481932    20A      1.643724  
      21A      1.643724    22A      1.958666    23A      3.734958  

    Beta Occupied:                                                        

       1A    -10.561199     2A     -1.027832     3A     -0.762961  
       4A     -0.762960  

    Beta Virtual:                                                         

       5A     -0.545664     6A     -0.201423     7A     -0.168208  
       8A     -0.092530     9A     -0.092530    10A      0.233590  
      11A      0.295424    12A      0.295424    13A      0.494792  
      14A      0.537098    15A      0.591512    16A      0.591512  
      17A      0.677978    18A      1.260863    19A      1.552438  
      20A      1.691917    21A      1.691917    22A      1.971183  
      23A      3.762036  

    Final Occupation by Irrep:
              A 
    DOCC [     4 ]
    SOCC [     1 ]
    NA   [     5 ]
    NB   [     4 ]

  @DF-UKS Final Energy:   -40.05129634230371

   => Energetics <=

    Nuclear Repulsion Energy =             13.0237803691103533
    One-Electron Energy =                 -75.3332992473741569
    Two-Electron Energy =                  27.5279322844649599
    DFT Exchange-Correlation Energy =      -5.2697097485048712
    Empirical Dispersion Energy =           0.0000000000000000
    VV10 Nonlocal Energy =                  0.0000000000000000
    Total Energy =                        -40.0512963423037149

  UHF NO Occupations:
  HONO-2 :    3  A 1.9998453
  HONO-1 :    4  A 1.9996992
  HONO-0 :    5  A 1.0000000
  LUNO+0 :    6  A 0.0003008
  LUNO+1 :    7  A 0.0001547
  LUNO+2 :    8  A 0.0001547
  LUNO+3 :    9  A 0.0000002


Computation Completed


Properties will be evaluated at   0.000000,   0.000000,   0.000000 [a0]

Properties computed using the SCF density matrix


 Multipole Moments:

 ------------------------------------------------------------------------------------
     Multipole            Electronic (a.u.)      Nuclear  (a.u.)        Total (a.u.)
 ------------------------------------------------------------------------------------

 L = 1.  Multiply by 2.5417464519 to convert [e a0] to [Debye]
 Dipole X            :         -0.0000148            0.0000122           -0.0000026
 Dipole Y            :          0.0000161           -0.0000131            0.0000030
 Dipole Z            :          0.0000203           -0.0000163            0.0000040
 Magnitude           :                                                    0.0000057

 ------------------------------------------------------------------------------------

*** tstop() called on iCarus at Sun Jun 29 21:43:34 2025
Module time:
	user time   =        nan seconds =        nan minutes
	system time =        nan seconds =        nan minutes
	total time  =          0 seconds =       0.00 minutes
Total time:
	user time   =        nan seconds =        nan minutes
	system time =        nan seconds =        nan minutes
	total time  =          4 seconds =       0.07 minutes

*** tstart() called on iCarus
*** at Sun Jun 29 21:43:34 2025


         ------------------------------------------------------------
                                   SCF GRAD                          
                          Rob Parrish, Justin Turney,                
                       Andy Simmonett, and Alex Sokolov              
         ------------------------------------------------------------

  ==> Geometry <==

    Molecular point group: c1
    Full point group: C1

    Geometry (in Angstrom), charge = 1, multiplicity = 2:

       Center              X                  Y                   Z               Mass       
    ------------   -----------------  -----------------  -----------------  -----------------
         C            0.000001294896    -0.000001382474    -0.000001724267    12.000000000000
         H           -0.521351937647     0.996395080735     0.068257693497     1.007825032230
         H           -0.192744712092    -0.984265504093    -0.513175076239     1.007825032230
         H           -0.170787161600    -0.339656938083     1.060535817441     1.007825032230
         H            0.884882516444     0.327528743915    -0.615616710433     1.007825032230

  Nuclear repulsion =   13.023780369110353

  ==> Basis Set <==

  Basis Set: 6-31G(D)
    Blend: 6-31G(D)
    Number of shells: 14
    Number of basis functions: 23
    Number of Cartesian functions: 23
    Spherical Harmonics?: false
    Max angular momentum: 2

  ==> DFJKGrad: Density-Fitted SCF Gradients <==

    Gradient:                    1
    J tasked:                  Yes
    K tasked:                  Yes
    wK tasked:                  No
    OpenMP threads:             10
    Integrals threads:          10
    Memory [MiB]:             -422
    Schwarz Cutoff:          1E-12
    Fitting Condition:       1E-10

   => Auxiliary Basis Set <=

  Basis Set: (6-31G(D) AUX)
    Blend: CC-PVDZ-JKFIT
    Number of shells: 60
    Number of basis functions: 181
    Number of Cartesian functions: 181
    Spherical Harmonics?: false
    Max angular momentum: 3

  ==> DFT Potential <==

   => LibXC <=

    Version 7.0.0
    S. Lehtola, C. Steigemann, M. J.T. Oliveira, and M. A.L. Marques.,  SoftwareX 7, 1–5 (2018) (10.1016/j.softx.2017.11.002)

   => Composite Functional: B3LYP <= 

    B3LYP Hyb-GGA Exchange-Correlation Functional

    P. J. Stephens, F. J. Devlin, C. F. Chabalowski, and M. J. Frisch.,  J. Phys. Chem. 98, 11623 (1994) (10.1021/j100096a001)

    Deriv               =              1
    GGA                 =           TRUE
    Meta                =          FALSE

    Exchange Hybrid     =           TRUE
    MP2 Hybrid          =          FALSE

   => Exchange Functionals <=

    0.0800   Slater exchange
    0.7200         Becke 88

   => Exact (HF) Exchange <=

    0.2000               HF 

   => Correlation Functionals <=

    0.1900   Vosko, Wilk & Nusair (VWN5_RPA)
    0.8100   Lee, Yang & Parr

   => LibXC Density Thresholds  <==

    XC_HYB_GGA_XC_B3LYP:  1.00E-15 

   => Molecular Quadrature <=

    Radial Scheme          =       TREUTLER
    Pruning Scheme         =           NONE
    Nuclear Scheme         =       TREUTLER

    Blocking Scheme        =         OCTREE
    BS radius alpha        =              1
    Pruning alpha          =              1
    Radial Points          =             75
    Spherical Points       =            302
    Total Points           =         109384
    Total Blocks           =            865
    Max Points             =            256
    Max Functions          =             23
    Weights Tolerance      =       1.00E-15


  -Total Gradient:
     Atom            X                  Y                   Z
    ------   -----------------  -----------------  -----------------
       1        0.000000111542     0.000000255081     0.000000096202
       2       -0.003838258423    -0.001065994963    -0.002841824149
       3       -0.004214942679     0.001202375924    -0.002175800863
       4        0.004631077599     0.000313997038     0.001548884555
       5        0.003422011951    -0.000450632959     0.003468644290


*** tstop() called on iCarus at Sun Jun 29 21:43:34 2025
Module time:
	user time   =        nan seconds =        nan minutes
	system time =        nan seconds =        nan minutes
	total time  =          0 seconds =       0.00 minutes
Total time:
	user time   =        nan seconds =        nan minutes
	system time =        nan seconds =        nan minutes
	total time  =          4 seconds =       0.07 minutes

    			-----------------------------------------

    			 OPTKING 3.0: for geometry optimizations 

    			     By R.A. King, Bethel University     

    			        with contributions from          

    			    A.V. Copan, J. Cayton, A. Heide      

    			-----------------------------------------

    
	                            ===> Fragment 1 <== 

	 Z (Atomic Numbers)          Masses                          Geom                  
	       6.000000            12.000000          0.00000245 -0.00000261 -0.00000326   
	       1.000000             1.007825         -0.98521238  1.88291382  0.12898835   
	       1.000000             1.007825         -0.36423472 -1.85999224 -0.96976035   
	       1.000000             1.007825         -0.32274096 -0.64185859  2.00412224   
	       1.000000             1.007825          1.67218561  0.61893962 -1.16334698   

	 - Coordinate -           - BOHR/RAD -       - ANG/DEG -
	 R(1,2)           =         2.129005           1.126621
	 R(1,3)           =         2.129004           1.126621
	 R(1,4)           =         2.129005           1.126621
	 R(1,5)           =         2.129003           1.126620
	 B(2,1,3)         =         2.376167         136.144323
	 B(2,1,4)         =         1.710701          98.015952
	 B(2,1,5)         =         1.710708          98.016348
	 B(3,1,4)         =         1.710710          98.016466
	 B(3,1,5)         =         1.710715          98.016756
	 B(4,1,5)         =         2.376168         136.144370
	 O(2,1,3,4)       =         0.706355          40.471167
	 O(2,1,3,5)       =        -0.706350         -40.470865
	 O(2,1,4,5)       =         1.188095          68.072821
	 O(3,1,2,4)       =        -0.706354         -40.471105
	 O(3,1,2,5)       =         0.706349          40.470816
	 O(3,1,4,5)       =        -1.188072         -68.071502
	 O(4,1,2,3)       =         1.188092          68.072675
	 O(4,1,2,5)       =        -0.706355         -40.471162
	 O(4,1,3,5)       =         0.706348          40.470758
	 O(5,1,2,3)       =        -1.188075         -68.071695
	 O(5,1,2,4)       =         0.706354          40.471114
	 O(5,1,3,4)       =        -0.706347         -40.470723


	        --- Internal Coordinate Step in ANG or DEG, aJ/ANG or AJ/DEG ---
	-------------------------------------------------------------------------------
	           Coordinate      Previous         Force          Change          New 
	           ----------      --------        ------          ------        ------
	               R(1,2)       1.12662       -0.00545       -0.00220       1.12442
	               R(1,3)       1.12662       -0.00545       -0.00220       1.12442
	               R(1,4)       1.12662       -0.00545       -0.00220       1.12442
	               R(1,5)       1.12662       -0.00545       -0.00220       1.12442
	             B(2,1,3)     136.14432        0.00019        2.57141     138.71573
	             B(2,1,4)      98.01595       -0.00007       -0.87729      97.13866
	             B(2,1,5)      98.01635       -0.00007       -0.87736      97.13898
	             B(3,1,4)      98.01647       -0.00007       -0.87739      97.13908
	             B(3,1,5)      98.01676       -0.00007       -0.87739      97.13936
	             B(4,1,5)     136.14437        0.00019        2.57138     138.71575
	           O(2,1,3,4)      40.47117       -0.00015       -1.98959      38.48157
	           O(2,1,3,5)     -40.47087        0.00015        1.98954     -38.48133
	           O(2,1,4,5)      68.07282        0.00010        1.28564      69.35846
	           O(3,1,2,4)     -40.47110        0.00015        1.98957     -38.48153
	           O(3,1,2,5)      40.47082       -0.00015       -1.98953      38.48129
	           O(3,1,4,5)     -68.07150       -0.00010       -1.28577     -69.35727
	           O(4,1,2,3)      68.07267        0.00010        1.28566      69.35833
	           O(4,1,2,5)     -40.47116        0.00015        1.98958     -38.48159
	           O(4,1,3,5)      40.47076       -0.00015       -1.98949      38.48127
	           O(5,1,2,3)     -68.07169       -0.00010       -1.28572     -69.35742
	           O(5,1,2,4)      40.47111       -0.00015       -1.98956      38.48155
	           O(5,1,3,4)     -40.47072        0.00015        1.98949     -38.48124
	-------------------------------------------------------------------------------

	                                 ==> Convergence Check <==                                  
    
	Measures of convergence in internal coordinates in au.
    
	Criteria marked as inactive (o), active & met (*), and active & unmet ( ).

	----------------------------------------------------------------------------------------------
	   Step    Total Energy     Delta E     Max Force     RMS Force      Max Disp      RMS Disp   
	----------------------------------------------------------------------------------------------
	  Convergence Criteria     1.00e-06 *    3.00e-04 *             o    1.20e-03 *             o
	----------------------------------------------------------------------------------------------
	     4     -40.05129634   -3.71e-03      2.55e-03      1.57e-03 o    4.49e-02      2.75e-02 o  ~
	----------------------------------------------------------------------------------------------

Next Geometry in Ang 
	Fragment 1 (Ang)

	    C   0.0000010388  -0.0000011887  -0.0000015354
	    H  -0.5018219730   1.0027896847   0.0831029001
	    H  -0.1709765433  -0.9913612861  -0.5022895623
	    H  -0.1950311372  -0.3415787234   1.0533784788
	    H   0.8678286147   0.3301515136  -0.6341902812


    Structure for next step:
    Molecular point group: c1
    Full point group: C1

    Geometry (in Angstrom), charge = 1, multiplicity = 2:

    C            0.000001038781    -0.000001188700    -0.000001535387
    H           -0.501821972990     1.002789684666     0.083102900070
    H           -0.170976543307    -0.991361286106    -0.502289562297
    H           -0.195031137232    -0.341578723447     1.053378478827
    H            0.867828614749     0.330151513588    -0.634190281213


Scratch directory: D:\BaiduSyncdisk\py\MS\psi4_scratch/
gradient() will perform analytic gradient computation.
   => Libint2 <=

    Primary   basis highest AM E, G, H:  6, 6, -
    Auxiliary basis highest AM E, G, H:  7, 7, -
    Onebody   basis highest AM E, G, H:  -, -, -
    Solid Harmonics ordering:            Gaussian

*** tstart() called on iCarus
*** at Sun Jun 29 21:43:34 2025

   => Loading Basis Set <=

    Name: 6-31G(D)
    Role: ORBITAL
    Keyword: BASIS
    atoms 1   entry C          line   111 file D:\anaconda3\envs\nistpy\Library\share\psi4\basis\6-31g_d_.gbs 
    atoms 2-5 entry H          line    44 file D:\anaconda3\envs\nistpy\Library\share\psi4\basis\6-31g_d_.gbs 


         ---------------------------------------------------------
                                   SCF
               by Justin Turney, Rob Parrish, Andy Simmonett
                          and Daniel G. A. Smith
                              UKS Reference
                       10 Threads,   -562 MiB Core
         ---------------------------------------------------------

  ==> Geometry <==

    Molecular point group: c1
    Full point group: C1

    Geometry (in Angstrom), charge = 1, multiplicity = 2:

       Center              X                  Y                   Z               Mass       
    ------------   -----------------  -----------------  -----------------  -----------------
         C            0.000001038781    -0.000001188700    -0.000001535387    12.000000000000
         H           -0.501821972990     1.002789684666     0.083102900070     1.007825032230
         H           -0.170976543307    -0.991361286106    -0.502289562297     1.007825032230
         H           -0.195031137232    -0.341578723447     1.053378478827     1.007825032230
         H            0.867828614749     0.330151513588    -0.634190281213     1.007825032230

  Running in c1 symmetry.

  Rotational constants: A =      5.88370  B =      5.88370  C =      3.77683 [cm^-1]
  Rotational constants: A = 176388.84065  B = 176388.78800  C = 113226.44248 [MHz]
  Nuclear repulsion =   13.053235165558208

  Charge       = 1
  Multiplicity = 2
  Electrons    = 9
  Nalpha       = 5
  Nbeta        = 4

  ==> Algorithm <==

  SCF Algorithm Type is DF.
  DIIS enabled.
  MOM disabled.
  Fractional occupation disabled.
  Guess Type is READ.
  Energy threshold   = 1.00e-08
  Density threshold  = 1.00e-08
  Integral threshold = 1.00e-12

  ==> Primary Basis <==

  Basis Set: 6-31G(D)
    Blend: 6-31G(D)
    Number of shells: 14
    Number of basis functions: 23
    Number of Cartesian functions: 23
    Spherical Harmonics?: false
    Max angular momentum: 2

  ==> DFT Potential <==

   => LibXC <=

    Version 7.0.0
    S. Lehtola, C. Steigemann, M. J.T. Oliveira, and M. A.L. Marques.,  SoftwareX 7, 1–5 (2018) (10.1016/j.softx.2017.11.002)

   => Composite Functional: B3LYP <= 

    B3LYP Hyb-GGA Exchange-Correlation Functional

    P. J. Stephens, F. J. Devlin, C. F. Chabalowski, and M. J. Frisch.,  J. Phys. Chem. 98, 11623 (1994) (10.1021/j100096a001)

    Deriv               =              1
    GGA                 =           TRUE
    Meta                =          FALSE

    Exchange Hybrid     =           TRUE
    MP2 Hybrid          =          FALSE

   => Exchange Functionals <=

    0.0800   Slater exchange
    0.7200         Becke 88

   => Exact (HF) Exchange <=

    0.2000               HF 

   => Correlation Functionals <=

    0.1900   Vosko, Wilk & Nusair (VWN5_RPA)
    0.8100   Lee, Yang & Parr

   => LibXC Density Thresholds  <==

    XC_HYB_GGA_XC_B3LYP:  1.00E-15 

   => Molecular Quadrature <=

    Radial Scheme          =       TREUTLER
    Pruning Scheme         =           NONE
    Nuclear Scheme         =       TREUTLER

    Blocking Scheme        =         OCTREE
    BS radius alpha        =              1
    Pruning alpha          =              1
    Radial Points          =             75
    Spherical Points       =            302
    Total Points           =         109484
    Total Blocks           =            867
    Max Points             =            256
    Max Functions          =             23
    Weights Tolerance      =       1.00E-15

   => Loading Basis Set <=

    Name: (6-31G(D) AUX)
    Role: JKFIT
    Keyword: DF_BASIS_SCF
    atoms 1   entry C          line   121 file D:\anaconda3\envs\nistpy\Library\share\psi4\basis\cc-pvdz-jkfit.gbs 
    atoms 2-5 entry H          line    51 file D:\anaconda3\envs\nistpy\Library\share\psi4\basis\cc-pvdz-jkfit.gbs 

   => Loading Basis Set <=

    Name: 6-31G(D)
    Role: ORBITAL
    Keyword: BASIS
    atoms 1   entry C          line   111 file D:\anaconda3\envs\nistpy\Library\share\psi4\basis\6-31g_d_.gbs 
    atoms 2-5 entry H          line    44 file D:\anaconda3\envs\nistpy\Library\share\psi4\basis\6-31g_d_.gbs 

  Reading orbitals from file D:\BaiduSyncdisk\py\MS\psi4_scratch/psi4_calculation.default.34212.180.npy, no projection.

  ==> Integral Setup <==

  DFHelper Memory: AOs need 0.001 GiB; user supplied 5.515 GiB. 
  Using in-core AOs.

  ==> MemDFJK: Density-Fitted J/K Matrices <==

    J tasked:                   Yes
    K tasked:                   Yes
    wK tasked:                   No
    OpenMP threads:              10
    Memory [MiB]:              5647
    Algorithm:                 Core
    Schwarz Cutoff:           1E-12
    Mask sparsity (%):       0.0000
    Fitting Condition:        1E-10

   => Auxiliary Basis Set <=

  Basis Set: (6-31G(D) AUX)
    Blend: CC-PVDZ-JKFIT
    Number of shells: 60
    Number of basis functions: 181
    Number of Cartesian functions: 181
    Spherical Harmonics?: false
    Max angular momentum: 3

  Cached 100.0% of DFT collocation blocks in 0.073 [GiB].

  Minimum eigenvalue in the overlap matrix is 2.3606278347E-02.
  Reciprocal condition number of the overlap matrix is 4.2854846233E-03.
    Using symmetric orthogonalization.

  ==> Pre-Iterations <==

  SCF Guess: Orbitals guess was supplied from a previous computation.

   -------------------------------------------------------
    Irrep   Nso     Nmo     Nalpha   Nbeta   Ndocc  Nsocc
   -------------------------------------------------------
     A         23      23       5       4       4       1
   -------------------------------------------------------
    Total      23      23       5       4       4       1
   -------------------------------------------------------

  ==> Iterations <==

                           Total Energy        Delta E     RMS |[F,P]|

   @DF-UKS iter   0:   -40.05629546557422   -4.00563e+01   3.94967e-04 
   @DF-UKS iter   1:   -40.05191978079907    4.37568e-03   6.93580e-05 DIIS
   @DF-UKS iter   2:   -40.05192103685527   -1.25606e-06   3.94574e-05 DIIS
   @DF-UKS iter   3:   -40.05192148519971   -4.48344e-07   1.48948e-05 DIIS
   @DF-UKS iter   4:   -40.05192156577067   -8.05710e-08   1.96045e-06 DIIS
   @DF-UKS iter   5:   -40.05192156914933   -3.37866e-09   1.91125e-07 DIIS
   @DF-UKS iter   6:   -40.05192156917452   -2.51887e-11   1.56684e-08 DIIS
   @DF-UKS iter   7:   -40.05192156917467   -1.49214e-13   1.09701e-09 DIIS
  Energy and wave function converged.


  ==> Post-Iterations <==

   Electrons on quadrature grid:
      Nalpha   =    5.0000015957 ; deviation = 1.596e-06
      Nbeta    =    4.0000040912 ; deviation = 4.091e-06
      Ntotal   =    9.0000056869 ; deviation = 5.687e-06 

   @Spin Contamination Metric:   1.154515642E-03
   @S^2 Expected:                7.500000000E-01
   @S^2 Observed:                7.511545156E-01
   @S   Expected:                5.000000000E-01
   @S   Observed:                5.000000000E-01

    Orbital Energies [Eh]
    ---------------------

    Alpha Occupied:                                                       

       1A    -10.569744     2A     -1.072518     3A     -0.790973  
       4A     -0.790973     5A     -0.681633  

    Alpha Virtual:                                                        

       6A     -0.243781     7A     -0.186795     8A     -0.101966  
       9A     -0.101966    10A      0.192107    11A      0.293453  
      12A      0.293453    13A      0.461317    14A      0.488471  
      15A      0.571700    16A      0.571700    17A      0.650626  
      18A      1.252583    19A      1.514153    20A      1.621799  
      21A      1.621799    22A      1.977893    23A      3.732240  

    Beta Occupied:                                                        

       1A    -10.560103     2A     -1.029381     3A     -0.766441  
       4A     -0.766441  

    Beta Virtual:                                                         

       5A     -0.536733     6A     -0.210549     7A     -0.168790  
       8A     -0.089314     9A     -0.089314    10A      0.236062  
      11A      0.305713    12A      0.305713    13A      0.488306  
      14A      0.529803    15A      0.596039    16A      0.596039  
      17A      0.671409    18A      1.258068    19A      1.584942  
      20A      1.670435    21A      1.670435    22A      1.990698  
      23A      3.759813  

    Final Occupation by Irrep:
              A 
    DOCC [     4 ]
    SOCC [     1 ]
    NA   [     5 ]
    NB   [     4 ]

  @DF-UKS Final Energy:   -40.05192156917467

   => Energetics <=

    Nuclear Repulsion Energy =             13.0532351655582080
    One-Electron Energy =                 -75.3868566099005761
    Two-Electron Energy =                  27.5534070950435463
    DFT Exchange-Correlation Energy =      -5.2717072198758403
    Empirical Dispersion Energy =           0.0000000000000000
    VV10 Nonlocal Energy =                  0.0000000000000000
    Total Energy =                        -40.0519215691746666

  UHF NO Occupations:
  HONO-2 :    3  A 1.9998673
  HONO-1 :    4  A 1.9996883
  HONO-0 :    5  A 1.0000000
  LUNO+0 :    6  A 0.0003117
  LUNO+1 :    7  A 0.0001327
  LUNO+2 :    8  A 0.0001327
  LUNO+3 :    9  A 0.0000002


Computation Completed


Properties will be evaluated at   0.000000,   0.000000,   0.000000 [a0]

Properties computed using the SCF density matrix


 Multipole Moments:

 ------------------------------------------------------------------------------------
     Multipole            Electronic (a.u.)      Nuclear  (a.u.)        Total (a.u.)
 ------------------------------------------------------------------------------------

 L = 1.  Multiply by 2.5417464519 to convert [e a0] to [Debye]
 Dipole X            :         -0.0000120            0.0000098           -0.0000022
 Dipole Y            :          0.0000138           -0.0000112            0.0000026
 Dipole Z            :          0.0000179           -0.0000145            0.0000034
 Magnitude           :                                                    0.0000048

 ------------------------------------------------------------------------------------

*** tstop() called on iCarus at Sun Jun 29 21:43:34 2025
Module time:
	user time   =        nan seconds =        nan minutes
	system time =        nan seconds =        nan minutes
	total time  =          0 seconds =       0.00 minutes
Total time:
	user time   =        nan seconds =        nan minutes
	system time =        nan seconds =        nan minutes
	total time  =          4 seconds =       0.07 minutes

*** tstart() called on iCarus
*** at Sun Jun 29 21:43:34 2025


         ------------------------------------------------------------
                                   SCF GRAD                          
                          Rob Parrish, Justin Turney,                
                       Andy Simmonett, and Alex Sokolov              
         ------------------------------------------------------------

  ==> Geometry <==

    Molecular point group: c1
    Full point group: C1

    Geometry (in Angstrom), charge = 1, multiplicity = 2:

       Center              X                  Y                   Z               Mass       
    ------------   -----------------  -----------------  -----------------  -----------------
         C            0.000001038781    -0.000001188700    -0.000001535387    12.000000000000
         H           -0.501821972990     1.002789684666     0.083102900070     1.007825032230
         H           -0.170976543307    -0.991361286106    -0.502289562297     1.007825032230
         H           -0.195031137232    -0.341578723447     1.053378478827     1.007825032230
         H            0.867828614749     0.330151513588    -0.634190281213     1.007825032230

  Nuclear repulsion =   13.053235165558208

  ==> Basis Set <==

  Basis Set: 6-31G(D)
    Blend: 6-31G(D)
    Number of shells: 14
    Number of basis functions: 23
    Number of Cartesian functions: 23
    Spherical Harmonics?: false
    Max angular momentum: 2

  ==> DFJKGrad: Density-Fitted SCF Gradients <==

    Gradient:                    1
    J tasked:                  Yes
    K tasked:                  Yes
    wK tasked:                  No
    OpenMP threads:             10
    Integrals threads:          10
    Memory [MiB]:             -422
    Schwarz Cutoff:          1E-12
    Fitting Condition:       1E-10

   => Auxiliary Basis Set <=

  Basis Set: (6-31G(D) AUX)
    Blend: CC-PVDZ-JKFIT
    Number of shells: 60
    Number of basis functions: 181
    Number of Cartesian functions: 181
    Spherical Harmonics?: false
    Max angular momentum: 3

  ==> DFT Potential <==

   => LibXC <=

    Version 7.0.0
    S. Lehtola, C. Steigemann, M. J.T. Oliveira, and M. A.L. Marques.,  SoftwareX 7, 1–5 (2018) (10.1016/j.softx.2017.11.002)

   => Composite Functional: B3LYP <= 

    B3LYP Hyb-GGA Exchange-Correlation Functional

    P. J. Stephens, F. J. Devlin, C. F. Chabalowski, and M. J. Frisch.,  J. Phys. Chem. 98, 11623 (1994) (10.1021/j100096a001)

    Deriv               =              1
    GGA                 =           TRUE
    Meta                =          FALSE

    Exchange Hybrid     =           TRUE
    MP2 Hybrid          =          FALSE

   => Exchange Functionals <=

    0.0800   Slater exchange
    0.7200         Becke 88

   => Exact (HF) Exchange <=

    0.2000               HF 

   => Correlation Functionals <=

    0.1900   Vosko, Wilk & Nusair (VWN5_RPA)
    0.8100   Lee, Yang & Parr

   => LibXC Density Thresholds  <==

    XC_HYB_GGA_XC_B3LYP:  1.00E-15 

   => Molecular Quadrature <=

    Radial Scheme          =       TREUTLER
    Pruning Scheme         =           NONE
    Nuclear Scheme         =       TREUTLER

    Blocking Scheme        =         OCTREE
    BS radius alpha        =              1
    Pruning alpha          =              1
    Radial Points          =             75
    Spherical Points       =            302
    Total Points           =         109484
    Total Blocks           =            867
    Max Points             =            256
    Max Functions          =             23
    Weights Tolerance      =       1.00E-15


  -Total Gradient:
     Atom            X                  Y                   Z
    ------   -----------------  -----------------  -----------------
       1        0.000000055326     0.000000132247     0.000000032974
       2       -0.001172213703    -0.000621597887    -0.000986246606
       3       -0.001386324053     0.000664745314    -0.000607843750
       4        0.001622045017     0.000195579733     0.000252692899
       5        0.000936437502    -0.000238859301     0.001341364335


*** tstop() called on iCarus at Sun Jun 29 21:43:34 2025
Module time:
	user time   =        nan seconds =        nan minutes
	system time =        nan seconds =        nan minutes
	total time  =          0 seconds =       0.00 minutes
Total time:
	user time   =        nan seconds =        nan minutes
	system time =        nan seconds =        nan minutes
	total time  =          4 seconds =       0.07 minutes

    			-----------------------------------------

    			 OPTKING 3.0: for geometry optimizations 

    			     By R.A. King, Bethel University     

    			        with contributions from          

    			    A.V. Copan, J. Cayton, A. Heide      

    			-----------------------------------------

    
	                            ===> Fragment 1 <== 

	 Z (Atomic Numbers)          Masses                          Geom                  
	       6.000000            12.000000          0.00000196 -0.00000225 -0.00000290   
	       1.000000             1.007825         -0.94830609  1.89499787  0.15704172   
	       1.000000             1.007825         -0.32309884 -1.87340132 -0.94918971   
	       1.000000             1.007825         -0.36855544 -0.64549024  1.99059683   
	       1.000000             1.007825          1.63995841  0.62389594 -1.19844594   

	 - Coordinate -           - BOHR/RAD -       - ANG/DEG -
	 R(1,2)           =         2.124847           1.124421
	 R(1,3)           =         2.124847           1.124420
	 R(1,4)           =         2.124847           1.124421
	 R(1,5)           =         2.124846           1.124420
	 B(2,1,3)         =         2.421046         138.715733
	 B(2,1,4)         =         1.695389          97.138662
	 B(2,1,5)         =         1.695395          97.138984
	 B(3,1,4)         =         1.695397          97.139077
	 B(3,1,5)         =         1.695402          97.139363
	 B(4,1,5)         =         2.421047         138.715751
	 O(2,1,3,4)       =         0.671630          38.481572
	 O(2,1,3,5)       =        -0.671626         -38.481327
	 O(2,1,4,5)       =         1.210534          69.358463
	 O(3,1,2,4)       =        -0.671629         -38.481531
	 O(3,1,2,5)       =         0.671625          38.481290
	 O(3,1,4,5)       =        -1.210513         -69.357269
	 O(4,1,2,3)       =         1.210531          69.358333
	 O(4,1,2,5)       =        -0.671630         -38.481585
	 O(4,1,3,5)       =         0.671625          38.481265
	 O(5,1,2,3)       =        -1.210515         -69.357419
	 O(5,1,2,4)       =         0.671630          38.481553
	 O(5,1,3,4)       =        -0.671624         -38.481237


	        --- Internal Coordinate Step in ANG or DEG, aJ/ANG or AJ/DEG ---
	-------------------------------------------------------------------------------
	           Coordinate      Previous         Force          Change          New 
	           ----------      --------        ------          ------        ------
	               R(1,2)       1.12442        0.00086       -0.00038       1.12404
	               R(1,3)       1.12442        0.00085       -0.00038       1.12404
	               R(1,4)       1.12442        0.00086       -0.00038       1.12404
	               R(1,5)       1.12442        0.00086       -0.00038       1.12404
	             B(2,1,3)     138.71573        0.00006        0.97624     139.69197
	             B(2,1,4)      97.13866       -0.00002       -0.32125      96.81741
	             B(2,1,5)      97.13898       -0.00002       -0.32130      96.81769
	             B(3,1,4)      97.13908       -0.00002       -0.32131      96.81776
	             B(3,1,5)      97.13936       -0.00002       -0.32133      96.81803
	             B(4,1,5)     138.71575        0.00006        0.97622     139.69198
	           O(2,1,3,4)      38.48157       -0.00005       -0.77530      37.70627
	           O(2,1,3,5)     -38.48133        0.00005        0.77527     -37.70606
	           O(2,1,4,5)      69.35846        0.00003        0.48805      69.84652
	           O(3,1,2,4)     -38.48153        0.00005        0.77529     -37.70624
	           O(3,1,2,5)      38.48129       -0.00005       -0.77526      37.70603
	           O(3,1,4,5)     -69.35727       -0.00003       -0.48818     -69.84545
	           O(4,1,2,3)      69.35833        0.00003        0.48807      69.84641
	           O(4,1,2,5)     -38.48159        0.00005        0.77530     -37.70629
	           O(4,1,3,5)      38.48127       -0.00005       -0.77525      37.70602
	           O(5,1,2,3)     -69.35742       -0.00003       -0.48815     -69.84557
	           O(5,1,2,4)      38.48155       -0.00005       -0.77529      37.70626
	           O(5,1,3,4)     -38.48124        0.00005        0.77525     -37.70599
	-------------------------------------------------------------------------------

	                                 ==> Convergence Check <==                                  
    
	Measures of convergence in internal coordinates in au.
    
	Criteria marked as inactive (o), active & met (*), and active & unmet ( ).

	----------------------------------------------------------------------------------------------
	   Step    Total Energy     Delta E     Max Force     RMS Force      Max Disp      RMS Disp   
	----------------------------------------------------------------------------------------------
	  Convergence Criteria     1.00e-06 *    3.00e-04 *             o    1.20e-03 *             o
	----------------------------------------------------------------------------------------------
	     5     -40.05192157   -6.25e-04      8.33e-04      5.17e-04 o    1.70e-02      1.06e-02 o  ~
	----------------------------------------------------------------------------------------------

Next Geometry in Ang 
	Fragment 1 (Ang)

	    C   0.0000009019  -0.0000010848  -0.0000014124
	    H  -0.4945601460   1.0054843751   0.0887488156
	    H  -0.1627768676  -0.9943181263  -0.4983023478
	    H  -0.2042678435  -0.3423996403   1.0509532147
	    H   0.8616039552   0.3312344762  -0.6413982701


    Structure for next step:
    Molecular point group: c1
    Full point group: C1

    Geometry (in Angstrom), charge = 1, multiplicity = 2:

    C            0.000000901944    -0.000001084768    -0.000001412383
    H           -0.494560145981     1.005484375116     0.088748815644
    H           -0.162776867623    -0.994318126318    -0.498302347846
    H           -0.204267843510    -0.342399640279     1.050953214695
    H            0.861603955170     0.331234476248    -0.641398270110


Scratch directory: D:\BaiduSyncdisk\py\MS\psi4_scratch/
gradient() will perform analytic gradient computation.
   => Libint2 <=

    Primary   basis highest AM E, G, H:  6, 6, -
    Auxiliary basis highest AM E, G, H:  7, 7, -
    Onebody   basis highest AM E, G, H:  -, -, -
    Solid Harmonics ordering:            Gaussian

*** tstart() called on iCarus
*** at Sun Jun 29 21:43:34 2025

   => Loading Basis Set <=

    Name: 6-31G(D)
    Role: ORBITAL
    Keyword: BASIS
    atoms 1   entry C          line   111 file D:\anaconda3\envs\nistpy\Library\share\psi4\basis\6-31g_d_.gbs 
    atoms 2-5 entry H          line    44 file D:\anaconda3\envs\nistpy\Library\share\psi4\basis\6-31g_d_.gbs 


         ---------------------------------------------------------
                                   SCF
               by Justin Turney, Rob Parrish, Andy Simmonett
                          and Daniel G. A. Smith
                              UKS Reference
                       10 Threads,   -562 MiB Core
         ---------------------------------------------------------

  ==> Geometry <==

    Molecular point group: c1
    Full point group: C1

    Geometry (in Angstrom), charge = 1, multiplicity = 2:

       Center              X                  Y                   Z               Mass       
    ------------   -----------------  -----------------  -----------------  -----------------
         C            0.000000901944    -0.000001084768    -0.000001412383    12.000000000000
         H           -0.494560145981     1.005484375116     0.088748815644     1.007825032230
         H           -0.162776867623    -0.994318126318    -0.498302347846     1.007825032230
         H           -0.204267843510    -0.342399640279     1.050953214695     1.007825032230
         H            0.861603955170     0.331234476248    -0.641398270110     1.007825032230

  Running in c1 symmetry.

  Rotational constants: A =      5.91697  B =      5.91697  C =      3.75551 [cm^-1]
  Rotational constants: A = 177386.28617  B = 177386.27591  C = 112587.38268 [MHz]
  Nuclear repulsion =   13.059176019304287

  Charge       = 1
  Multiplicity = 2
  Electrons    = 9
  Nalpha       = 5
  Nbeta        = 4

  ==> Algorithm <==

  SCF Algorithm Type is DF.
  DIIS enabled.
  MOM disabled.
  Fractional occupation disabled.
  Guess Type is READ.
  Energy threshold   = 1.00e-08
  Density threshold  = 1.00e-08
  Integral threshold = 1.00e-12

  ==> Primary Basis <==

  Basis Set: 6-31G(D)
    Blend: 6-31G(D)
    Number of shells: 14
    Number of basis functions: 23
    Number of Cartesian functions: 23
    Spherical Harmonics?: false
    Max angular momentum: 2

  ==> DFT Potential <==

   => LibXC <=

    Version 7.0.0
    S. Lehtola, C. Steigemann, M. J.T. Oliveira, and M. A.L. Marques.,  SoftwareX 7, 1–5 (2018) (10.1016/j.softx.2017.11.002)

   => Composite Functional: B3LYP <= 

    B3LYP Hyb-GGA Exchange-Correlation Functional

    P. J. Stephens, F. J. Devlin, C. F. Chabalowski, and M. J. Frisch.,  J. Phys. Chem. 98, 11623 (1994) (10.1021/j100096a001)

    Deriv               =              1
    GGA                 =           TRUE
    Meta                =          FALSE

    Exchange Hybrid     =           TRUE
    MP2 Hybrid          =          FALSE

   => Exchange Functionals <=

    0.0800   Slater exchange
    0.7200         Becke 88

   => Exact (HF) Exchange <=

    0.2000               HF 

   => Correlation Functionals <=

    0.1900   Vosko, Wilk & Nusair (VWN5_RPA)
    0.8100   Lee, Yang & Parr

   => LibXC Density Thresholds  <==

    XC_HYB_GGA_XC_B3LYP:  1.00E-15 

   => Molecular Quadrature <=

    Radial Scheme          =       TREUTLER
    Pruning Scheme         =           NONE
    Nuclear Scheme         =       TREUTLER

    Blocking Scheme        =         OCTREE
    BS radius alpha        =              1
    Pruning alpha          =              1
    Radial Points          =             75
    Spherical Points       =            302
    Total Points           =         109480
    Total Blocks           =            864
    Max Points             =            256
    Max Functions          =             23
    Weights Tolerance      =       1.00E-15

   => Loading Basis Set <=

    Name: (6-31G(D) AUX)
    Role: JKFIT
    Keyword: DF_BASIS_SCF
    atoms 1   entry C          line   121 file D:\anaconda3\envs\nistpy\Library\share\psi4\basis\cc-pvdz-jkfit.gbs 
    atoms 2-5 entry H          line    51 file D:\anaconda3\envs\nistpy\Library\share\psi4\basis\cc-pvdz-jkfit.gbs 

   => Loading Basis Set <=

    Name: 6-31G(D)
    Role: ORBITAL
    Keyword: BASIS
    atoms 1   entry C          line   111 file D:\anaconda3\envs\nistpy\Library\share\psi4\basis\6-31g_d_.gbs 
    atoms 2-5 entry H          line    44 file D:\anaconda3\envs\nistpy\Library\share\psi4\basis\6-31g_d_.gbs 

  Reading orbitals from file D:\BaiduSyncdisk\py\MS\psi4_scratch/psi4_calculation.default.34212.180.npy, no projection.

  ==> Integral Setup <==

  DFHelper Memory: AOs need 0.001 GiB; user supplied 5.515 GiB. 
  Using in-core AOs.

  ==> MemDFJK: Density-Fitted J/K Matrices <==

    J tasked:                   Yes
    K tasked:                   Yes
    wK tasked:                   No
    OpenMP threads:              10
    Memory [MiB]:              5647
    Algorithm:                 Core
    Schwarz Cutoff:           1E-12
    Mask sparsity (%):       0.0000
    Fitting Condition:        1E-10

   => Auxiliary Basis Set <=

  Basis Set: (6-31G(D) AUX)
    Blend: CC-PVDZ-JKFIT
    Number of shells: 60
    Number of basis functions: 181
    Number of Cartesian functions: 181
    Spherical Harmonics?: false
    Max angular momentum: 3

  Cached 100.0% of DFT collocation blocks in 0.073 [GiB].

  Minimum eigenvalue in the overlap matrix is 2.3775204324E-02.
  Reciprocal condition number of the overlap matrix is 4.3142062428E-03.
    Using symmetric orthogonalization.

  ==> Pre-Iterations <==

  SCF Guess: Orbitals guess was supplied from a previous computation.

   -------------------------------------------------------
    Irrep   Nso     Nmo     Nalpha   Nbeta   Ndocc  Nsocc
   -------------------------------------------------------
     A         23      23       5       4       4       1
   -------------------------------------------------------
    Total      23      23       5       4       4       1
   -------------------------------------------------------

  ==> Iterations <==

                           Total Energy        Delta E     RMS |[F,P]|

   @DF-UKS iter   0:   -40.05254171042534   -4.00525e+01   1.44092e-04 
   @DF-UKS iter   1:   -40.05199624253508    5.45468e-04   3.46040e-05 DIIS
   @DF-UKS iter   2:   -40.05199643459025   -1.92055e-07   2.65698e-05 DIIS
   @DF-UKS iter   3:   -40.05199663699377   -2.02404e-07   4.74945e-06 DIIS
   @DF-UKS iter   4:   -40.05199664780859   -1.08148e-08   6.90611e-07 DIIS
   @DF-UKS iter   5:   -40.05199664823090   -4.22311e-10   6.04651e-08 DIIS
   @DF-UKS iter   6:   -40.05199664823310   -2.20268e-12   4.46489e-09 DIIS
  Energy and wave function converged.


  ==> Post-Iterations <==

   Electrons on quadrature grid:
      Nalpha   =    5.0000000558 ; deviation = 5.585e-08
      Nbeta    =    4.0000026758 ; deviation = 2.676e-06
      Ntotal   =    9.0000027317 ; deviation = 2.732e-06 

   @Spin Contamination Metric:   1.132870059E-03
   @S^2 Expected:                7.500000000E-01
   @S^2 Observed:                7.511328701E-01
   @S   Expected:                5.000000000E-01
   @S   Observed:                5.000000000E-01

    Orbital Energies [Eh]
    ---------------------

    Alpha Occupied:                                                       

       1A    -10.569452     2A     -1.072751     3A     -0.791922  
       4A     -0.791922     5A     -0.678242  

    Alpha Virtual:                                                        

       6A     -0.247306     7A     -0.187256     8A     -0.100764  
       9A     -0.100764    10A      0.192780    11A      0.297801  
      12A      0.297801    13A      0.459288    14A      0.485454  
      15A      0.573200    16A      0.573201    17A      0.647989  
      18A      1.251629    19A      1.526233    20A      1.612854  
      21A      1.612854    22A      1.984526    23A      3.731052  

    Beta Occupied:                                                        

       1A    -10.559731     2A     -1.029756     3A     -0.767555  
       4A     -0.767555  

    Beta Virtual:                                                         

       5A     -0.533231     6A     -0.214154     7A     -0.169143  
       8A     -0.088252     9A     -0.088252    10A      0.237211  
      11A      0.310076    12A      0.310076    13A      0.485777  
      14A      0.526624    15A      0.597445    16A      0.597445  
      17A      0.669148    18A      1.257228    19A      1.597160  
      20A      1.661673    21A      1.661673    22A      1.997423  
      23A      3.758814  

    Final Occupation by Irrep:
              A 
    DOCC [     4 ]
    SOCC [     1 ]
    NA   [     5 ]
    NB   [     4 ]

  @DF-UKS Final Energy:   -40.05199664823310

   => Energetics <=

    Nuclear Repulsion Energy =             13.0591760193042870
    One-Electron Energy =                 -75.3980441078169861
    Two-Electron Energy =                  27.5588569862297668
    DFT Exchange-Correlation Energy =      -5.2719855459501712
    Empirical Dispersion Energy =           0.0000000000000000
    VV10 Nonlocal Energy =                  0.0000000000000000
    Total Energy =                        -40.0519966482331000

  UHF NO Occupations:
  HONO-2 :    3  A 1.9998751
  HONO-1 :    4  A 1.9996835
  HONO-0 :    5  A 1.0000000
  LUNO+0 :    6  A 0.0003165
  LUNO+1 :    7  A 0.0001249
  LUNO+2 :    8  A 0.0001249
  LUNO+3 :    9  A 0.0000002


Computation Completed


Properties will be evaluated at   0.000000,   0.000000,   0.000000 [a0]

Properties computed using the SCF density matrix


 Multipole Moments:

 ------------------------------------------------------------------------------------
     Multipole            Electronic (a.u.)      Nuclear  (a.u.)        Total (a.u.)
 ------------------------------------------------------------------------------------

 L = 1.  Multiply by 2.5417464519 to convert [e a0] to [Debye]
 Dipole X            :         -0.0000104            0.0000085           -0.0000019
 Dipole Y            :          0.0000126           -0.0000102            0.0000024
 Dipole Z            :          0.0000164           -0.0000133            0.0000031
 Magnitude           :                                                    0.0000043

 ------------------------------------------------------------------------------------

*** tstop() called on iCarus at Sun Jun 29 21:43:35 2025
Module time:
	user time   =        nan seconds =        nan minutes
	system time =        nan seconds =        nan minutes
	total time  =          1 seconds =       0.02 minutes
Total time:
	user time   =        nan seconds =        nan minutes
	system time =        nan seconds =        nan minutes
	total time  =          5 seconds =       0.08 minutes

*** tstart() called on iCarus
*** at Sun Jun 29 21:43:35 2025


         ------------------------------------------------------------
                                   SCF GRAD                          
                          Rob Parrish, Justin Turney,                
                       Andy Simmonett, and Alex Sokolov              
         ------------------------------------------------------------

  ==> Geometry <==

    Molecular point group: c1
    Full point group: C1

    Geometry (in Angstrom), charge = 1, multiplicity = 2:

       Center              X                  Y                   Z               Mass       
    ------------   -----------------  -----------------  -----------------  -----------------
         C            0.000000901944    -0.000001084768    -0.000001412383    12.000000000000
         H           -0.494560145981     1.005484375116     0.088748815644     1.007825032230
         H           -0.162776867623    -0.994318126318    -0.498302347846     1.007825032230
         H           -0.204267843510    -0.342399640279     1.050953214695     1.007825032230
         H            0.861603955170     0.331234476248    -0.641398270110     1.007825032230

  Nuclear repulsion =   13.059176019304287

  ==> Basis Set <==

  Basis Set: 6-31G(D)
    Blend: 6-31G(D)
    Number of shells: 14
    Number of basis functions: 23
    Number of Cartesian functions: 23
    Spherical Harmonics?: false
    Max angular momentum: 2

  ==> DFJKGrad: Density-Fitted SCF Gradients <==

    Gradient:                    1
    J tasked:                  Yes
    K tasked:                  Yes
    wK tasked:                  No
    OpenMP threads:             10
    Integrals threads:          10
    Memory [MiB]:             -422
    Schwarz Cutoff:          1E-12
    Fitting Condition:       1E-10

   => Auxiliary Basis Set <=

  Basis Set: (6-31G(D) AUX)
    Blend: CC-PVDZ-JKFIT
    Number of shells: 60
    Number of basis functions: 181
    Number of Cartesian functions: 181
    Spherical Harmonics?: false
    Max angular momentum: 3

  ==> DFT Potential <==

   => LibXC <=

    Version 7.0.0
    S. Lehtola, C. Steigemann, M. J.T. Oliveira, and M. A.L. Marques.,  SoftwareX 7, 1–5 (2018) (10.1016/j.softx.2017.11.002)

   => Composite Functional: B3LYP <= 

    B3LYP Hyb-GGA Exchange-Correlation Functional

    P. J. Stephens, F. J. Devlin, C. F. Chabalowski, and M. J. Frisch.,  J. Phys. Chem. 98, 11623 (1994) (10.1021/j100096a001)

    Deriv               =              1
    GGA                 =           TRUE
    Meta                =          FALSE

    Exchange Hybrid     =           TRUE
    MP2 Hybrid          =          FALSE

   => Exchange Functionals <=

    0.0800   Slater exchange
    0.7200         Becke 88

   => Exact (HF) Exchange <=

    0.2000               HF 

   => Correlation Functionals <=

    0.1900   Vosko, Wilk & Nusair (VWN5_RPA)
    0.8100   Lee, Yang & Parr

   => LibXC Density Thresholds  <==

    XC_HYB_GGA_XC_B3LYP:  1.00E-15 

   => Molecular Quadrature <=

    Radial Scheme          =       TREUTLER
    Pruning Scheme         =           NONE
    Nuclear Scheme         =       TREUTLER

    Blocking Scheme        =         OCTREE
    BS radius alpha        =              1
    Pruning alpha          =              1
    Radial Points          =             75
    Spherical Points       =            302
    Total Points           =         109480
    Total Blocks           =            864
    Max Points             =            256
    Max Functions          =             23
    Weights Tolerance      =       1.00E-15


  -Total Gradient:
     Atom            X                  Y                   Z
    ------   -----------------  -----------------  -----------------
       1        0.000000070411    -0.000000021561    -0.000000072885
       2       -0.000264405315    -0.000279983469    -0.000278666988
       3       -0.000359797190     0.000290470977    -0.000110162358
       4        0.000464085761     0.000091498720    -0.000046942530
       5        0.000160046433    -0.000101964573     0.000435844598


*** tstop() called on iCarus at Sun Jun 29 21:43:35 2025
Module time:
	user time   =        nan seconds =        nan minutes
	system time =        nan seconds =        nan minutes
	total time  =          0 seconds =       0.00 minutes
Total time:
	user time   =        nan seconds =        nan minutes
	system time =        nan seconds =        nan minutes
	total time  =          5 seconds =       0.08 minutes

    			-----------------------------------------

    			 OPTKING 3.0: for geometry optimizations 

    			     By R.A. King, Bethel University     

    			        with contributions from          

    			    A.V. Copan, J. Cayton, A. Heide      

    			-----------------------------------------

    
	                            ===> Fragment 1 <== 

	 Z (Atomic Numbers)          Masses                          Geom                  
	       6.000000            12.000000          0.00000170 -0.00000205 -0.00000267   
	       1.000000             1.007825         -0.93458323  1.90009009  0.16771096   
	       1.000000             1.007825         -0.30760370 -1.87898894 -0.94165497   
	       1.000000             1.007825         -0.38601028 -0.64704155  1.98601375   
	       1.000000             1.007825          1.62819550  0.62594244 -1.21206707   

	 - Coordinate -           - BOHR/RAD -       - ANG/DEG -
	 R(1,2)           =         2.124130           1.124041
	 R(1,3)           =         2.124128           1.124040
	 R(1,4)           =         2.124130           1.124041
	 R(1,5)           =         2.124128           1.124040
	 B(2,1,3)         =         2.438085         139.691969
	 B(2,1,4)         =         1.689783          96.817414
	 B(2,1,5)         =         1.689787          96.817689
	 B(3,1,4)         =         1.689789          96.817765
	 B(3,1,5)         =         1.689793          96.818033
	 B(4,1,5)         =         2.438085         139.691976
	 O(2,1,3,4)       =         0.658099          37.706269
	 O(2,1,3,5)       =        -0.658095         -37.706057
	 O(2,1,4,5)       =         1.219052          69.846518
	 O(3,1,2,4)       =        -0.658098         -37.706237
	 O(3,1,2,5)       =         0.658094          37.706026
	 O(3,1,4,5)       =        -1.219033         -69.845451
	 O(4,1,2,3)       =         1.219050          69.846405
	 O(4,1,2,5)       =        -0.658099         -37.706287
	 O(4,1,3,5)       =         0.658094          37.706016
	 O(5,1,2,3)       =        -1.219035         -69.845571
	 O(5,1,2,4)       =         0.658098          37.706262
	 O(5,1,3,4)       =        -0.658094         -37.705991


	        --- Internal Coordinate Step in ANG or DEG, aJ/ANG or AJ/DEG ---
	-------------------------------------------------------------------------------
	           Coordinate      Previous         Force          Change          New 
	           ----------      --------        ------          ------        ------
	               R(1,2)       1.12404        0.00129        0.00009       1.12413
	               R(1,3)       1.12404        0.00129        0.00009       1.12413
	               R(1,4)       1.12404        0.00129        0.00009       1.12413
	               R(1,5)       1.12404        0.00129        0.00009       1.12413
	             B(2,1,3)     139.69197        0.00002        0.26383     139.95580
	             B(2,1,4)      96.81741       -0.00001       -0.08567      96.73174
	             B(2,1,5)      96.81769       -0.00001       -0.08570      96.73199
	             B(3,1,4)      96.81776       -0.00001       -0.08571      96.73206
	             B(3,1,5)      96.81803       -0.00001       -0.08573      96.73231
	             B(4,1,5)     139.69198        0.00002        0.26383     139.95581
	           O(2,1,3,4)      37.70627       -0.00001       -0.21134      37.49493
	           O(2,1,3,5)     -37.70606        0.00001        0.21132     -37.49474
	           O(2,1,4,5)      69.84652        0.00001        0.13187      69.97839
	           O(3,1,2,4)     -37.70624        0.00001        0.21133     -37.49490
	           O(3,1,2,5)      37.70603       -0.00001       -0.21131      37.49471
	           O(3,1,4,5)     -69.84545       -0.00001       -0.13196     -69.97741
	           O(4,1,2,3)      69.84641        0.00001        0.13188      69.97829
	           O(4,1,2,5)     -37.70629        0.00001        0.21134     -37.49495
	           O(4,1,3,5)      37.70602       -0.00001       -0.21131      37.49471
	           O(5,1,2,3)     -69.84557       -0.00001       -0.13195     -69.97752
	           O(5,1,2,4)      37.70626       -0.00001       -0.21133      37.49493
	           O(5,1,3,4)     -37.70599        0.00001        0.21131     -37.49468
	-------------------------------------------------------------------------------

	                                 ==> Convergence Check <==                                  
    
	Measures of convergence in internal coordinates in au.
    
	Criteria marked as inactive (o), active & met (*), and active & unmet ( ).

	----------------------------------------------------------------------------------------------
	   Step    Total Energy     Delta E     Max Force     RMS Force      Max Disp      RMS Disp   
	----------------------------------------------------------------------------------------------
	  Convergence Criteria     1.00e-06 *    3.00e-04 *             o    1.20e-03 *             o
	----------------------------------------------------------------------------------------------
	     6     -40.05199665   -7.51e-05      2.23e-04 *    1.54e-04 o    4.60e-03      2.87e-03 o  ~
	----------------------------------------------------------------------------------------------

Next Geometry in Ang 
	Fragment 1 (Ang)

	    C   0.0000008241  -0.0000010105  -0.0000013186
	    H  -0.4926771857   1.0063732980   0.0902884049
	    H  -0.1605873552  -0.9952759320  -0.4973047398
	    H  -0.2067961298  -0.3426762863   1.0504660113
	    H   0.8600598467   0.3315799308  -0.6434483579


    Structure for next step:
    Molecular point group: c1
    Full point group: C1

    Geometry (in Angstrom), charge = 1, multiplicity = 2:

    C            0.000000824082    -0.000001010547    -0.000001318578
    H           -0.492677185694     1.006373298028     0.090288404950
    H           -0.160587355219    -0.995275931984    -0.497304739797
    H           -0.206796129837    -0.342676286278     1.050466011330
    H            0.860059846667     0.331579930781    -0.643448357904


Scratch directory: D:\BaiduSyncdisk\py\MS\psi4_scratch/
gradient() will perform analytic gradient computation.
   => Libint2 <=

    Primary   basis highest AM E, G, H:  6, 6, -
    Auxiliary basis highest AM E, G, H:  7, 7, -
    Onebody   basis highest AM E, G, H:  -, -, -
    Solid Harmonics ordering:            Gaussian

*** tstart() called on iCarus
*** at Sun Jun 29 21:43:35 2025

   => Loading Basis Set <=

    Name: 6-31G(D)
    Role: ORBITAL
    Keyword: BASIS
    atoms 1   entry C          line   111 file D:\anaconda3\envs\nistpy\Library\share\psi4\basis\6-31g_d_.gbs 
    atoms 2-5 entry H          line    44 file D:\anaconda3\envs\nistpy\Library\share\psi4\basis\6-31g_d_.gbs 


         ---------------------------------------------------------
                                   SCF
               by Justin Turney, Rob Parrish, Andy Simmonett
                          and Daniel G. A. Smith
                              UKS Reference
                       10 Threads,   -562 MiB Core
         ---------------------------------------------------------

  ==> Geometry <==

    Molecular point group: c1
    Full point group: C1

    Geometry (in Angstrom), charge = 1, multiplicity = 2:

       Center              X                  Y                   Z               Mass       
    ------------   -----------------  -----------------  -----------------  -----------------
         C            0.000000824082    -0.000001010547    -0.000001318578    12.000000000000
         H           -0.492677185694     1.006373298028     0.090288404950     1.007825032230
         H           -0.160587355219    -0.995275931984    -0.497304739797     1.007825032230
         H           -0.206796129837    -0.342676286278     1.050466011330     1.007825032230
         H            0.860059846667     0.331579930781    -0.643448357904     1.007825032230

  Running in c1 symmetry.

  Rotational constants: A =      5.92388  B =      5.92388  C =      3.74858 [cm^-1]
  Rotational constants: A = 177593.33349  B = 177593.33008  C = 112379.73135 [MHz]
  Nuclear repulsion =   13.058532377167744

  Charge       = 1
  Multiplicity = 2
  Electrons    = 9
  Nalpha       = 5
  Nbeta        = 4

  ==> Algorithm <==

  SCF Algorithm Type is DF.
  DIIS enabled.
  MOM disabled.
  Fractional occupation disabled.
  Guess Type is READ.
  Energy threshold   = 1.00e-08
  Density threshold  = 1.00e-08
  Integral threshold = 1.00e-12

  ==> Primary Basis <==

  Basis Set: 6-31G(D)
    Blend: 6-31G(D)
    Number of shells: 14
    Number of basis functions: 23
    Number of Cartesian functions: 23
    Spherical Harmonics?: false
    Max angular momentum: 2

  ==> DFT Potential <==

   => LibXC <=

    Version 7.0.0
    S. Lehtola, C. Steigemann, M. J.T. Oliveira, and M. A.L. Marques.,  SoftwareX 7, 1–5 (2018) (10.1016/j.softx.2017.11.002)

   => Composite Functional: B3LYP <= 

    B3LYP Hyb-GGA Exchange-Correlation Functional

    P. J. Stephens, F. J. Devlin, C. F. Chabalowski, and M. J. Frisch.,  J. Phys. Chem. 98, 11623 (1994) (10.1021/j100096a001)

    Deriv               =              1
    GGA                 =           TRUE
    Meta                =          FALSE

    Exchange Hybrid     =           TRUE
    MP2 Hybrid          =          FALSE

   => Exchange Functionals <=

    0.0800   Slater exchange
    0.7200         Becke 88

   => Exact (HF) Exchange <=

    0.2000               HF 

   => Correlation Functionals <=

    0.1900   Vosko, Wilk & Nusair (VWN5_RPA)
    0.8100   Lee, Yang & Parr

   => LibXC Density Thresholds  <==

    XC_HYB_GGA_XC_B3LYP:  1.00E-15 

   => Molecular Quadrature <=

    Radial Scheme          =       TREUTLER
    Pruning Scheme         =           NONE
    Nuclear Scheme         =       TREUTLER

    Blocking Scheme        =         OCTREE
    BS radius alpha        =              1
    Pruning alpha          =              1
    Radial Points          =             75
    Spherical Points       =            302
    Total Points           =         109384
    Total Blocks           =            868
    Max Points             =            254
    Max Functions          =             23
    Weights Tolerance      =       1.00E-15

   => Loading Basis Set <=

    Name: (6-31G(D) AUX)
    Role: JKFIT
    Keyword: DF_BASIS_SCF
    atoms 1   entry C          line   121 file D:\anaconda3\envs\nistpy\Library\share\psi4\basis\cc-pvdz-jkfit.gbs 
    atoms 2-5 entry H          line    51 file D:\anaconda3\envs\nistpy\Library\share\psi4\basis\cc-pvdz-jkfit.gbs 

   => Loading Basis Set <=

    Name: 6-31G(D)
    Role: ORBITAL
    Keyword: BASIS
    atoms 1   entry C          line   111 file D:\anaconda3\envs\nistpy\Library\share\psi4\basis\6-31g_d_.gbs 
    atoms 2-5 entry H          line    44 file D:\anaconda3\envs\nistpy\Library\share\psi4\basis\6-31g_d_.gbs 

  Reading orbitals from file D:\BaiduSyncdisk\py\MS\psi4_scratch/psi4_calculation.default.34212.180.npy, no projection.

  ==> Integral Setup <==

  DFHelper Memory: AOs need 0.001 GiB; user supplied 5.515 GiB. 
  Using in-core AOs.

  ==> MemDFJK: Density-Fitted J/K Matrices <==

    J tasked:                   Yes
    K tasked:                   Yes
    wK tasked:                   No
    OpenMP threads:              10
    Memory [MiB]:              5647
    Algorithm:                 Core
    Schwarz Cutoff:           1E-12
    Mask sparsity (%):       0.0000
    Fitting Condition:        1E-10

   => Auxiliary Basis Set <=

  Basis Set: (6-31G(D) AUX)
    Blend: CC-PVDZ-JKFIT
    Number of shells: 60
    Number of basis functions: 181
    Number of Cartesian functions: 181
    Spherical Harmonics?: false
    Max angular momentum: 3

  Cached 100.0% of DFT collocation blocks in 0.073 [GiB].

  Minimum eigenvalue in the overlap matrix is 2.3833806561E-02.
  Reciprocal condition number of the overlap matrix is 4.3248728502E-03.
    Using symmetric orthogonalization.

  ==> Pre-Iterations <==

  SCF Guess: Orbitals guess was supplied from a previous computation.

   -------------------------------------------------------
    Irrep   Nso     Nmo     Nalpha   Nbeta   Ndocc  Nsocc
   -------------------------------------------------------
     A         23      23       5       4       4       1
   -------------------------------------------------------
    Total      23      23       5       4       4       1
   -------------------------------------------------------

  ==> Iterations <==

                           Total Energy        Delta E     RMS |[F,P]|

   @DF-UKS iter   0:   -40.05175208536993   -4.00518e+01   3.80842e-05 
   @DF-UKS iter   1:   -40.05200131579426   -2.49230e-04   1.13878e-05 DIIS
   @DF-UKS iter   2:   -40.05200133050091   -1.47067e-08   9.85352e-06 DIIS
   @DF-UKS iter   3:   -40.05200135805099   -2.75501e-08   1.06589e-06 DIIS
   @DF-UKS iter   4:   -40.05200135873338   -6.82391e-10   2.03691e-07 DIIS
   @DF-UKS iter   5:   -40.05200135876623   -3.28555e-11   2.55881e-08 DIIS
   @DF-UKS iter   6:   -40.05200135876652   -2.91323e-13   1.23093e-09 DIIS
  Energy and wave function converged.


  ==> Post-Iterations <==

   Electrons on quadrature grid:
      Nalpha   =    5.0000011040 ; deviation = 1.104e-06
      Nbeta    =    3.9999999655 ; deviation = -3.454e-08
      Ntotal   =    9.0000010695 ; deviation = 1.069e-06 

   @Spin Contamination Metric:   1.127447671E-03
   @S^2 Expected:                7.500000000E-01
   @S^2 Observed:                7.511274477E-01
   @S   Expected:                5.000000000E-01
   @S   Observed:                5.000000000E-01

    Orbital Energies [Eh]
    ---------------------

    Alpha Occupied:                                                       

       1A    -10.569399     2A     -1.072719     3A     -0.792095  
       4A     -0.792095     5A     -0.677277  

    Alpha Virtual:                                                        

       6A     -0.248312     7A     -0.187443     8A     -0.100493  
       9A     -0.100493    10A      0.193008    11A      0.299081  
      12A      0.299081    13A      0.458728    14A      0.484553  
      15A      0.573470    16A      0.573470    17A      0.647249  
      18A      1.251462    19A      1.529403    20A      1.610290  
      21A      1.610290    22A      1.986001    23A      3.730639  

    Beta Occupied:                                                        

       1A    -10.559658     2A     -1.029765     3A     -0.767775  
       4A     -0.767775  

    Beta Virtual:                                                         

       5A     -0.532251     6A     -0.215174     7A     -0.169294  
       8A     -0.088016     9A     -0.088016    10A      0.237573  
      11A      0.311358    12A      0.311358    13A      0.485074  
      14A      0.525670    15A      0.597686    16A      0.597686  
      17A      0.668501    18A      1.257090    19A      1.600371  
      20A      1.659160    21A      1.659160    22A      1.998919  
      23A      3.758449  

    Final Occupation by Irrep:
              A 
    DOCC [     4 ]
    SOCC [     1 ]
    NA   [     5 ]
    NB   [     4 ]

  @DF-UKS Final Energy:   -40.05200135876652

   => Energetics <=

    Nuclear Repulsion Energy =             13.0585323771677437
    One-Electron Energy =                 -75.3971865159934538
    Two-Electron Energy =                  27.5585154932520560
    DFT Exchange-Correlation Energy =      -5.2718627131928679
    Empirical Dispersion Energy =           0.0000000000000000
    VV10 Nonlocal Energy =                  0.0000000000000000
    Total Energy =                        -40.0520013587665247

  UHF NO Occupations:
  HONO-2 :    3  A 1.9998771
  HONO-1 :    4  A 1.9996822
  HONO-0 :    5  A 1.0000000
  LUNO+0 :    6  A 0.0003178
  LUNO+1 :    7  A 0.0001229
  LUNO+2 :    8  A 0.0001229
  LUNO+3 :    9  A 0.0000002


Computation Completed


Properties will be evaluated at   0.000000,   0.000000,   0.000000 [a0]

Properties computed using the SCF density matrix


 Multipole Moments:

 ------------------------------------------------------------------------------------
     Multipole            Electronic (a.u.)      Nuclear  (a.u.)        Total (a.u.)
 ------------------------------------------------------------------------------------

 L = 1.  Multiply by 2.5417464519 to convert [e a0] to [Debye]
 Dipole X            :         -0.0000096            0.0000078           -0.0000018
 Dipole Y            :          0.0000117           -0.0000095            0.0000022
 Dipole Z            :          0.0000153           -0.0000125            0.0000029
 Magnitude           :                                                    0.0000040

 ------------------------------------------------------------------------------------

*** tstop() called on iCarus at Sun Jun 29 21:43:35 2025
Module time:
	user time   =        nan seconds =        nan minutes
	system time =        nan seconds =        nan minutes
	total time  =          0 seconds =       0.00 minutes
Total time:
	user time   =        nan seconds =        nan minutes
	system time =        nan seconds =        nan minutes
	total time  =          5 seconds =       0.08 minutes

*** tstart() called on iCarus
*** at Sun Jun 29 21:43:35 2025


         ------------------------------------------------------------
                                   SCF GRAD                          
                          Rob Parrish, Justin Turney,                
                       Andy Simmonett, and Alex Sokolov              
         ------------------------------------------------------------

  ==> Geometry <==

    Molecular point group: c1
    Full point group: C1

    Geometry (in Angstrom), charge = 1, multiplicity = 2:

       Center              X                  Y                   Z               Mass       
    ------------   -----------------  -----------------  -----------------  -----------------
         C            0.000000824082    -0.000001010547    -0.000001318578    12.000000000000
         H           -0.492677185694     1.006373298028     0.090288404950     1.007825032230
         H           -0.160587355219    -0.995275931984    -0.497304739797     1.007825032230
         H           -0.206796129837    -0.342676286278     1.050466011330     1.007825032230
         H            0.860059846667     0.331579930781    -0.643448357904     1.007825032230

  Nuclear repulsion =   13.058532377167744

  ==> Basis Set <==

  Basis Set: 6-31G(D)
    Blend: 6-31G(D)
    Number of shells: 14
    Number of basis functions: 23
    Number of Cartesian functions: 23
    Spherical Harmonics?: false
    Max angular momentum: 2

  ==> DFJKGrad: Density-Fitted SCF Gradients <==

    Gradient:                    1
    J tasked:                  Yes
    K tasked:                  Yes
    wK tasked:                  No
    OpenMP threads:             10
    Integrals threads:          10
    Memory [MiB]:             -422
    Schwarz Cutoff:          1E-12
    Fitting Condition:       1E-10

   => Auxiliary Basis Set <=

  Basis Set: (6-31G(D) AUX)
    Blend: CC-PVDZ-JKFIT
    Number of shells: 60
    Number of basis functions: 181
    Number of Cartesian functions: 181
    Spherical Harmonics?: false
    Max angular momentum: 3

  ==> DFT Potential <==

   => LibXC <=

    Version 7.0.0
    S. Lehtola, C. Steigemann, M. J.T. Oliveira, and M. A.L. Marques.,  SoftwareX 7, 1–5 (2018) (10.1016/j.softx.2017.11.002)

   => Composite Functional: B3LYP <= 

    B3LYP Hyb-GGA Exchange-Correlation Functional

    P. J. Stephens, F. J. Devlin, C. F. Chabalowski, and M. J. Frisch.,  J. Phys. Chem. 98, 11623 (1994) (10.1021/j100096a001)

    Deriv               =              1
    GGA                 =           TRUE
    Meta                =          FALSE

    Exchange Hybrid     =           TRUE
    MP2 Hybrid          =          FALSE

   => Exchange Functionals <=

    0.0800   Slater exchange
    0.7200         Becke 88

   => Exact (HF) Exchange <=

    0.2000               HF 

   => Correlation Functionals <=

    0.1900   Vosko, Wilk & Nusair (VWN5_RPA)
    0.8100   Lee, Yang & Parr

   => LibXC Density Thresholds  <==

    XC_HYB_GGA_XC_B3LYP:  1.00E-15 

   => Molecular Quadrature <=

    Radial Scheme          =       TREUTLER
    Pruning Scheme         =           NONE
    Nuclear Scheme         =       TREUTLER

    Blocking Scheme        =         OCTREE
    BS radius alpha        =              1
    Pruning alpha          =              1
    Radial Points          =             75
    Spherical Points       =            302
    Total Points           =         109384
    Total Blocks           =            868
    Max Points             =            254
    Max Functions          =             23
    Weights Tolerance      =       1.00E-15


  -Total Gradient:
     Atom            X                  Y                   Z
    ------   -----------------  -----------------  -----------------
       1        0.000000082302    -0.000000079975    -0.000000117936
       2       -0.000065544133    -0.000094237409    -0.000078795320
       3       -0.000097359666     0.000096975698    -0.000022598462
       4        0.000132358983     0.000030931577    -0.000030161786
       5        0.000030462488    -0.000033589977     0.000131673548


*** tstop() called on iCarus at Sun Jun 29 21:43:35 2025
Module time:
	user time   =        nan seconds =        nan minutes
	system time =        nan seconds =        nan minutes
	total time  =          0 seconds =       0.00 minutes
Total time:
	user time   =        nan seconds =        nan minutes
	system time =        nan seconds =        nan minutes
	total time  =          5 seconds =       0.08 minutes

    			-----------------------------------------

    			 OPTKING 3.0: for geometry optimizations 

    			     By R.A. King, Bethel University     

    			        with contributions from          

    			    A.V. Copan, J. Cayton, A. Heide      

    			-----------------------------------------

    
	                            ===> Fragment 1 <== 

	 Z (Atomic Numbers)          Masses                          Geom                  
	       6.000000            12.000000          0.00000156 -0.00000191 -0.00000249   
	       1.000000             1.007825         -0.93102495  1.90176991  0.17062036   
	       1.000000             1.007825         -0.30346612 -1.88079893 -0.93976976   
	       1.000000             1.007825         -0.39078805 -0.64756433  1.98509307   
	       1.000000             1.007825          1.62527756  0.62659526 -1.21594117   

	 - Coordinate -           - BOHR/RAD -       - ANG/DEG -
	 R(1,2)           =         2.124302           1.124132
	 R(1,3)           =         2.124300           1.124131
	 R(1,4)           =         2.124302           1.124132
	 R(1,5)           =         2.124301           1.124131
	 B(2,1,3)         =         2.442690         139.955802
	 B(2,1,4)         =         1.688287          96.731739
	 B(2,1,5)         =         1.688292          96.731990
	 B(3,1,4)         =         1.688293          96.732057
	 B(3,1,5)         =         1.688297          96.732305
	 B(4,1,5)         =         2.442690         139.955806
	 O(2,1,3,4)       =         0.654410          37.494932
	 O(2,1,3,5)       =        -0.654407         -37.494739
	 O(2,1,4,5)       =         1.221353          69.978390
	 O(3,1,2,4)       =        -0.654410         -37.494903
	 O(3,1,2,5)       =         0.654406          37.494711
	 O(3,1,4,5)       =        -1.221336         -69.977412
	 O(4,1,2,3)       =         1.221352          69.978288
	 O(4,1,2,5)       =        -0.654410         -37.494951
	 O(4,1,3,5)       =         0.654406          37.494706
	 O(5,1,2,3)       =        -1.221338         -69.977518
	 O(5,1,2,4)       =         0.654410          37.494928
	 O(5,1,3,4)       =        -0.654406         -37.494684


	        --- Internal Coordinate Step in ANG or DEG, aJ/ANG or AJ/DEG ---
	-------------------------------------------------------------------------------
	           Coordinate      Previous         Force          Change          New 
	           ----------      --------        ------          ------        ------
	               R(1,2)       1.12413        0.00051        0.00006       1.12419
	               R(1,3)       1.12413        0.00051        0.00006       1.12419
	               R(1,4)       1.12413        0.00051        0.00006       1.12419
	               R(1,5)       1.12413        0.00051        0.00006       1.12419
	             B(2,1,3)     139.95580        0.00000        0.08477     140.04057
	             B(2,1,4)      96.73174       -0.00000       -0.02741      96.70433
	             B(2,1,5)      96.73199       -0.00000       -0.02743      96.70456
	             B(3,1,4)      96.73206       -0.00000       -0.02744      96.70462
	             B(3,1,5)      96.73231       -0.00000       -0.02745      96.70485
	             B(4,1,5)     139.95581        0.00000        0.08477     140.04057
	           O(2,1,3,4)      37.49493       -0.00000       -0.06807      37.42687
	           O(2,1,3,5)     -37.49474        0.00000        0.06805     -37.42669
	           O(2,1,4,5)      69.97839        0.00000        0.04235      70.02074
	           O(3,1,2,4)     -37.49490        0.00000        0.06806     -37.42684
	           O(3,1,2,5)      37.49471       -0.00000       -0.06805      37.42666
	           O(3,1,4,5)     -69.97741       -0.00000       -0.04242     -70.01983
	           O(4,1,2,3)      69.97829        0.00000        0.04236      70.02064
	           O(4,1,2,5)     -37.49495        0.00000        0.06807     -37.42688
	           O(4,1,3,5)      37.49471       -0.00000       -0.06805      37.42666
	           O(5,1,2,3)     -69.97752       -0.00000       -0.04241     -70.01993
	           O(5,1,2,4)      37.49493       -0.00000       -0.06807      37.42686
	           O(5,1,3,4)     -37.49468        0.00000        0.06805     -37.42664
	-------------------------------------------------------------------------------

	                                 ==> Convergence Check <==                                  
    
	Measures of convergence in internal coordinates in au.
    
	Criteria marked as inactive (o), active & met (*), and active & unmet ( ).

	----------------------------------------------------------------------------------------------
	   Step    Total Energy     Delta E     Max Force     RMS Force      Max Disp      RMS Disp   
	----------------------------------------------------------------------------------------------
	  Convergence Criteria     1.00e-06 *    3.00e-04 *             o    1.20e-03 *             o
	----------------------------------------------------------------------------------------------
	     7     -40.05200136   -4.71e-06      6.20e-05 *    4.68e-05 o    1.48e-03      9.25e-04 o  ~
	----------------------------------------------------------------------------------------------

Next Geometry in Ang 
	Fragment 1 (Ang)

	    C   0.0000007612  -0.0000009418  -0.0000012298
	    H  -0.4920829911   1.0066811768   0.0907850677
	    H  -0.1598872699  -0.9956055966  -0.4969951446
	    H  -0.2076130965  -0.3427728117   1.0503326275
	    H   0.8595825963   0.3316981734  -0.6441213208


    Structure for next step:
    Molecular point group: c1
    Full point group: C1

    Geometry (in Angstrom), charge = 1, multiplicity = 2:

    C            0.000000761219    -0.000000941829    -0.000001229844
    H           -0.492082991129     1.006681176815     0.090785067690
    H           -0.159887269893    -0.995605596599    -0.496995144568
    H           -0.207613096452    -0.342772811743     1.050332627475
    H            0.859582596255     0.331698173355    -0.644121320753


Scratch directory: D:\BaiduSyncdisk\py\MS\psi4_scratch/
gradient() will perform analytic gradient computation.
   => Libint2 <=

    Primary   basis highest AM E, G, H:  6, 6, -
    Auxiliary basis highest AM E, G, H:  7, 7, -
    Onebody   basis highest AM E, G, H:  -, -, -
    Solid Harmonics ordering:            Gaussian

*** tstart() called on iCarus
*** at Sun Jun 29 21:43:35 2025

   => Loading Basis Set <=

    Name: 6-31G(D)
    Role: ORBITAL
    Keyword: BASIS
    atoms 1   entry C          line   111 file D:\anaconda3\envs\nistpy\Library\share\psi4\basis\6-31g_d_.gbs 
    atoms 2-5 entry H          line    44 file D:\anaconda3\envs\nistpy\Library\share\psi4\basis\6-31g_d_.gbs 


         ---------------------------------------------------------
                                   SCF
               by Justin Turney, Rob Parrish, Andy Simmonett
                          and Daniel G. A. Smith
                              UKS Reference
                       10 Threads,   -562 MiB Core
         ---------------------------------------------------------

  ==> Geometry <==

    Molecular point group: c1
    Full point group: C1

    Geometry (in Angstrom), charge = 1, multiplicity = 2:

       Center              X                  Y                   Z               Mass       
    ------------   -----------------  -----------------  -----------------  -----------------
         C            0.000000761219    -0.000000941829    -0.000001229844    12.000000000000
         H           -0.492082991129     1.006681176815     0.090785067690     1.007825032230
         H           -0.159887269893    -0.995605596599    -0.496995144568     1.007825032230
         H           -0.207613096452    -0.342772811743     1.050332627475     1.007825032230
         H            0.859582596255     0.331698173355    -0.644121320753     1.007825032230

  Running in c1 symmetry.

  Rotational constants: A =      5.92582  B =      5.92582  C =      3.74620 [cm^-1]
  Rotational constants: A = 177651.46911  B = 177651.46763  C = 112308.17785 [MHz]
  Nuclear repulsion =   13.058023150158014

  Charge       = 1
  Multiplicity = 2
  Electrons    = 9
  Nalpha       = 5
  Nbeta        = 4

  ==> Algorithm <==

  SCF Algorithm Type is DF.
  DIIS enabled.
  MOM disabled.
  Fractional occupation disabled.
  Guess Type is READ.
  Energy threshold   = 1.00e-08
  Density threshold  = 1.00e-08
  Integral threshold = 1.00e-12

  ==> Primary Basis <==

  Basis Set: 6-31G(D)
    Blend: 6-31G(D)
    Number of shells: 14
    Number of basis functions: 23
    Number of Cartesian functions: 23
    Spherical Harmonics?: false
    Max angular momentum: 2

  ==> DFT Potential <==

   => LibXC <=

    Version 7.0.0
    S. Lehtola, C. Steigemann, M. J.T. Oliveira, and M. A.L. Marques.,  SoftwareX 7, 1–5 (2018) (10.1016/j.softx.2017.11.002)

   => Composite Functional: B3LYP <= 

    B3LYP Hyb-GGA Exchange-Correlation Functional

    P. J. Stephens, F. J. Devlin, C. F. Chabalowski, and M. J. Frisch.,  J. Phys. Chem. 98, 11623 (1994) (10.1021/j100096a001)

    Deriv               =              1
    GGA                 =           TRUE
    Meta                =          FALSE

    Exchange Hybrid     =           TRUE
    MP2 Hybrid          =          FALSE

   => Exchange Functionals <=

    0.0800   Slater exchange
    0.7200         Becke 88

   => Exact (HF) Exchange <=

    0.2000               HF 

   => Correlation Functionals <=

    0.1900   Vosko, Wilk & Nusair (VWN5_RPA)
    0.8100   Lee, Yang & Parr

   => LibXC Density Thresholds  <==

    XC_HYB_GGA_XC_B3LYP:  1.00E-15 

   => Molecular Quadrature <=

    Radial Scheme          =       TREUTLER
    Pruning Scheme         =           NONE
    Nuclear Scheme         =       TREUTLER

    Blocking Scheme        =         OCTREE
    BS radius alpha        =              1
    Pruning alpha          =              1
    Radial Points          =             75
    Spherical Points       =            302
    Total Points           =         109408
    Total Blocks           =            873
    Max Points             =            256
    Max Functions          =             23
    Weights Tolerance      =       1.00E-15

   => Loading Basis Set <=

    Name: (6-31G(D) AUX)
    Role: JKFIT
    Keyword: DF_BASIS_SCF
    atoms 1   entry C          line   121 file D:\anaconda3\envs\nistpy\Library\share\psi4\basis\cc-pvdz-jkfit.gbs 
    atoms 2-5 entry H          line    51 file D:\anaconda3\envs\nistpy\Library\share\psi4\basis\cc-pvdz-jkfit.gbs 

   => Loading Basis Set <=

    Name: 6-31G(D)
    Role: ORBITAL
    Keyword: BASIS
    atoms 1   entry C          line   111 file D:\anaconda3\envs\nistpy\Library\share\psi4\basis\6-31g_d_.gbs 
    atoms 2-5 entry H          line    44 file D:\anaconda3\envs\nistpy\Library\share\psi4\basis\6-31g_d_.gbs 

  Reading orbitals from file D:\BaiduSyncdisk\py\MS\psi4_scratch/psi4_calculation.default.34212.180.npy, no projection.

  ==> Integral Setup <==

  DFHelper Memory: AOs need 0.001 GiB; user supplied 5.515 GiB. 
  Using in-core AOs.

  ==> MemDFJK: Density-Fitted J/K Matrices <==

    J tasked:                   Yes
    K tasked:                   Yes
    wK tasked:                   No
    OpenMP threads:              10
    Memory [MiB]:              5647
    Algorithm:                 Core
    Schwarz Cutoff:           1E-12
    Mask sparsity (%):       0.0000
    Fitting Condition:        1E-10

   => Auxiliary Basis Set <=

  Basis Set: (6-31G(D) AUX)
    Blend: CC-PVDZ-JKFIT
    Number of shells: 60
    Number of basis functions: 181
    Number of Cartesian functions: 181
    Spherical Harmonics?: false
    Max angular momentum: 3

  Cached 100.0% of DFT collocation blocks in 0.073 [GiB].

  Minimum eigenvalue in the overlap matrix is 2.3854377692E-02.
  Reciprocal condition number of the overlap matrix is 4.3286915362E-03.
    Using symmetric orthogonalization.

  ==> Pre-Iterations <==

  SCF Guess: Orbitals guess was supplied from a previous computation.

   -------------------------------------------------------
    Irrep   Nso     Nmo     Nalpha   Nbeta   Ndocc  Nsocc
   -------------------------------------------------------
     A         23      23       5       4       4       1
   -------------------------------------------------------
    Total      23      23       5       4       4       1
   -------------------------------------------------------

  ==> Iterations <==

                           Total Energy        Delta E     RMS |[F,P]|

   @DF-UKS iter   0:   -40.05186925245899   -4.00519e+01   1.23919e-05 
   @DF-UKS iter   1:   -40.05200161434414   -1.32362e-04   3.94774e-06 DIIS
   @DF-UKS iter   2:   -40.05200161593258   -1.58845e-09   3.51432e-06 DIIS
   @DF-UKS iter   3:   -40.05200161942396   -3.49138e-09   3.22294e-07 DIIS
   @DF-UKS iter   4:   -40.05200161949036   -6.64002e-11   7.18258e-08 DIIS
   @DF-UKS iter   5:   -40.05200161949424   -3.87956e-12   9.13286e-09 DIIS
  Energy and wave function converged.


  ==> Post-Iterations <==

   Electrons on quadrature grid:
      Nalpha   =    4.9999983204 ; deviation = -1.680e-06
      Nbeta    =    3.9999982582 ; deviation = -1.742e-06
      Ntotal   =    8.9999965786 ; deviation = -3.421e-06 

   @Spin Contamination Metric:   1.125766428E-03
   @S^2 Expected:                7.500000000E-01
   @S^2 Observed:                7.511257664E-01
   @S   Expected:                5.000000000E-01
   @S   Observed:                5.000000000E-01

    Orbital Energies [Eh]
    ---------------------

    Alpha Occupied:                                                       

       1A    -10.569385     2A     -1.072695     3A     -0.792139  
       4A     -0.792139     5A     -0.676961  

    Alpha Virtual:                                                        

       6A     -0.248642     7A     -0.187511     8A     -0.100413  
       9A     -0.100413    10A      0.193089    11A      0.299508  
      12A      0.299508    13A      0.458546    14A      0.484254  
      15A      0.573538    16A      0.573538    17A      0.647007  
      18A      1.251420    19A      1.530407    20A      1.609449  
      21A      1.609449    22A      1.986431    23A      3.730494  

    Beta Occupied:                                                        

       1A    -10.559638     2A     -1.029755     3A     -0.767835  
       4A     -0.767835  

    Beta Virtual:                                                         

       5A     -0.531931     6A     -0.215508     7A     -0.169349  
       8A     -0.087947     9A     -0.087947    10A      0.237698  
      11A      0.311784    12A      0.311784    13A      0.484846  
      14A      0.525352    15A      0.597744    16A      0.597744  
      17A      0.668288    18A      1.257057    19A      1.601390  
      20A      1.658334    21A      1.658334    22A      1.999355  
      23A      3.758319  

    Final Occupation by Irrep:
              A 
    DOCC [     4 ]
    SOCC [     1 ]
    NA   [     5 ]
    NB   [     4 ]

  @DF-UKS Final Energy:   -40.05200161949424

   => Energetics <=

    Nuclear Repulsion Energy =             13.0580231501580144
    One-Electron Energy =                 -75.3963907026259221
    Two-Electron Energy =                  27.5581633300629001
    DFT Exchange-Correlation Energy =      -5.2717973970892400
    Empirical Dispersion Energy =           0.0000000000000000
    VV10 Nonlocal Energy =                  0.0000000000000000
    Total Energy =                        -40.0520016194942414

  UHF NO Occupations:
  HONO-2 :    3  A 1.9998777
  HONO-1 :    4  A 1.9996818
  HONO-0 :    5  A 1.0000000
  LUNO+0 :    6  A 0.0003182
  LUNO+1 :    7  A 0.0001223
  LUNO+2 :    8  A 0.0001223
  LUNO+3 :    9  A 0.0000002


Computation Completed


Properties will be evaluated at   0.000000,   0.000000,   0.000000 [a0]

Properties computed using the SCF density matrix


 Multipole Moments:

 ------------------------------------------------------------------------------------
     Multipole            Electronic (a.u.)      Nuclear  (a.u.)        Total (a.u.)
 ------------------------------------------------------------------------------------

 L = 1.  Multiply by 2.5417464519 to convert [e a0] to [Debye]
 Dipole X            :         -0.0000088            0.0000072           -0.0000016
 Dipole Y            :          0.0000109           -0.0000089            0.0000020
 Dipole Z            :          0.0000143           -0.0000116            0.0000027
 Magnitude           :                                                    0.0000038

 ------------------------------------------------------------------------------------

*** tstop() called on iCarus at Sun Jun 29 21:43:36 2025
Module time:
	user time   =        nan seconds =        nan minutes
	system time =        nan seconds =        nan minutes
	total time  =          1 seconds =       0.02 minutes
Total time:
	user time   =        nan seconds =        nan minutes
	system time =        nan seconds =        nan minutes
	total time  =          6 seconds =       0.10 minutes

*** tstart() called on iCarus
*** at Sun Jun 29 21:43:36 2025


         ------------------------------------------------------------
                                   SCF GRAD                          
                          Rob Parrish, Justin Turney,                
                       Andy Simmonett, and Alex Sokolov              
         ------------------------------------------------------------

  ==> Geometry <==

    Molecular point group: c1
    Full point group: C1

    Geometry (in Angstrom), charge = 1, multiplicity = 2:

       Center              X                  Y                   Z               Mass       
    ------------   -----------------  -----------------  -----------------  -----------------
         C            0.000000761219    -0.000000941829    -0.000001229844    12.000000000000
         H           -0.492082991129     1.006681176815     0.090785067690     1.007825032230
         H           -0.159887269893    -0.995605596599    -0.496995144568     1.007825032230
         H           -0.207613096452    -0.342772811743     1.050332627475     1.007825032230
         H            0.859582596255     0.331698173355    -0.644121320753     1.007825032230

  Nuclear repulsion =   13.058023150158014

  ==> Basis Set <==

  Basis Set: 6-31G(D)
    Blend: 6-31G(D)
    Number of shells: 14
    Number of basis functions: 23
    Number of Cartesian functions: 23
    Spherical Harmonics?: false
    Max angular momentum: 2

  ==> DFJKGrad: Density-Fitted SCF Gradients <==

    Gradient:                    1
    J tasked:                  Yes
    K tasked:                  Yes
    wK tasked:                  No
    OpenMP threads:             10
    Integrals threads:          10
    Memory [MiB]:             -422
    Schwarz Cutoff:          1E-12
    Fitting Condition:       1E-10

   => Auxiliary Basis Set <=

  Basis Set: (6-31G(D) AUX)
    Blend: CC-PVDZ-JKFIT
    Number of shells: 60
    Number of basis functions: 181
    Number of Cartesian functions: 181
    Spherical Harmonics?: false
    Max angular momentum: 3

  ==> DFT Potential <==

   => LibXC <=

    Version 7.0.0
    S. Lehtola, C. Steigemann, M. J.T. Oliveira, and M. A.L. Marques.,  SoftwareX 7, 1–5 (2018) (10.1016/j.softx.2017.11.002)

   => Composite Functional: B3LYP <= 

    B3LYP Hyb-GGA Exchange-Correlation Functional

    P. J. Stephens, F. J. Devlin, C. F. Chabalowski, and M. J. Frisch.,  J. Phys. Chem. 98, 11623 (1994) (10.1021/j100096a001)

    Deriv               =              1
    GGA                 =           TRUE
    Meta                =          FALSE

    Exchange Hybrid     =           TRUE
    MP2 Hybrid          =          FALSE

   => Exchange Functionals <=

    0.0800   Slater exchange
    0.7200         Becke 88

   => Exact (HF) Exchange <=

    0.2000               HF 

   => Correlation Functionals <=

    0.1900   Vosko, Wilk & Nusair (VWN5_RPA)
    0.8100   Lee, Yang & Parr

   => LibXC Density Thresholds  <==

    XC_HYB_GGA_XC_B3LYP:  1.00E-15 

   => Molecular Quadrature <=

    Radial Scheme          =       TREUTLER
    Pruning Scheme         =           NONE
    Nuclear Scheme         =       TREUTLER

    Blocking Scheme        =         OCTREE
    BS radius alpha        =              1
    Pruning alpha          =              1
    Radial Points          =             75
    Spherical Points       =            302
    Total Points           =         109408
    Total Blocks           =            873
    Max Points             =            256
    Max Functions          =             23
    Weights Tolerance      =       1.00E-15


  -Total Gradient:
     Atom            X                  Y                   Z
    ------   -----------------  -----------------  -----------------
       1        0.000000083287    -0.000000097357    -0.000000129069
       2       -0.000007364129    -0.000020152267    -0.000012382722
       3       -0.000013934142     0.000020517767    -0.000000779924
       4        0.000021435235     0.000006527165    -0.000010569826
       5       -0.000000220361    -0.000006795169     0.000023861721


*** tstop() called on iCarus at Sun Jun 29 21:43:36 2025
Module time:
	user time   =        nan seconds =        nan minutes
	system time =        nan seconds =        nan minutes
	total time  =          0 seconds =       0.00 minutes
Total time:
	user time   =        nan seconds =        nan minutes
	system time =        nan seconds =        nan minutes
	total time  =          6 seconds =       0.10 minutes

    			-----------------------------------------

    			 OPTKING 3.0: for geometry optimizations 

    			     By R.A. King, Bethel University     

    			        with contributions from          

    			    A.V. Copan, J. Cayton, A. Heide      

    			-----------------------------------------

    
	                            ===> Fragment 1 <== 

	 Z (Atomic Numbers)          Masses                          Geom                  
	       6.000000            12.000000          0.00000144 -0.00000178 -0.00000232   
	       1.000000             1.007825         -0.92990208  1.90235172  0.17155891   
	       1.000000             1.007825         -0.30214315 -1.88142191 -0.93918471   
	       1.000000             1.007825         -0.39233189 -0.64774674  1.98484101   
	       1.000000             1.007825          1.62437569  0.62681870 -1.21721289   

	 - Coordinate -           - BOHR/RAD -       - ANG/DEG -
	 R(1,2)           =         2.124406           1.124187
	 R(1,3)           =         2.124405           1.124187
	 R(1,4)           =         2.124406           1.124187
	 R(1,5)           =         2.124405           1.124187
	 B(2,1,3)         =         2.444169         140.040570
	 B(2,1,4)         =         1.687809          96.704328
	 B(2,1,5)         =         1.687813          96.704559
	 B(3,1,4)         =         1.687814          96.704621
	 B(3,1,5)         =         1.687818          96.704851
	 B(4,1,5)         =         2.444169         140.040572
	 O(2,1,3,4)       =         0.653222          37.426865
	 O(2,1,3,5)       =        -0.653219         -37.426687
	 O(2,1,4,5)       =         1.222092          70.020737
	 O(3,1,2,4)       =        -0.653222         -37.426839
	 O(3,1,2,5)       =         0.653218          37.426661
	 O(3,1,4,5)       =        -1.222077         -70.019833
	 O(4,1,2,3)       =         1.222091          70.020643
	 O(4,1,2,5)       =        -0.653222         -37.426884
	 O(4,1,3,5)       =         0.653218          37.426658
	 O(5,1,2,3)       =        -1.222078         -70.019929
	 O(5,1,2,4)       =         0.653222          37.426863
	 O(5,1,3,4)       =        -0.653218         -37.426638


	        --- Internal Coordinate Step in ANG or DEG, aJ/ANG or AJ/DEG ---
	-------------------------------------------------------------------------------
	           Coordinate      Previous         Force          Change          New 
	           ----------      --------        ------          ------        ------
	               R(1,2)       1.12419        0.00013        0.00002       1.12421
	               R(1,3)       1.12419        0.00013        0.00002       1.12421
	               R(1,4)       1.12419        0.00013        0.00002       1.12421
	               R(1,5)       1.12419        0.00013        0.00002       1.12421
	             B(2,1,3)     140.04057        0.00000        0.01420     140.05477
	             B(2,1,4)      96.70433       -0.00000       -0.00457      96.69976
	             B(2,1,5)      96.70456       -0.00000       -0.00459      96.69997
	             B(3,1,4)      96.70462       -0.00000       -0.00459      96.70003
	             B(3,1,5)      96.70485       -0.00000       -0.00461      96.70024
	             B(4,1,5)     140.04057        0.00000        0.01420     140.05477
	           O(2,1,3,4)      37.42687       -0.00000       -0.01141      37.41545
	           O(2,1,3,5)     -37.42669        0.00000        0.01140     -37.41529
	           O(2,1,4,5)      70.02074        0.00000        0.00707      70.02780
	           O(3,1,2,4)     -37.42684        0.00000        0.01141     -37.41543
	           O(3,1,2,5)      37.42666       -0.00000       -0.01140      37.41526
	           O(3,1,4,5)     -70.01983       -0.00000       -0.00713     -70.02696
	           O(4,1,2,3)      70.02064        0.00000        0.00707      70.02772
	           O(4,1,2,5)     -37.42688        0.00000        0.01141     -37.41547
	           O(4,1,3,5)      37.42666       -0.00000       -0.01140      37.41526
	           O(5,1,2,3)     -70.01993       -0.00000       -0.00712     -70.02705
	           O(5,1,2,4)      37.42686       -0.00000       -0.01141      37.41545
	           O(5,1,3,4)     -37.42664        0.00000        0.01140     -37.41524
	-------------------------------------------------------------------------------

	                                 ==> Convergence Check <==                                  
    
	Measures of convergence in internal coordinates in au.
    
	Criteria marked as inactive (o), active & met (*), and active & unmet ( ).

	----------------------------------------------------------------------------------------------
	   Step    Total Energy     Delta E     Max Force     RMS Force      Max Disp      RMS Disp   
	----------------------------------------------------------------------------------------------
	  Convergence Criteria     1.00e-06 *    3.00e-04 *             o    1.20e-03 *             o
	----------------------------------------------------------------------------------------------
	     8     -40.05200162   -2.61e-07 *    1.58e-05 *    8.97e-06 o    2.48e-04 *    1.56e-04 o  ~
	----------------------------------------------------------------------------------------------

Next Geometry in Ang 
	Fragment 1 (Ang)

	    C   0.0000007080  -0.0000008787  -0.0000011481
	    H  -0.4919879696   1.0067419828   0.0908690222
	    H  -0.1597714116  -0.9956698380  -0.4969478385
	    H  -0.2077517983  -0.3427921880   1.0503198881
	    H   0.8595104715   0.3317209219  -0.6442399237


    Final optimized geometry and variables:
    Molecular point group: c1
    Full point group: C1

    Geometry (in Angstrom), charge = 1, multiplicity = 2:

    C            0.000000761219    -0.000000941829    -0.000001229844
    H           -0.492082991129     1.006681176815     0.090785067690
    H           -0.159887269893    -0.995605596599    -0.496995144568
    H           -0.207613096452    -0.342772811743     1.050332627475
    H            0.859582596255     0.331698173355    -0.644121320753


==================================================
=== PSI4计算完成 ===
绝热电离能: 12.692 eV
中性分子能量: -40.51840708 Hartree
阳离子能量: -40.05200162 Hartree
完成时间: 2025-06-29 21:43:36
