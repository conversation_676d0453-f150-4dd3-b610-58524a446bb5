
  Memory set to   7.451 GiB by Python driver.
  Threads set to 10 by Python driver.

Scratch directory: D:\BaiduSyncdisk\py\MS\psi4_scratch/
gradient() will perform analytic gradient computation.
   => Libint2 <=

    Primary   basis highest AM E, G, H:  6, 6, -
    Auxiliary basis highest AM E, G, H:  7, 7, -
    Onebody   basis highest AM E, G, H:  -, -, -
    Solid Harmonics ordering:            Gaussian

*** tstart() called on iCarus
*** at Mon Jul  7 22:34:23 2025

   => Loading Basis Set <=

    Name: 6-31G(D)
    Role: ORBITAL
    Keyword: BASIS
    atoms 1   entry C          line   111 file D:\anaconda3\envs\nistpy\Library\share\psi4\basis\6-31g_d_.gbs 
    atoms 2-5 entry H          line    44 file D:\anaconda3\envs\nistpy\Library\share\psi4\basis\6-31g_d_.gbs 


         ---------------------------------------------------------
                                   SCF
               by <PERSON>, <PERSON>, <PERSON>
                          and <PERSON>
                              RKS Reference
                       10 Threads,   -562 MiB Core
         ---------------------------------------------------------

  ==> Geometry <==

    Molecular point group: c1
    Full point group: C1

    Geometry (in Angstrom), charge = 0, multiplicity = 1:

       Center              X                  Y                   Z               Mass       
    ------------   -----------------  -----------------  -----------------  -----------------
         C            0.000000000000     0.000000000000     0.000000000000    12.000000000000
         H           -0.675343000000     0.854123000000    -0.085361000000     1.007825032230
         H           -0.394946000000    -0.835938000000    -0.581485000000     1.007825032230
         H            0.084754000000    -0.293741000000     1.048538000000     1.007825032230
         H            0.985535000000     0.275556000000    -0.381692000000     1.007825032230

  Running in c1 symmetry.

  Rotational constants: A =      5.25822  B =      5.25822  C =      5.25822 [cm^-1]
  Rotational constants: A = 157637.54748  B = 157637.44145  C = 157637.40228 [MHz]
  Nuclear repulsion =   13.408333629033770

  Charge       = 0
  Multiplicity = 1
  Electrons    = 10
  Nalpha       = 5
  Nbeta        = 5

  ==> Algorithm <==

  SCF Algorithm Type is DF.
  DIIS enabled.
  MOM disabled.
  Fractional occupation disabled.
  Guess Type is SAD.
  Energy threshold   = 1.00e-08
  Density threshold  = 1.00e-08
  Integral threshold = 1.00e-12

  ==> Primary Basis <==

  Basis Set: 6-31G(D)
    Blend: 6-31G(D)
    Number of shells: 14
    Number of basis functions: 23
    Number of Cartesian functions: 23
    Spherical Harmonics?: false
    Max angular momentum: 2

  ==> DFT Potential <==

   => LibXC <=

    Version 7.0.0
    S. Lehtola, C. Steigemann, M. J.T. Oliveira, and M. A.L. Marques.,  SoftwareX 7, 1–5 (2018) (10.1016/j.softx.2017.11.002)

   => Composite Functional: B3LYP <= 

    B3LYP Hyb-GGA Exchange-Correlation Functional

    P. J. Stephens, F. J. Devlin, C. F. Chabalowski, and M. J. Frisch.,  J. Phys. Chem. 98, 11623 (1994) (10.1021/j100096a001)

    Deriv               =              1
    GGA                 =           TRUE
    Meta                =          FALSE

    Exchange Hybrid     =           TRUE
    MP2 Hybrid          =          FALSE

   => Exchange Functionals <=

    0.0800   Slater exchange
    0.7200         Becke 88

   => Exact (HF) Exchange <=

    0.2000               HF 

   => Correlation Functionals <=

    0.1900   Vosko, Wilk & Nusair (VWN5_RPA)
    0.8100   Lee, Yang & Parr

   => LibXC Density Thresholds  <==

    XC_HYB_GGA_XC_B3LYP:  1.00E-15 

   => Molecular Quadrature <=

    Radial Scheme          =       TREUTLER
    Pruning Scheme         =           NONE
    Nuclear Scheme         =       TREUTLER

    Blocking Scheme        =         OCTREE
    BS radius alpha        =              1
    Pruning alpha          =              1
    Radial Points          =             75
    Spherical Points       =            302
    Total Points           =         109203
    Total Blocks           =            878
    Max Points             =            255
    Max Functions          =             23
    Weights Tolerance      =       1.00E-15

   => Loading Basis Set <=

    Name: (6-31G(D) AUX)
    Role: JKFIT
    Keyword: DF_BASIS_SCF
    atoms 1   entry C          line   121 file D:\anaconda3\envs\nistpy\Library\share\psi4\basis\cc-pvdz-jkfit.gbs 
    atoms 2-5 entry H          line    51 file D:\anaconda3\envs\nistpy\Library\share\psi4\basis\cc-pvdz-jkfit.gbs 

  ==> Integral Setup <==

  DFHelper Memory: AOs need 0.001 GiB; user supplied 5.515 GiB. 
  Using in-core AOs.

  ==> MemDFJK: Density-Fitted J/K Matrices <==

    J tasked:                   Yes
    K tasked:                   Yes
    wK tasked:                   No
    OpenMP threads:              10
    Memory [MiB]:              5647
    Algorithm:                 Core
    Schwarz Cutoff:           1E-12
    Mask sparsity (%):       0.0000
    Fitting Condition:        1E-10

   => Auxiliary Basis Set <=

  Basis Set: (6-31G(D) AUX)
    Blend: CC-PVDZ-JKFIT
    Number of shells: 60
    Number of basis functions: 181
    Number of Cartesian functions: 181
    Spherical Harmonics?: false
    Max angular momentum: 3

  Cached 100.0% of DFT collocation blocks in 0.073 [GiB].

  Minimum eigenvalue in the overlap matrix is 1.7897030891E-02.
  Reciprocal condition number of the overlap matrix is 3.1910812166E-03.
    Using symmetric orthogonalization.

  ==> Pre-Iterations <==

  SCF Guess: Superposition of Atomic Densities via on-the-fly atomic UHF (no occupation information).

   -------------------------
    Irrep   Nso     Nmo    
   -------------------------
     A         23      23 
   -------------------------
    Total      23      23
   -------------------------

  ==> Iterations <==

                           Total Energy        Delta E     RMS |[F,P]|

   @DF-RKS iter SAD:   -40.09362093565437   -4.00936e+01   0.00000e+00 
   @DF-RKS iter   1:   -40.35147126380735   -2.57850e-01   2.49153e-02 DIIS/ADIIS
   @DF-RKS iter   2:   -40.31077246903421    4.06988e-02   2.63750e-02 DIIS/ADIIS
   @DF-RKS iter   3:   -40.51838948507617   -2.07617e-01   2.47275e-04 DIIS/ADIIS
   @DF-RKS iter   4:   -40.51840391582196   -1.44307e-05   6.00555e-05 DIIS
   @DF-RKS iter   5:   -40.51840487628672   -9.60465e-07   3.41993e-06 DIIS
   @DF-RKS iter   6:   -40.51840487925729   -2.97057e-09   9.03350e-08 DIIS
   @DF-RKS iter   7:   -40.51840487925971   -2.42295e-12   2.63329e-09 DIIS
  Energy and wave function converged.


  ==> Post-Iterations <==

   Electrons on quadrature grid:
      Ntotal   =   10.0000048809 ; deviation = 4.881e-06 

    Orbital Energies [Eh]
    ---------------------

    Doubly Occupied:                                                      

       1A    -10.166480     2A     -0.690613     3A     -0.389244  
       4A     -0.389243     5A     -0.389243  

    Virtual:                                                              

       6A      0.118182     7A      0.176898     8A      0.176898  
       9A      0.176899    10A      0.532897    11A      0.532898  
      12A      0.532899    13A      0.898636    14A      0.898636  
      15A      0.898636    16A      0.949080    17A      1.100191  
      18A      1.656826    19A      1.656827    20A      2.244882  
      21A      2.244883    22A      2.244884    23A      4.179964  

    Final Occupation by Irrep:
              A 
    DOCC [     5 ]
    NA   [     5 ]
    NB   [     5 ]

  @DF-RKS Final Energy:   -40.51840487925971

   => Energetics <=

    Nuclear Repulsion Energy =             13.4083336290337698
    One-Electron Energy =                 -79.7835434868685240
    Two-Electron Energy =                  31.4448883221927815
    DFT Exchange-Correlation Energy =      -5.5880833436177344
    Empirical Dispersion Energy =           0.0000000000000000
    VV10 Nonlocal Energy =                  0.0000000000000000
    Total Energy =                        -40.5184048792597125

Computation Completed


Properties will be evaluated at   0.000000,   0.000000,   0.000000 [a0]

Properties computed using the SCF density matrix


 Multipole Moments:

 ------------------------------------------------------------------------------------
     Multipole            Electronic (a.u.)      Nuclear  (a.u.)        Total (a.u.)
 ------------------------------------------------------------------------------------

 L = 1.  Multiply by 2.5417464519 to convert [e a0] to [Debye]
 Dipole X            :         -0.0000011            0.0000000           -0.0000011
 Dipole Y            :          0.0000002            0.0000000            0.0000002
 Dipole Z            :          0.0000005            0.0000000            0.0000005
 Magnitude           :                                                    0.0000012

 ------------------------------------------------------------------------------------

*** tstop() called on iCarus at Mon Jul  7 22:34:24 2025
Module time:
	user time   =        nan seconds =        nan minutes
	system time =        nan seconds =        nan minutes
	total time  =          1 seconds =       0.02 minutes
Total time:
	user time   =        nan seconds =        nan minutes
	system time =        nan seconds =        nan minutes
	total time  =          1 seconds =       0.02 minutes

*** tstart() called on iCarus
*** at Mon Jul  7 22:34:24 2025


         ------------------------------------------------------------
                                   SCF GRAD                          
                          Rob Parrish, Justin Turney,                
                       Andy Simmonett, and Alex Sokolov              
         ------------------------------------------------------------

  ==> Geometry <==

    Molecular point group: c1
    Full point group: C1

    Geometry (in Angstrom), charge = 0, multiplicity = 1:

       Center              X                  Y                   Z               Mass       
    ------------   -----------------  -----------------  -----------------  -----------------
         C            0.000000000000     0.000000000000     0.000000000000    12.000000000000
         H           -0.675343000000     0.854123000000    -0.085361000000     1.007825032230
         H           -0.394946000000    -0.835938000000    -0.581485000000     1.007825032230
         H            0.084754000000    -0.293741000000     1.048538000000     1.007825032230
         H            0.985535000000     0.275556000000    -0.381692000000     1.007825032230

  Nuclear repulsion =   13.408333629033770

  ==> Basis Set <==

  Basis Set: 6-31G(D)
    Blend: 6-31G(D)
    Number of shells: 14
    Number of basis functions: 23
    Number of Cartesian functions: 23
    Spherical Harmonics?: false
    Max angular momentum: 2

  ==> DFJKGrad: Density-Fitted SCF Gradients <==

    Gradient:                    1
    J tasked:                  Yes
    K tasked:                  Yes
    wK tasked:                  No
    OpenMP threads:             10
    Integrals threads:          10
    Memory [MiB]:             -422
    Schwarz Cutoff:          1E-12
    Fitting Condition:       1E-10

   => Auxiliary Basis Set <=

  Basis Set: (6-31G(D) AUX)
    Blend: CC-PVDZ-JKFIT
    Number of shells: 60
    Number of basis functions: 181
    Number of Cartesian functions: 181
    Spherical Harmonics?: false
    Max angular momentum: 3

  ==> DFT Potential <==

   => LibXC <=

    Version 7.0.0
    S. Lehtola, C. Steigemann, M. J.T. Oliveira, and M. A.L. Marques.,  SoftwareX 7, 1–5 (2018) (10.1016/j.softx.2017.11.002)

   => Composite Functional: B3LYP <= 

    B3LYP Hyb-GGA Exchange-Correlation Functional

    P. J. Stephens, F. J. Devlin, C. F. Chabalowski, and M. J. Frisch.,  J. Phys. Chem. 98, 11623 (1994) (10.1021/j100096a001)

    Deriv               =              1
    GGA                 =           TRUE
    Meta                =          FALSE

    Exchange Hybrid     =           TRUE
    MP2 Hybrid          =          FALSE

   => Exchange Functionals <=

    0.0800   Slater exchange
    0.7200         Becke 88

   => Exact (HF) Exchange <=

    0.2000               HF 

   => Correlation Functionals <=

    0.1900   Vosko, Wilk & Nusair (VWN5_RPA)
    0.8100   Lee, Yang & Parr

   => LibXC Density Thresholds  <==

    XC_HYB_GGA_XC_B3LYP:  1.00E-15 

   => Molecular Quadrature <=

    Radial Scheme          =       TREUTLER
    Pruning Scheme         =           NONE
    Nuclear Scheme         =       TREUTLER

    Blocking Scheme        =         OCTREE
    BS radius alpha        =              1
    Pruning alpha          =              1
    Radial Points          =             75
    Spherical Points       =            302
    Total Points           =         109203
    Total Blocks           =            878
    Max Points             =            255
    Max Functions          =             23
    Weights Tolerance      =       1.00E-15


  -Total Gradient:
     Atom            X                  Y                   Z
    ------   -----------------  -----------------  -----------------
       1       -0.000000954966     0.000000204677     0.000000630358
       2        0.000441385452    -0.000558500112     0.000055599769
       3        0.000257760238     0.000546140235     0.000380391500
       4       -0.000054434493     0.000193299594    -0.000686471107
       5       -0.000644793894    -0.000179996112     0.000249137331


*** tstop() called on iCarus at Mon Jul  7 22:34:24 2025
Module time:
	user time   =        nan seconds =        nan minutes
	system time =        nan seconds =        nan minutes
	total time  =          0 seconds =       0.00 minutes
Total time:
	user time   =        nan seconds =        nan minutes
	system time =        nan seconds =        nan minutes
	total time  =          1 seconds =       0.02 minutes

    			-----------------------------------------

    			 OPTKING 3.0: for geometry optimizations 

    			     By R.A. King, Bethel University     

    			        with contributions from          

    			    A.V. Copan, J. Cayton, A. Heide      

    			-----------------------------------------

    
	                            ===> Fragment 1 <== 

	 Z (Atomic Numbers)          Masses                          Geom                  
	       6.000000            12.000000          0.00000000  0.00000000  0.00000000   
	       1.000000             1.007825         -1.27621331  1.61405855 -0.16130891   
	       1.000000             1.007825         -0.74633977 -1.57969388 -1.09884740   
	       1.000000             1.007825          0.16016185 -0.55509004  1.98144965   
	       1.000000             1.007825          1.86239124  0.52072537 -0.72129334   

	 - Coordinate -           - BOHR/RAD -       - ANG/DEG -
	 R(1,2)           =         2.063959           1.092200
	 R(1,3)           =         2.063958           1.092199
	 R(1,4)           =         2.063957           1.092199
	 R(1,5)           =         2.063957           1.092199
	 B(2,1,3)         =         1.910634         109.471238
	 B(2,1,4)         =         1.910633         109.471217
	 B(2,1,5)         =         1.910634         109.471247
	 B(3,1,4)         =         1.910633         109.471222
	 B(3,1,5)         =         1.910633         109.471198
	 B(4,1,5)         =         1.910633         109.471201
	 O(2,1,3,4)       =         0.955316          54.735596
	 O(2,1,3,5)       =        -0.955316         -54.735578
	 O(2,1,4,5)       =         0.955316          54.735597
	 O(3,1,2,4)       =        -0.955316         -54.735593
	 O(3,1,2,5)       =         0.955316          54.735602
	 O(3,1,4,5)       =        -0.955317         -54.735641
	 O(4,1,2,3)       =         0.955316          54.735603
	 O(4,1,2,5)       =        -0.955317         -54.735620
	 O(4,1,3,5)       =         0.955317          54.735639
	 O(5,1,2,3)       =        -0.955316         -54.735598
	 O(5,1,2,4)       =         0.955317          54.735605
	 O(5,1,3,4)       =        -0.955317         -54.735651


	        --- Internal Coordinate Step in ANG or DEG, aJ/ANG or AJ/DEG ---
	-------------------------------------------------------------------------------
	           Coordinate      Previous         Force          Change          New 
	           ----------      --------        ------          ------        ------
	               R(1,2)       1.09220        0.00589        0.00109       1.09329
	               R(1,3)       1.09220        0.00588        0.00109       1.09329
	               R(1,4)       1.09220        0.00589        0.00109       1.09329
	               R(1,5)       1.09220        0.00588        0.00109       1.09329
	             B(2,1,3)     109.47124        0.00000        0.00000     109.47124
	             B(2,1,4)     109.47122       -0.00000       -0.00006     109.47116
	             B(2,1,5)     109.47125       -0.00000       -0.00002     109.47123
	             B(3,1,4)     109.47122       -0.00000       -0.00010     109.47112
	             B(3,1,5)     109.47120       -0.00000       -0.00004     109.47116
	             B(4,1,5)     109.47120        0.00000        0.00021     109.47141
	           O(2,1,3,4)      54.73560        0.00000        0.00010      54.73570
	           O(2,1,3,5)     -54.73558       -0.00000       -0.00003     -54.73561
	           O(2,1,4,5)      54.73560        0.00000       -0.00003      54.73557
	           O(3,1,2,4)     -54.73559       -0.00000       -0.00013     -54.73572
	           O(3,1,2,5)      54.73560        0.00000        0.00004      54.73564
	           O(3,1,4,5)     -54.73564       -0.00000       -0.00004     -54.73568
	           O(4,1,2,3)      54.73560        0.00000        0.00016      54.73576
	           O(4,1,2,5)     -54.73562        0.00000        0.00014     -54.73548
	           O(4,1,3,5)      54.73564       -0.00000       -0.00009      54.73555
	           O(5,1,2,3)     -54.73560       -0.00000       -0.00005     -54.73565
	           O(5,1,2,4)      54.73561       -0.00000       -0.00016      54.73544
	           O(5,1,3,4)     -54.73565        0.00000        0.00012     -54.73553
	-------------------------------------------------------------------------------

	                                 ==> Convergence Check <==                                  
    
	Measures of convergence in internal coordinates in au.
    
	Criteria marked as inactive (o), active & met (*), and active & unmet ( ).

	----------------------------------------------------------------------------------------------~
	   Step    Total Energy     Delta E     Max Force     RMS Force      Max Disp      RMS Disp   ~
	----------------------------------------------------------------------------------------------~
	  Convergence Criteria     1.00e-06 *    3.00e-04 *             o    1.20e-03 *             o~
	----------------------------------------------------------------------------------------------~
	     1     -40.51840488   -4.05e+01      7.15e-04      3.05e-04 o    2.07e-03      8.81e-04 o  ~
	----------------------------------------------------------------------------------------------

Next Geometry in Ang 
	Fragment 1 (Ang)

	    C   0.0000005796   0.0000001226  -0.0000001355
	    H  -0.6760190082   0.8549783037  -0.0854463846
	    H  -0.3953406851  -0.8367745555  -0.5820664696
	    H   0.0848373938  -0.2940360390   1.0495888969
	    H   0.9865217198   0.2758321681  -0.3820759072


    Structure for next step:
    Molecular point group: c1
    Full point group: C1

    Geometry (in Angstrom), charge = 0, multiplicity = 1:

    C            0.000000579599     0.000000122627    -0.000000135522
    H           -0.676019008173     0.854978303694    -0.085446384605
    H           -0.395340685064    -0.836774555468    -0.582066469625
    H            0.084837393845    -0.294036038966     1.049588896945
    H            0.986521719793     0.275832168114    -0.382075907193


Scratch directory: D:\BaiduSyncdisk\py\MS\psi4_scratch/
gradient() will perform analytic gradient computation.
   => Libint2 <=

    Primary   basis highest AM E, G, H:  6, 6, -
    Auxiliary basis highest AM E, G, H:  7, 7, -
    Onebody   basis highest AM E, G, H:  -, -, -
    Solid Harmonics ordering:            Gaussian

*** tstart() called on iCarus
*** at Mon Jul  7 22:34:24 2025

   => Loading Basis Set <=

    Name: 6-31G(D)
    Role: ORBITAL
    Keyword: BASIS
    atoms 1   entry C          line   111 file D:\anaconda3\envs\nistpy\Library\share\psi4\basis\6-31g_d_.gbs 
    atoms 2-5 entry H          line    44 file D:\anaconda3\envs\nistpy\Library\share\psi4\basis\6-31g_d_.gbs 


         ---------------------------------------------------------
                                   SCF
               by Justin Turney, Rob Parrish, Andy Simmonett
                          and Daniel G. A. Smith
                              RKS Reference
                       10 Threads,   -562 MiB Core
         ---------------------------------------------------------

  ==> Geometry <==

    Molecular point group: c1
    Full point group: C1

    Geometry (in Angstrom), charge = 0, multiplicity = 1:

       Center              X                  Y                   Z               Mass       
    ------------   -----------------  -----------------  -----------------  -----------------
         C            0.000000579599     0.000000122627    -0.000000135522    12.000000000000
         H           -0.676019008173     0.854978303694    -0.085446384605     1.007825032230
         H           -0.395340685064    -0.836774555468    -0.582066469625     1.007825032230
         H            0.084837393845    -0.294036038966     1.049588896945     1.007825032230
         H            0.986521719793     0.275832168114    -0.382075907193     1.007825032230

  Running in c1 symmetry.

  Rotational constants: A =      5.24771  B =      5.24771  C =      5.24770 [cm^-1]
  Rotational constants: A = 157322.42721  B = 157322.26093  C = 157321.95422 [MHz]
  Nuclear repulsion =   13.394919661992720

  Charge       = 0
  Multiplicity = 1
  Electrons    = 10
  Nalpha       = 5
  Nbeta        = 5

  ==> Algorithm <==

  SCF Algorithm Type is DF.
  DIIS enabled.
  MOM disabled.
  Fractional occupation disabled.
  Guess Type is READ.
  Energy threshold   = 1.00e-08
  Density threshold  = 1.00e-08
  Integral threshold = 1.00e-12

  ==> Primary Basis <==

  Basis Set: 6-31G(D)
    Blend: 6-31G(D)
    Number of shells: 14
    Number of basis functions: 23
    Number of Cartesian functions: 23
    Spherical Harmonics?: false
    Max angular momentum: 2

  ==> DFT Potential <==

   => LibXC <=

    Version 7.0.0
    S. Lehtola, C. Steigemann, M. J.T. Oliveira, and M. A.L. Marques.,  SoftwareX 7, 1–5 (2018) (10.1016/j.softx.2017.11.002)

   => Composite Functional: B3LYP <= 

    B3LYP Hyb-GGA Exchange-Correlation Functional

    P. J. Stephens, F. J. Devlin, C. F. Chabalowski, and M. J. Frisch.,  J. Phys. Chem. 98, 11623 (1994) (10.1021/j100096a001)

    Deriv               =              1
    GGA                 =           TRUE
    Meta                =          FALSE

    Exchange Hybrid     =           TRUE
    MP2 Hybrid          =          FALSE

   => Exchange Functionals <=

    0.0800   Slater exchange
    0.7200         Becke 88

   => Exact (HF) Exchange <=

    0.2000               HF 

   => Correlation Functionals <=

    0.1900   Vosko, Wilk & Nusair (VWN5_RPA)
    0.8100   Lee, Yang & Parr

   => LibXC Density Thresholds  <==

    XC_HYB_GGA_XC_B3LYP:  1.00E-15 

   => Molecular Quadrature <=

    Radial Scheme          =       TREUTLER
    Pruning Scheme         =           NONE
    Nuclear Scheme         =       TREUTLER

    Blocking Scheme        =         OCTREE
    BS radius alpha        =              1
    Pruning alpha          =              1
    Radial Points          =             75
    Spherical Points       =            302
    Total Points           =         109196
    Total Blocks           =            883
    Max Points             =            256
    Max Functions          =             23
    Weights Tolerance      =       1.00E-15

   => Loading Basis Set <=

    Name: (6-31G(D) AUX)
    Role: JKFIT
    Keyword: DF_BASIS_SCF
    atoms 1   entry C          line   121 file D:\anaconda3\envs\nistpy\Library\share\psi4\basis\cc-pvdz-jkfit.gbs 
    atoms 2-5 entry H          line    51 file D:\anaconda3\envs\nistpy\Library\share\psi4\basis\cc-pvdz-jkfit.gbs 

   => Loading Basis Set <=

    Name: 6-31G(D)
    Role: ORBITAL
    Keyword: BASIS
    atoms 1   entry C          line   111 file D:\anaconda3\envs\nistpy\Library\share\psi4\basis\6-31g_d_.gbs 
    atoms 2-5 entry H          line    44 file D:\anaconda3\envs\nistpy\Library\share\psi4\basis\6-31g_d_.gbs 

  Reading orbitals from file D:\BaiduSyncdisk\py\MS\psi4_scratch/psi4_calculation.default.33932.180.npy, no projection.

  ==> Integral Setup <==

  DFHelper Memory: AOs need 0.001 GiB; user supplied 5.515 GiB. 
  Using in-core AOs.

  ==> MemDFJK: Density-Fitted J/K Matrices <==

    J tasked:                   Yes
    K tasked:                   Yes
    wK tasked:                   No
    OpenMP threads:              10
    Memory [MiB]:              5647
    Algorithm:                 Core
    Schwarz Cutoff:           1E-12
    Mask sparsity (%):       0.0000
    Fitting Condition:        1E-10

   => Auxiliary Basis Set <=

  Basis Set: (6-31G(D) AUX)
    Blend: CC-PVDZ-JKFIT
    Number of shells: 60
    Number of basis functions: 181
    Number of Cartesian functions: 181
    Spherical Harmonics?: false
    Max angular momentum: 3

  Cached 100.0% of DFT collocation blocks in 0.073 [GiB].

  Minimum eigenvalue in the overlap matrix is 1.7960254003E-02.
  Reciprocal condition number of the overlap matrix is 3.2047277007E-03.
    Using symmetric orthogonalization.

  ==> Pre-Iterations <==

  SCF Guess: Orbitals guess was supplied from a previous computation.

   -------------------------------------------------------
    Irrep   Nso     Nmo     Nalpha   Nbeta   Ndocc  Nsocc
   -------------------------------------------------------
     A         23      23       5       5       5       0
   -------------------------------------------------------
    Total      23      23       5       5       5       0
   -------------------------------------------------------

  ==> Iterations <==

                           Total Energy        Delta E     RMS |[F,P]|

   @DF-RKS iter   0:   -40.51729045140609   -4.05173e+01   7.30947e-05 
   @DF-RKS iter   1:   -40.51840706152541   -1.11661e-03   7.36676e-06 DIIS
   @DF-RKS iter   2:   -40.51840707630540   -1.47800e-08   4.76415e-06 DIIS
   @DF-RKS iter   3:   -40.51840708150530   -5.19990e-09   2.04586e-06 DIIS
   @DF-RKS iter   4:   -40.51840708259368   -1.08838e-09   5.45552e-08 DIIS
   @DF-RKS iter   5:   -40.51840708259446   -7.81597e-13   5.54154e-10 DIIS
  Energy and wave function converged.


  ==> Post-Iterations <==

   Electrons on quadrature grid:
      Ntotal   =   10.0000006238 ; deviation = 6.238e-07 

    Orbital Energies [Eh]
    ---------------------

    Doubly Occupied:                                                      

       1A    -10.166938     2A     -0.690279     3A     -0.389061  
       4A     -0.389061     5A     -0.389060  

    Virtual:                                                              

       6A      0.117914     7A      0.176556     8A      0.176557  
       9A      0.176557    10A      0.533224    11A      0.533225  
      12A      0.533227    13A      0.897799    14A      0.897800  
      15A      0.897801    16A      0.947455    17A      1.099862  
      18A      1.657043    19A      1.657044    20A      2.243407  
      21A      2.243408    22A      2.243409    23A      4.179167  

    Final Occupation by Irrep:
              A 
    DOCC [     5 ]
    NA   [     5 ]
    NB   [     5 ]

  @DF-RKS Final Energy:   -40.51840708259446

   => Energetics <=

    Nuclear Repulsion Energy =             13.3949196619927200
    One-Electron Energy =                 -79.7584354296752025
    Two-Electron Energy =                  31.4319451698080208
    DFT Exchange-Correlation Energy =      -5.5868364847199992
    Empirical Dispersion Energy =           0.0000000000000000
    VV10 Nonlocal Energy =                  0.0000000000000000
    Total Energy =                        -40.5184070825944644

Computation Completed


Properties will be evaluated at   0.000000,   0.000000,   0.000000 [a0]

Properties computed using the SCF density matrix


 Multipole Moments:

 ------------------------------------------------------------------------------------
     Multipole            Electronic (a.u.)      Nuclear  (a.u.)        Total (a.u.)
 ------------------------------------------------------------------------------------

 L = 1.  Multiply by 2.5417464519 to convert [e a0] to [Debye]
 Dipole X            :         -0.0000070            0.0000055           -0.0000015
 Dipole Y            :         -0.0000020            0.0000012           -0.0000008
 Dipole Z            :          0.0000022           -0.0000013            0.0000009
 Magnitude           :                                                    0.0000019

 ------------------------------------------------------------------------------------

*** tstop() called on iCarus at Mon Jul  7 22:34:24 2025
Module time:
	user time   =        nan seconds =        nan minutes
	system time =        nan seconds =        nan minutes
	total time  =          0 seconds =       0.00 minutes
Total time:
	user time   =        nan seconds =        nan minutes
	system time =        nan seconds =        nan minutes
	total time  =          1 seconds =       0.02 minutes

*** tstart() called on iCarus
*** at Mon Jul  7 22:34:24 2025


         ------------------------------------------------------------
                                   SCF GRAD                          
                          Rob Parrish, Justin Turney,                
                       Andy Simmonett, and Alex Sokolov              
         ------------------------------------------------------------

  ==> Geometry <==

    Molecular point group: c1
    Full point group: C1

    Geometry (in Angstrom), charge = 0, multiplicity = 1:

       Center              X                  Y                   Z               Mass       
    ------------   -----------------  -----------------  -----------------  -----------------
         C            0.000000579599     0.000000122627    -0.000000135522    12.000000000000
         H           -0.676019008173     0.854978303694    -0.085446384605     1.007825032230
         H           -0.395340685064    -0.836774555468    -0.582066469625     1.007825032230
         H            0.084837393845    -0.294036038966     1.049588896945     1.007825032230
         H            0.986521719793     0.275832168114    -0.382075907193     1.007825032230

  Nuclear repulsion =   13.394919661992720

  ==> Basis Set <==

  Basis Set: 6-31G(D)
    Blend: 6-31G(D)
    Number of shells: 14
    Number of basis functions: 23
    Number of Cartesian functions: 23
    Spherical Harmonics?: false
    Max angular momentum: 2

  ==> DFJKGrad: Density-Fitted SCF Gradients <==

    Gradient:                    1
    J tasked:                  Yes
    K tasked:                  Yes
    wK tasked:                  No
    OpenMP threads:             10
    Integrals threads:          10
    Memory [MiB]:             -422
    Schwarz Cutoff:          1E-12
    Fitting Condition:       1E-10

   => Auxiliary Basis Set <=

  Basis Set: (6-31G(D) AUX)
    Blend: CC-PVDZ-JKFIT
    Number of shells: 60
    Number of basis functions: 181
    Number of Cartesian functions: 181
    Spherical Harmonics?: false
    Max angular momentum: 3

  ==> DFT Potential <==

   => LibXC <=

    Version 7.0.0
    S. Lehtola, C. Steigemann, M. J.T. Oliveira, and M. A.L. Marques.,  SoftwareX 7, 1–5 (2018) (10.1016/j.softx.2017.11.002)

   => Composite Functional: B3LYP <= 

    B3LYP Hyb-GGA Exchange-Correlation Functional

    P. J. Stephens, F. J. Devlin, C. F. Chabalowski, and M. J. Frisch.,  J. Phys. Chem. 98, 11623 (1994) (10.1021/j100096a001)

    Deriv               =              1
    GGA                 =           TRUE
    Meta                =          FALSE

    Exchange Hybrid     =           TRUE
    MP2 Hybrid          =          FALSE

   => Exchange Functionals <=

    0.0800   Slater exchange
    0.7200         Becke 88

   => Exact (HF) Exchange <=

    0.2000               HF 

   => Correlation Functionals <=

    0.1900   Vosko, Wilk & Nusair (VWN5_RPA)
    0.8100   Lee, Yang & Parr

   => LibXC Density Thresholds  <==

    XC_HYB_GGA_XC_B3LYP:  1.00E-15 

   => Molecular Quadrature <=

    Radial Scheme          =       TREUTLER
    Pruning Scheme         =           NONE
    Nuclear Scheme         =       TREUTLER

    Blocking Scheme        =         OCTREE
    BS radius alpha        =              1
    Pruning alpha          =              1
    Radial Points          =             75
    Spherical Points       =            302
    Total Points           =         109196
    Total Blocks           =            883
    Max Points             =            256
    Max Functions          =             23
    Weights Tolerance      =       1.00E-15


  -Total Gradient:
     Atom            X                  Y                   Z
    ------   -----------------  -----------------  -----------------
       1        0.000000424207    -0.000000330029    -0.000001842695
       2       -0.000014431264     0.000018010103    -0.000002048586
       3       -0.000007486516    -0.000016959892    -0.000012306769
       4        0.000001972344    -0.000006155936     0.000022244540
       5        0.000019714410     0.000004886094    -0.000007786512


*** tstop() called on iCarus at Mon Jul  7 22:34:24 2025
Module time:
	user time   =        nan seconds =        nan minutes
	system time =        nan seconds =        nan minutes
	total time  =          0 seconds =       0.00 minutes
Total time:
	user time   =        nan seconds =        nan minutes
	system time =        nan seconds =        nan minutes
	total time  =          1 seconds =       0.02 minutes

    			-----------------------------------------

    			 OPTKING 3.0: for geometry optimizations 

    			     By R.A. King, Bethel University     

    			        with contributions from          

    			    A.V. Copan, J. Cayton, A. Heide      

    			-----------------------------------------

    
	                            ===> Fragment 1 <== 

	 Z (Atomic Numbers)          Masses                          Geom                  
	       6.000000            12.000000          0.00000110  0.00000023 -0.00000026   
	       1.000000             1.007825         -1.27749078  1.61567484 -0.16147027   
	       1.000000             1.007825         -0.74708562 -1.58127474 -1.09994621   
	       1.000000             1.007825          0.16031944 -0.55564758  1.98343556   
	       1.000000             1.007825          1.86425587  0.52124725 -0.72201882   

	 - Coordinate -           - BOHR/RAD -       - ANG/DEG -
	 R(1,2)           =         2.066026           1.093294
	 R(1,3)           =         2.066023           1.093292
	 R(1,4)           =         2.066026           1.093294
	 R(1,5)           =         2.066024           1.093293
	 B(2,1,3)         =         1.910634         109.471243
	 B(2,1,4)         =         1.910632         109.471160
	 B(2,1,5)         =         1.910633         109.471232
	 B(3,1,4)         =         1.910631         109.471119
	 B(3,1,5)         =         1.910632         109.471162
	 B(4,1,5)         =         1.910637         109.471408
	 O(2,1,3,4)       =         0.955318          54.735700
	 O(2,1,3,5)       =        -0.955317         -54.735606
	 O(2,1,4,5)       =         0.955316          54.735566
	 O(3,1,2,4)       =        -0.955319         -54.735720
	 O(3,1,2,5)       =         0.955317          54.735641
	 O(3,1,4,5)       =        -0.955318         -54.735677
	 O(4,1,2,3)       =         0.955319          54.735761
	 O(4,1,2,5)       =        -0.955314         -54.735478
	 O(4,1,3,5)       =         0.955316          54.735554
	 O(5,1,2,3)       =        -0.955317         -54.735647
	 O(5,1,2,4)       =         0.955314          54.735442
	 O(5,1,3,4)       =        -0.955315         -54.735532


	        --- Internal Coordinate Step in ANG or DEG, aJ/ANG or AJ/DEG ---
	-------------------------------------------------------------------------------
	           Coordinate      Previous         Force          Change          New 
	           ----------      --------        ------          ------        ------
	               R(1,2)       1.09329       -0.00019       -0.00003       1.09326
	               R(1,3)       1.09329       -0.00018       -0.00003       1.09326
	               R(1,4)       1.09329       -0.00019       -0.00003       1.09326
	               R(1,5)       1.09329       -0.00018       -0.00003       1.09326
	             B(2,1,3)     109.47124       -0.00000       -0.00004     109.47120
	             B(2,1,4)     109.47116        0.00000        0.00007     109.47123
	             B(2,1,5)     109.47123       -0.00000       -0.00014     109.47110
	             B(3,1,4)     109.47112       -0.00000       -0.00011     109.47101
	             B(3,1,5)     109.47116        0.00000        0.00015     109.47131
	             B(4,1,5)     109.47141        0.00000        0.00008     109.47148
	           O(2,1,3,4)      54.73570       -0.00000        0.00003      54.73573
	           O(2,1,3,5)     -54.73561       -0.00000       -0.00010     -54.73571
	           O(2,1,4,5)      54.73557        0.00000        0.00003      54.73560
	           O(3,1,2,4)     -54.73572       -0.00000       -0.00012     -54.73584
	           O(3,1,2,5)      54.73564       -0.00000       -0.00004      54.73560
	           O(3,1,4,5)     -54.73568        0.00000        0.00007     -54.73560
	           O(4,1,2,3)      54.73576        0.00000        0.00007      54.73583
	           O(4,1,2,5)     -54.73548        0.00000        0.00007     -54.73540
	           O(4,1,3,5)      54.73555        0.00000       -0.00004      54.73552
	           O(5,1,2,3)     -54.73565       -0.00000       -0.00001     -54.73566
	           O(5,1,2,4)      54.73544        0.00000        0.00003      54.73547
	           O(5,1,3,4)     -54.73553        0.00000        0.00017     -54.73536
	-------------------------------------------------------------------------------

	                                 ==> Convergence Check <==                                  
    
	Measures of convergence in internal coordinates in au.
    
	Criteria marked as inactive (o), active & met (*), and active & unmet ( ).

	----------------------------------------------------------------------------------------------
	   Step    Total Energy     Delta E     Max Force     RMS Force      Max Disp      RMS Disp   
	----------------------------------------------------------------------------------------------
	  Convergence Criteria     1.00e-06 *    3.00e-04 *             o    1.20e-03 *             o
	----------------------------------------------------------------------------------------------
	     2     -40.51840708   -2.20e-06      2.35e-05 *    9.63e-06 o    6.58e-05 *    2.71e-05 o  ~
	----------------------------------------------------------------------------------------------

Next Geometry in Ang 
	Fragment 1 (Ang)

	    C   0.0000006928   0.0000000853   0.0000003239
	    H  -0.6759971195   0.8549515352  -0.0854441498
	    H  -0.3953300453  -0.8367497402  -0.5820476019
	    H   0.0848340904  -0.2940276555   1.0495557145
	    H   0.9864923816   0.2758257753  -0.3820642867


    Final optimized geometry and variables:
    Molecular point group: c1
    Full point group: C1

    Geometry (in Angstrom), charge = 0, multiplicity = 1:

    C            0.000000579599     0.000000122627    -0.000000135522
    H           -0.676019008173     0.854978303694    -0.085446384605
    H           -0.395340685064    -0.836774555468    -0.582066469625
    H            0.084837393845    -0.294036038966     1.049588896945
    H            0.986521719793     0.275832168114    -0.382075907193


Scratch directory: D:\BaiduSyncdisk\py\MS\psi4_scratch/
gradient() will perform analytic gradient computation.
   => Libint2 <=

    Primary   basis highest AM E, G, H:  6, 6, -
    Auxiliary basis highest AM E, G, H:  7, 7, -
    Onebody   basis highest AM E, G, H:  -, -, -
    Solid Harmonics ordering:            Gaussian

*** tstart() called on iCarus
*** at Mon Jul  7 22:34:25 2025

   => Loading Basis Set <=

    Name: 6-31G(D)
    Role: ORBITAL
    Keyword: BASIS
    atoms 1   entry C          line   111 file D:\anaconda3\envs\nistpy\Library\share\psi4\basis\6-31g_d_.gbs 
    atoms 2-5 entry H          line    44 file D:\anaconda3\envs\nistpy\Library\share\psi4\basis\6-31g_d_.gbs 


         ---------------------------------------------------------
                                   SCF
               by Justin Turney, Rob Parrish, Andy Simmonett
                          and Daniel G. A. Smith
                              RKS Reference
                       10 Threads,   -562 MiB Core
         ---------------------------------------------------------

  ==> Geometry <==

    Molecular point group: c1
    Full point group: C1

    Geometry (in Angstrom), charge = 0, multiplicity = 1:

       Center              X                  Y                   Z               Mass       
    ------------   -----------------  -----------------  -----------------  -----------------
         C            0.000000579599     0.000000122627    -0.000000135522    12.000000000000
         H           -0.676019008173     0.854978303694    -0.085446384605     1.007825032230
         H           -0.395340685064    -0.836774555468    -0.582066469625     1.007825032230
         H            0.084837393845    -0.294036038966     1.049588896945     1.007825032230
         H            0.986521719793     0.275832168114    -0.382075907193     1.007825032230

  Running in c1 symmetry.

  Rotational constants: A =      5.24771  B =      5.24771  C =      5.24770 [cm^-1]
  Rotational constants: A = 157322.42721  B = 157322.26093  C = 157321.95422 [MHz]
  Nuclear repulsion =   13.394919661991628

  Charge       = 0
  Multiplicity = 1
  Electrons    = 10
  Nalpha       = 5
  Nbeta        = 5

  ==> Algorithm <==

  SCF Algorithm Type is DF.
  DIIS enabled.
  MOM disabled.
  Fractional occupation disabled.
  Guess Type is SAD.
  Energy threshold   = 1.00e-08
  Density threshold  = 1.00e-08
  Integral threshold = 1.00e-12

  ==> Primary Basis <==

  Basis Set: 6-31G(D)
    Blend: 6-31G(D)
    Number of shells: 14
    Number of basis functions: 23
    Number of Cartesian functions: 23
    Spherical Harmonics?: false
    Max angular momentum: 2

  ==> DFT Potential <==

   => LibXC <=

    Version 7.0.0
    S. Lehtola, C. Steigemann, M. J.T. Oliveira, and M. A.L. Marques.,  SoftwareX 7, 1–5 (2018) (10.1016/j.softx.2017.11.002)

   => Composite Functional: B3LYP <= 

    B3LYP Hyb-GGA Exchange-Correlation Functional

    P. J. Stephens, F. J. Devlin, C. F. Chabalowski, and M. J. Frisch.,  J. Phys. Chem. 98, 11623 (1994) (10.1021/j100096a001)

    Deriv               =              1
    GGA                 =           TRUE
    Meta                =          FALSE

    Exchange Hybrid     =           TRUE
    MP2 Hybrid          =          FALSE

   => Exchange Functionals <=

    0.0800   Slater exchange
    0.7200         Becke 88

   => Exact (HF) Exchange <=

    0.2000               HF 

   => Correlation Functionals <=

    0.1900   Vosko, Wilk & Nusair (VWN5_RPA)
    0.8100   Lee, Yang & Parr

   => LibXC Density Thresholds  <==

    XC_HYB_GGA_XC_B3LYP:  1.00E-15 

   => Molecular Quadrature <=

    Radial Scheme          =       TREUTLER
    Pruning Scheme         =           NONE
    Nuclear Scheme         =       TREUTLER

    Blocking Scheme        =         OCTREE
    BS radius alpha        =              1
    Pruning alpha          =              1
    Radial Points          =             75
    Spherical Points       =            302
    Total Points           =         109197
    Total Blocks           =            879
    Max Points             =            256
    Max Functions          =             23
    Weights Tolerance      =       1.00E-15

   => Loading Basis Set <=

    Name: (6-31G(D) AUX)
    Role: JKFIT
    Keyword: DF_BASIS_SCF
    atoms 1   entry C          line   121 file D:\anaconda3\envs\nistpy\Library\share\psi4\basis\cc-pvdz-jkfit.gbs 
    atoms 2-5 entry H          line    51 file D:\anaconda3\envs\nistpy\Library\share\psi4\basis\cc-pvdz-jkfit.gbs 

  ==> Integral Setup <==

  DFHelper Memory: AOs need 0.001 GiB; user supplied 5.515 GiB. 
  Using in-core AOs.

  ==> MemDFJK: Density-Fitted J/K Matrices <==

    J tasked:                   Yes
    K tasked:                   Yes
    wK tasked:                   No
    OpenMP threads:              10
    Memory [MiB]:              5647
    Algorithm:                 Core
    Schwarz Cutoff:           1E-12
    Mask sparsity (%):       0.0000
    Fitting Condition:        1E-10

   => Auxiliary Basis Set <=

  Basis Set: (6-31G(D) AUX)
    Blend: CC-PVDZ-JKFIT
    Number of shells: 60
    Number of basis functions: 181
    Number of Cartesian functions: 181
    Spherical Harmonics?: false
    Max angular momentum: 3

  Cached 100.0% of DFT collocation blocks in 0.073 [GiB].

  Minimum eigenvalue in the overlap matrix is 1.7960254003E-02.
  Reciprocal condition number of the overlap matrix is 3.2047277007E-03.
    Using symmetric orthogonalization.

  ==> Pre-Iterations <==

  SCF Guess: Superposition of Atomic Densities via on-the-fly atomic UHF (no occupation information).

   -------------------------
    Irrep   Nso     Nmo    
   -------------------------
     A         23      23 
   -------------------------
    Total      23      23
   -------------------------

  ==> Iterations <==

                           Total Energy        Delta E     RMS |[F,P]|

   @DF-RKS iter SAD:   -40.09237109601602   -4.00924e+01   0.00000e+00 
   @DF-RKS iter   1:   -40.35182816237164   -2.59457e-01   2.48833e-02 DIIS/ADIIS
   @DF-RKS iter   2:   -40.31075873376869    4.10694e-02   2.63668e-02 DIIS/ADIIS
   @DF-RKS iter   3:   -40.51839164388136   -2.07633e-01   2.48288e-04 DIIS/ADIIS
   @DF-RKS iter   4:   -40.51840611712218   -1.44732e-05   6.01082e-05 DIIS
   @DF-RKS iter   5:   -40.51840707961195   -9.62490e-07   3.42334e-06 DIIS
   @DF-RKS iter   6:   -40.51840708259070   -2.97875e-09   9.02578e-08 DIIS
   @DF-RKS iter   7:   -40.51840708259316   -2.45848e-12   2.68214e-09 DIIS
  Energy and wave function converged.


  ==> Post-Iterations <==

   Electrons on quadrature grid:
      Ntotal   =   10.0000006238 ; deviation = 6.238e-07 

    Orbital Energies [Eh]
    ---------------------

    Doubly Occupied:                                                      

       1A    -10.166938     2A     -0.690279     3A     -0.389061  
       4A     -0.389061     5A     -0.389060  

    Virtual:                                                              

       6A      0.117914     7A      0.176556     8A      0.176557  
       9A      0.176557    10A      0.533224    11A      0.533225  
      12A      0.533227    13A      0.897799    14A      0.897800  
      15A      0.897801    16A      0.947455    17A      1.099862  
      18A      1.657043    19A      1.657044    20A      2.243407  
      21A      2.243408    22A      2.243409    23A      4.179167  

    Final Occupation by Irrep:
              A 
    DOCC [     5 ]
    NA   [     5 ]
    NB   [     5 ]

  @DF-RKS Final Energy:   -40.51840708259316

   => Energetics <=

    Nuclear Repulsion Energy =             13.3949196619916275
    One-Electron Energy =                 -79.7584351936684612
    Two-Electron Energy =                  31.4319449136933962
    DFT Exchange-Correlation Energy =      -5.5868364646097097
    Empirical Dispersion Energy =           0.0000000000000000
    VV10 Nonlocal Energy =                  0.0000000000000000
    Total Energy =                        -40.5184070825931499

Computation Completed


Properties will be evaluated at   0.000000,   0.000000,   0.000000 [a0]

Properties computed using the SCF density matrix


 Multipole Moments:

 ------------------------------------------------------------------------------------
     Multipole            Electronic (a.u.)      Nuclear  (a.u.)        Total (a.u.)
 ------------------------------------------------------------------------------------

 L = 1.  Multiply by 2.5417464519 to convert [e a0] to [Debye]
 Dipole X            :         -0.0000070            0.0000055           -0.0000015
 Dipole Y            :         -0.0000020            0.0000012           -0.0000008
 Dipole Z            :          0.0000022           -0.0000013            0.0000009
 Magnitude           :                                                    0.0000020

 ------------------------------------------------------------------------------------

*** tstop() called on iCarus at Mon Jul  7 22:34:25 2025
Module time:
	user time   =        nan seconds =        nan minutes
	system time =        nan seconds =        nan minutes
	total time  =          0 seconds =       0.00 minutes
Total time:
	user time   =        nan seconds =        nan minutes
	system time =        nan seconds =        nan minutes
	total time  =          2 seconds =       0.03 minutes

*** tstart() called on iCarus
*** at Mon Jul  7 22:34:25 2025


         ------------------------------------------------------------
                                   SCF GRAD                          
                          Rob Parrish, Justin Turney,                
                       Andy Simmonett, and Alex Sokolov              
         ------------------------------------------------------------

  ==> Geometry <==

    Molecular point group: c1
    Full point group: C1

    Geometry (in Angstrom), charge = 0, multiplicity = 1:

       Center              X                  Y                   Z               Mass       
    ------------   -----------------  -----------------  -----------------  -----------------
         C            0.000000579599     0.000000122627    -0.000000135522    12.000000000000
         H           -0.676019008173     0.854978303694    -0.085446384605     1.007825032230
         H           -0.395340685064    -0.836774555468    -0.582066469625     1.007825032230
         H            0.084837393845    -0.294036038966     1.049588896945     1.007825032230
         H            0.986521719793     0.275832168114    -0.382075907193     1.007825032230

  Nuclear repulsion =   13.394919661991628

  ==> Basis Set <==

  Basis Set: 6-31G(D)
    Blend: 6-31G(D)
    Number of shells: 14
    Number of basis functions: 23
    Number of Cartesian functions: 23
    Spherical Harmonics?: false
    Max angular momentum: 2

  ==> DFJKGrad: Density-Fitted SCF Gradients <==

    Gradient:                    1
    J tasked:                  Yes
    K tasked:                  Yes
    wK tasked:                  No
    OpenMP threads:             10
    Integrals threads:          10
    Memory [MiB]:             -422
    Schwarz Cutoff:          1E-12
    Fitting Condition:       1E-10

   => Auxiliary Basis Set <=

  Basis Set: (6-31G(D) AUX)
    Blend: CC-PVDZ-JKFIT
    Number of shells: 60
    Number of basis functions: 181
    Number of Cartesian functions: 181
    Spherical Harmonics?: false
    Max angular momentum: 3

  ==> DFT Potential <==

   => LibXC <=

    Version 7.0.0
    S. Lehtola, C. Steigemann, M. J.T. Oliveira, and M. A.L. Marques.,  SoftwareX 7, 1–5 (2018) (10.1016/j.softx.2017.11.002)

   => Composite Functional: B3LYP <= 

    B3LYP Hyb-GGA Exchange-Correlation Functional

    P. J. Stephens, F. J. Devlin, C. F. Chabalowski, and M. J. Frisch.,  J. Phys. Chem. 98, 11623 (1994) (10.1021/j100096a001)

    Deriv               =              1
    GGA                 =           TRUE
    Meta                =          FALSE

    Exchange Hybrid     =           TRUE
    MP2 Hybrid          =          FALSE

   => Exchange Functionals <=

    0.0800   Slater exchange
    0.7200         Becke 88

   => Exact (HF) Exchange <=

    0.2000               HF 

   => Correlation Functionals <=

    0.1900   Vosko, Wilk & Nusair (VWN5_RPA)
    0.8100   Lee, Yang & Parr

   => LibXC Density Thresholds  <==

    XC_HYB_GGA_XC_B3LYP:  1.00E-15 

   => Molecular Quadrature <=

    Radial Scheme          =       TREUTLER
    Pruning Scheme         =           NONE
    Nuclear Scheme         =       TREUTLER

    Blocking Scheme        =         OCTREE
    BS radius alpha        =              1
    Pruning alpha          =              1
    Radial Points          =             75
    Spherical Points       =            302
    Total Points           =         109197
    Total Blocks           =            879
    Max Points             =            256
    Max Functions          =             23
    Weights Tolerance      =       1.00E-15


  -Total Gradient:
     Atom            X                  Y                   Z
    ------   -----------------  -----------------  -----------------
       1        0.000000425471    -0.000000330369    -0.000001844700
       2       -0.000014432079     0.000018011009    -0.000002048502
       3       -0.000007486674    -0.000016960013    -0.000012306726
       4        0.000001972359    -0.000006156317     0.000022246182
       5        0.000019714100     0.000004886034    -0.000007786275


*** tstop() called on iCarus at Mon Jul  7 22:34:25 2025
Module time:
	user time   =        nan seconds =        nan minutes
	system time =        nan seconds =        nan minutes
	total time  =          0 seconds =       0.00 minutes
Total time:
	user time   =        nan seconds =        nan minutes
	system time =        nan seconds =        nan minutes
	total time  =          2 seconds =       0.03 minutes

    			-----------------------------------------

    			 OPTKING 3.0: for geometry optimizations 

    			     By R.A. King, Bethel University     

    			        with contributions from          

    			    A.V. Copan, J. Cayton, A. Heide      

    			-----------------------------------------

    
	                            ===> Fragment 1 <== 

	 Z (Atomic Numbers)          Masses                          Geom                  
	       6.000000            12.000000          0.00000110  0.00000023 -0.00000026   
	       1.000000             1.007825         -1.27749078  1.61567484 -0.16147027   
	       1.000000             1.007825         -0.74708562 -1.58127474 -1.09994621   
	       1.000000             1.007825          0.16031944 -0.55564758  1.98343556   
	       1.000000             1.007825          1.86425587  0.52124725 -0.72201882   

	 - Coordinate -           - BOHR/RAD -       - ANG/DEG -
	 R(1,2)           =         2.066026           1.093294
	 R(1,3)           =         2.066023           1.093292
	 R(1,4)           =         2.066026           1.093294
	 R(1,5)           =         2.066024           1.093293
	 B(2,1,3)         =         1.910634         109.471243
	 B(2,1,4)         =         1.910632         109.471160
	 B(2,1,5)         =         1.910633         109.471232
	 B(3,1,4)         =         1.910631         109.471119
	 B(3,1,5)         =         1.910632         109.471162
	 B(4,1,5)         =         1.910637         109.471408
	 O(2,1,3,4)       =         0.955318          54.735700
	 O(2,1,3,5)       =        -0.955317         -54.735606
	 O(2,1,4,5)       =         0.955316          54.735566
	 O(3,1,2,4)       =        -0.955319         -54.735720
	 O(3,1,2,5)       =         0.955317          54.735641
	 O(3,1,4,5)       =        -0.955318         -54.735677
	 O(4,1,2,3)       =         0.955319          54.735761
	 O(4,1,2,5)       =        -0.955314         -54.735478
	 O(4,1,3,5)       =         0.955316          54.735554
	 O(5,1,2,3)       =        -0.955317         -54.735647
	 O(5,1,2,4)       =         0.955314          54.735442
	 O(5,1,3,4)       =        -0.955315         -54.735532


	        --- Internal Coordinate Step in ANG or DEG, aJ/ANG or AJ/DEG ---
	-------------------------------------------------------------------------------
	           Coordinate      Previous         Force          Change          New 
	           ----------      --------        ------          ------        ------
	               R(1,2)       1.09329       -0.00019       -0.00004       1.09326
	               R(1,3)       1.09329       -0.00018       -0.00003       1.09326
	               R(1,4)       1.09329       -0.00019       -0.00004       1.09326
	               R(1,5)       1.09329       -0.00018       -0.00003       1.09326
	             B(2,1,3)     109.47124       -0.00000       -0.00004     109.47120
	             B(2,1,4)     109.47116        0.00000        0.00007     109.47123
	             B(2,1,5)     109.47123       -0.00000       -0.00014     109.47109
	             B(3,1,4)     109.47112       -0.00000       -0.00012     109.47100
	             B(3,1,5)     109.47116        0.00000        0.00015     109.47131
	             B(4,1,5)     109.47141        0.00000        0.00008     109.47149
	           O(2,1,3,4)      54.73570       -0.00000        0.00003      54.73573
	           O(2,1,3,5)     -54.73561       -0.00000       -0.00011     -54.73571
	           O(2,1,4,5)      54.73557        0.00000        0.00003      54.73560
	           O(3,1,2,4)     -54.73572       -0.00000       -0.00012     -54.73584
	           O(3,1,2,5)      54.73564       -0.00000       -0.00004      54.73560
	           O(3,1,4,5)     -54.73568        0.00000        0.00008     -54.73560
	           O(4,1,2,3)      54.73576        0.00000        0.00007      54.73583
	           O(4,1,2,5)     -54.73548        0.00000        0.00008     -54.73540
	           O(4,1,3,5)      54.73555        0.00000       -0.00004      54.73552
	           O(5,1,2,3)     -54.73565       -0.00000       -0.00001     -54.73566
	           O(5,1,2,4)      54.73544        0.00000        0.00003      54.73547
	           O(5,1,3,4)     -54.73553        0.00000        0.00017     -54.73536
	-------------------------------------------------------------------------------

	                                 ==> Convergence Check <==                                  
    
	Measures of convergence in internal coordinates in au.
    
	Criteria marked as inactive (o), active & met (*), and active & unmet ( ).

	----------------------------------------------------------------------------------------------~
	   Step    Total Energy     Delta E     Max Force     RMS Force      Max Disp      RMS Disp   ~
	----------------------------------------------------------------------------------------------~
	  Convergence Criteria     1.00e-06 *    3.00e-04 *             o    1.20e-03 *             o~
	----------------------------------------------------------------------------------------------~
	     1     -40.51840708   -4.05e+01      2.35e-05 *    9.63e-06 o    6.81e-05 *    2.80e-05 o  ~
	----------------------------------------------------------------------------------------------

Next Geometry in Ang 
	Fragment 1 (Ang)

	    C   0.0000006947   0.0000000848   0.0000003408
	    H  -0.6759963464   0.8549505854  -0.0854440669
	    H  -0.3953296651  -0.8367488596  -0.5820469370
	    H   0.0848339742  -0.2940273536   1.0495545378
	    H   0.9864913424   0.2758255431  -0.3820638747


    Final optimized geometry and variables:
    Molecular point group: c1
    Full point group: C1

    Geometry (in Angstrom), charge = 0, multiplicity = 1:

    C            0.000000579599     0.000000122627    -0.000000135522
    H           -0.676019008173     0.854978303694    -0.085446384605
    H           -0.395340685064    -0.836774555468    -0.582066469625
    H            0.084837393845    -0.294036038966     1.049588896945
    H            0.986521719793     0.275832168114    -0.382075907193


Scratch directory: D:\BaiduSyncdisk\py\MS\psi4_scratch/
gradient() will perform analytic gradient computation.
   => Libint2 <=

    Primary   basis highest AM E, G, H:  6, 6, -
    Auxiliary basis highest AM E, G, H:  7, 7, -
    Onebody   basis highest AM E, G, H:  -, -, -
    Solid Harmonics ordering:            Gaussian

*** tstart() called on iCarus
*** at Mon Jul  7 22:34:25 2025

   => Loading Basis Set <=

    Name: 6-31G(D)
    Role: ORBITAL
    Keyword: BASIS
    atoms 1   entry C          line   111 file D:\anaconda3\envs\nistpy\Library\share\psi4\basis\6-31g_d_.gbs 
    atoms 2-5 entry H          line    44 file D:\anaconda3\envs\nistpy\Library\share\psi4\basis\6-31g_d_.gbs 


         ---------------------------------------------------------
                                   SCF
               by Justin Turney, Rob Parrish, Andy Simmonett
                          and Daniel G. A. Smith
                              UKS Reference
                       10 Threads,   -562 MiB Core
         ---------------------------------------------------------

  ==> Geometry <==

    Molecular point group: c1
    Full point group: C1

    Geometry (in Angstrom), charge = 1, multiplicity = 2:

       Center              X                  Y                   Z               Mass       
    ------------   -----------------  -----------------  -----------------  -----------------
         C            0.000000579599     0.000000122627    -0.000000135522    12.000000000000
         H           -0.676019008173     0.854978303694    -0.085446384605     1.007825032230
         H           -0.395340685064    -0.836774555468    -0.582066469625     1.007825032230
         H            0.084837393845    -0.294036038966     1.049588896945     1.007825032230
         H            0.986521719793     0.275832168114    -0.382075907193     1.007825032230

  Running in c1 symmetry.

  Rotational constants: A =      5.24771  B =      5.24771  C =      5.24770 [cm^-1]
  Rotational constants: A = 157322.42721  B = 157322.26093  C = 157321.95422 [MHz]
  Nuclear repulsion =   13.394919661991628

  Charge       = 1
  Multiplicity = 2
  Electrons    = 9
  Nalpha       = 5
  Nbeta        = 4

  ==> Algorithm <==

  SCF Algorithm Type is DF.
  DIIS enabled.
  MOM disabled.
  Fractional occupation disabled.
  Guess Type is SAD.
  Energy threshold   = 1.00e-08
  Density threshold  = 1.00e-08
  Integral threshold = 1.00e-12

  ==> Primary Basis <==

  Basis Set: 6-31G(D)
    Blend: 6-31G(D)
    Number of shells: 14
    Number of basis functions: 23
    Number of Cartesian functions: 23
    Spherical Harmonics?: false
    Max angular momentum: 2

  ==> DFT Potential <==

   => LibXC <=

    Version 7.0.0
    S. Lehtola, C. Steigemann, M. J.T. Oliveira, and M. A.L. Marques.,  SoftwareX 7, 1–5 (2018) (10.1016/j.softx.2017.11.002)

   => Composite Functional: B3LYP <= 

    B3LYP Hyb-GGA Exchange-Correlation Functional

    P. J. Stephens, F. J. Devlin, C. F. Chabalowski, and M. J. Frisch.,  J. Phys. Chem. 98, 11623 (1994) (10.1021/j100096a001)

    Deriv               =              1
    GGA                 =           TRUE
    Meta                =          FALSE

    Exchange Hybrid     =           TRUE
    MP2 Hybrid          =          FALSE

   => Exchange Functionals <=

    0.0800   Slater exchange
    0.7200         Becke 88

   => Exact (HF) Exchange <=

    0.2000               HF 

   => Correlation Functionals <=

    0.1900   Vosko, Wilk & Nusair (VWN5_RPA)
    0.8100   Lee, Yang & Parr

   => LibXC Density Thresholds  <==

    XC_HYB_GGA_XC_B3LYP:  1.00E-15 

   => Molecular Quadrature <=

    Radial Scheme          =       TREUTLER
    Pruning Scheme         =           NONE
    Nuclear Scheme         =       TREUTLER

    Blocking Scheme        =         OCTREE
    BS radius alpha        =              1
    Pruning alpha          =              1
    Radial Points          =             75
    Spherical Points       =            302
    Total Points           =         109197
    Total Blocks           =            879
    Max Points             =            256
    Max Functions          =             23
    Weights Tolerance      =       1.00E-15

   => Loading Basis Set <=

    Name: (6-31G(D) AUX)
    Role: JKFIT
    Keyword: DF_BASIS_SCF
    atoms 1   entry C          line   121 file D:\anaconda3\envs\nistpy\Library\share\psi4\basis\cc-pvdz-jkfit.gbs 
    atoms 2-5 entry H          line    51 file D:\anaconda3\envs\nistpy\Library\share\psi4\basis\cc-pvdz-jkfit.gbs 

  ==> Integral Setup <==

  DFHelper Memory: AOs need 0.001 GiB; user supplied 5.515 GiB. 
  Using in-core AOs.

  ==> MemDFJK: Density-Fitted J/K Matrices <==

    J tasked:                   Yes
    K tasked:                   Yes
    wK tasked:                   No
    OpenMP threads:              10
    Memory [MiB]:              5647
    Algorithm:                 Core
    Schwarz Cutoff:           1E-12
    Mask sparsity (%):       0.0000
    Fitting Condition:        1E-10

   => Auxiliary Basis Set <=

  Basis Set: (6-31G(D) AUX)
    Blend: CC-PVDZ-JKFIT
    Number of shells: 60
    Number of basis functions: 181
    Number of Cartesian functions: 181
    Spherical Harmonics?: false
    Max angular momentum: 3

  Cached 100.0% of DFT collocation blocks in 0.073 [GiB].

  Minimum eigenvalue in the overlap matrix is 1.7960254003E-02.
  Reciprocal condition number of the overlap matrix is 3.2047277007E-03.
    Using symmetric orthogonalization.

  ==> Pre-Iterations <==

  SCF Guess: Superposition of Atomic Densities via on-the-fly atomic UHF (no occupation information).

   -------------------------
    Irrep   Nso     Nmo    
   -------------------------
     A         23      23 
   -------------------------
    Total      23      23
   -------------------------

  ==> Iterations <==

                           Total Energy        Delta E     RMS |[F,P]|

   @DF-UKS iter SAD:   -40.09237109601601   -4.00924e+01   0.00000e+00 
   @DF-UKS iter   1:   -39.95958410288382    1.32787e-01   1.19156e-02 DIIS/ADIIS
   @DF-UKS iter   2:   -39.97015889655419   -1.05748e-02   1.00809e-02 DIIS/ADIIS
   @DF-UKS iter   3:   -39.99824310655259   -2.80842e-02   6.89536e-04 DIIS/ADIIS
   @DF-UKS iter   4:   -39.99856857464692   -3.25468e-04   2.12610e-04 DIIS/ADIIS
   @DF-UKS iter   5:   -39.99879679291197   -2.28218e-04   2.87595e-04 DIIS/ADIIS
   @DF-UKS iter   6:   -39.99895140639856   -1.54613e-04   2.55660e-04 DIIS/ADIIS
   @DF-UKS iter   7:   -39.99922838471983   -2.76978e-04   2.10425e-04 DIIS/ADIIS
   @DF-UKS iter   8:   -39.99957232466834   -3.43940e-04   1.13115e-04 DIIS/ADIIS
   @DF-UKS iter   9:   -39.99956679389669    5.53077e-06   6.89865e-05 DIIS
   @DF-UKS iter  10:   -39.99958072260736   -1.39287e-05   4.16756e-05 DIIS
   @DF-UKS iter  11:   -39.99958174848346   -1.02588e-06   9.87605e-06 DIIS
   @DF-UKS iter  12:   -39.99958184134193   -9.28585e-08   1.35474e-06 DIIS
   @DF-UKS iter  13:   -39.99958184400316   -2.66123e-09   6.33902e-07 DIIS
   @DF-UKS iter  14:   -39.99958184506082   -1.05766e-09   4.84581e-07 DIIS
   @DF-UKS iter  15:   -39.99958184635864   -1.29782e-09   2.05681e-07 DIIS
   @DF-UKS iter  16:   -39.99958184659360   -2.34962e-10   8.01186e-08 DIIS
   @DF-UKS iter  17:   -39.99958184661710   -2.34976e-11   2.29180e-08 DIIS
   @DF-UKS iter  18:   -39.99958184661812   -1.02318e-12   1.61860e-09 DIIS
  Energy and wave function converged.


  ==> Post-Iterations <==

   Electrons on quadrature grid:
      Nalpha   =    5.0000002658 ; deviation = 2.658e-07
      Nbeta    =    4.0000005491 ; deviation = 5.491e-07
      Ntotal   =    9.0000008149 ; deviation = 8.149e-07 

   @Spin Contamination Metric:   2.327419369E-03
   @S^2 Expected:                7.500000000E-01
   @S^2 Observed:                7.523274194E-01
   @S   Expected:                5.000000000E-01
   @S   Observed:                5.000000000E-01

    Orbital Energies [Eh]
    ---------------------

    Alpha Occupied:                                                       

       1A    -10.570186     2A     -1.090621     3A     -0.798355  
       4A     -0.769268     5A     -0.769268  

    Alpha Virtual:                                                        

       6A     -0.168518     7A     -0.138243     8A     -0.132746  
       9A     -0.132746    10A      0.190199    11A      0.205389  
      12A      0.205392    13A      0.539292    14A      0.546490  
      15A      0.546490    16A      0.573191    17A      0.773189  
      18A      1.225580    19A      1.269461    20A      1.832715  
      21A      1.832716    22A      1.859773    23A      3.783807  

    Beta Occupied:                                                        

       1A    -10.561962     2A     -1.041090     3A     -0.741625  
       4A     -0.741624  

    Beta Virtual:                                                         

       5A     -0.648744     6A     -0.154391     7A     -0.116766  
       8A     -0.116766     9A     -0.107633    10A      0.217038  
      11A      0.217041    12A      0.222786    13A      0.571971  
      14A      0.571971    15A      0.580449    16A      0.613113  
      17A      0.787500    18A      1.272421    19A      1.298000  
      20A      1.869523    21A      1.878827    22A      1.878829  
      23A      3.807865  

    Final Occupation by Irrep:
              A 
    DOCC [     4 ]
    SOCC [     1 ]
    NA   [     5 ]
    NB   [     4 ]

  @DF-UKS Final Energy:   -39.99958184661812

   => Energetics <=

    Nuclear Repulsion Energy =             13.3949196619916275
    One-Electron Energy =                 -75.8888430027839433
    Two-Electron Energy =                  27.7953364312939932
    DFT Exchange-Correlation Energy =      -5.3009949371197971
    Empirical Dispersion Energy =           0.0000000000000000
    VV10 Nonlocal Energy =                  0.0000000000000000
    Total Energy =                        -39.9995818466181134

  UHF NO Occupations:
  HONO-2 :    3  A 1.9995792
  HONO-1 :    4  A 1.9995792
  HONO-0 :    5  A 1.0000000
  LUNO+0 :    6  A 0.0004208
  LUNO+1 :    7  A 0.0004208
  LUNO+2 :    8  A 0.0003221
  LUNO+3 :    9  A 0.0000002


Computation Completed


Properties will be evaluated at   0.000000,   0.000000,   0.000000 [a0]

Properties computed using the SCF density matrix


 Multipole Moments:

 ------------------------------------------------------------------------------------
     Multipole            Electronic (a.u.)      Nuclear  (a.u.)        Total (a.u.)
 ------------------------------------------------------------------------------------

 L = 1.  Multiply by 2.5417464519 to convert [e a0] to [Debye]
 Dipole X            :         -0.0000162            0.0000055           -0.0000107
 Dipole Y            :          0.0000169            0.0000012            0.0000181
 Dipole Z            :          0.0000214           -0.0000013            0.0000201
 Magnitude           :                                                    0.0000291

 ------------------------------------------------------------------------------------

*** tstop() called on iCarus at Mon Jul  7 22:34:26 2025
Module time:
	user time   =        nan seconds =        nan minutes
	system time =        nan seconds =        nan minutes
	total time  =          1 seconds =       0.02 minutes
Total time:
	user time   =        nan seconds =        nan minutes
	system time =        nan seconds =        nan minutes
	total time  =          3 seconds =       0.05 minutes

*** tstart() called on iCarus
*** at Mon Jul  7 22:34:26 2025


         ------------------------------------------------------------
                                   SCF GRAD                          
                          Rob Parrish, Justin Turney,                
                       Andy Simmonett, and Alex Sokolov              
         ------------------------------------------------------------

  ==> Geometry <==

    Molecular point group: c1
    Full point group: C1

    Geometry (in Angstrom), charge = 1, multiplicity = 2:

       Center              X                  Y                   Z               Mass       
    ------------   -----------------  -----------------  -----------------  -----------------
         C            0.000000579599     0.000000122627    -0.000000135522    12.000000000000
         H           -0.676019008173     0.854978303694    -0.085446384605     1.007825032230
         H           -0.395340685064    -0.836774555468    -0.582066469625     1.007825032230
         H            0.084837393845    -0.294036038966     1.049588896945     1.007825032230
         H            0.986521719793     0.275832168114    -0.382075907193     1.007825032230

  Nuclear repulsion =   13.394919661991628

  ==> Basis Set <==

  Basis Set: 6-31G(D)
    Blend: 6-31G(D)
    Number of shells: 14
    Number of basis functions: 23
    Number of Cartesian functions: 23
    Spherical Harmonics?: false
    Max angular momentum: 2

  ==> DFJKGrad: Density-Fitted SCF Gradients <==

    Gradient:                    1
    J tasked:                  Yes
    K tasked:                  Yes
    wK tasked:                  No
    OpenMP threads:             10
    Integrals threads:          10
    Memory [MiB]:             -422
    Schwarz Cutoff:          1E-12
    Fitting Condition:       1E-10

   => Auxiliary Basis Set <=

  Basis Set: (6-31G(D) AUX)
    Blend: CC-PVDZ-JKFIT
    Number of shells: 60
    Number of basis functions: 181
    Number of Cartesian functions: 181
    Spherical Harmonics?: false
    Max angular momentum: 3

  ==> DFT Potential <==

   => LibXC <=

    Version 7.0.0
    S. Lehtola, C. Steigemann, M. J.T. Oliveira, and M. A.L. Marques.,  SoftwareX 7, 1–5 (2018) (10.1016/j.softx.2017.11.002)

   => Composite Functional: B3LYP <= 

    B3LYP Hyb-GGA Exchange-Correlation Functional

    P. J. Stephens, F. J. Devlin, C. F. Chabalowski, and M. J. Frisch.,  J. Phys. Chem. 98, 11623 (1994) (10.1021/j100096a001)

    Deriv               =              1
    GGA                 =           TRUE
    Meta                =          FALSE

    Exchange Hybrid     =           TRUE
    MP2 Hybrid          =          FALSE

   => Exchange Functionals <=

    0.0800   Slater exchange
    0.7200         Becke 88

   => Exact (HF) Exchange <=

    0.2000               HF 

   => Correlation Functionals <=

    0.1900   Vosko, Wilk & Nusair (VWN5_RPA)
    0.8100   Lee, Yang & Parr

   => LibXC Density Thresholds  <==

    XC_HYB_GGA_XC_B3LYP:  1.00E-15 

   => Molecular Quadrature <=

    Radial Scheme          =       TREUTLER
    Pruning Scheme         =           NONE
    Nuclear Scheme         =       TREUTLER

    Blocking Scheme        =         OCTREE
    BS radius alpha        =              1
    Pruning alpha          =              1
    Radial Points          =             75
    Spherical Points       =            302
    Total Points           =         109197
    Total Blocks           =            879
    Max Points             =            256
    Max Functions          =             23
    Weights Tolerance      =       1.00E-15


  -Total Gradient:
     Atom            X                  Y                   Z
    ------   -----------------  -----------------  -----------------
       1       -0.000004518261     0.000007527504     0.000006340565
       2       -0.009220533981    -0.049674589387    -0.025581623266
       3       -0.025801453447     0.050264213850     0.003755227659
       4        0.044146487562     0.016533402628    -0.031377916436
       5       -0.009119827658    -0.017131924334     0.053196285832


*** tstop() called on iCarus at Mon Jul  7 22:34:26 2025
Module time:
	user time   =        nan seconds =        nan minutes
	system time =        nan seconds =        nan minutes
	total time  =          0 seconds =       0.00 minutes
Total time:
	user time   =        nan seconds =        nan minutes
	system time =        nan seconds =        nan minutes
	total time  =          3 seconds =       0.05 minutes

    			-----------------------------------------

    			 OPTKING 3.0: for geometry optimizations 

    			     By R.A. King, Bethel University     

    			        with contributions from          

    			    A.V. Copan, J. Cayton, A. Heide      

    			-----------------------------------------

    
	                            ===> Fragment 1 <== 

	 Z (Atomic Numbers)          Masses                          Geom                  
	       6.000000            12.000000          0.00000110  0.00000023 -0.00000026   
	       1.000000             1.007825         -1.27749078  1.61567484 -0.16147027   
	       1.000000             1.007825         -0.74708562 -1.58127474 -1.09994621   
	       1.000000             1.007825          0.16031944 -0.55564758  1.98343556   
	       1.000000             1.007825          1.86425587  0.52124725 -0.72201882   

	 - Coordinate -           - BOHR/RAD -       - ANG/DEG -
	 R(1,2)           =         2.066026           1.093294
	 R(1,3)           =         2.066023           1.093292
	 R(1,4)           =         2.066026           1.093294
	 R(1,5)           =         2.066024           1.093293
	 B(2,1,3)         =         1.910634         109.471243
	 B(2,1,4)         =         1.910632         109.471160
	 B(2,1,5)         =         1.910633         109.471232
	 B(3,1,4)         =         1.910631         109.471119
	 B(3,1,5)         =         1.910632         109.471162
	 B(4,1,5)         =         1.910637         109.471408
	 O(2,1,3,4)       =         0.955318          54.735700
	 O(2,1,3,5)       =        -0.955317         -54.735606
	 O(2,1,4,5)       =         0.955316          54.735566
	 O(3,1,2,4)       =        -0.955319         -54.735720
	 O(3,1,2,5)       =         0.955317          54.735641
	 O(3,1,4,5)       =        -0.955318         -54.735677
	 O(4,1,2,3)       =         0.955319          54.735761
	 O(4,1,2,5)       =        -0.955314         -54.735478
	 O(4,1,3,5)       =         0.955316          54.735554
	 O(5,1,2,3)       =        -0.955317         -54.735647
	 O(5,1,2,4)       =         0.955314          54.735442
	 O(5,1,3,4)       =        -0.955315         -54.735532


	        --- Internal Coordinate Step in ANG or DEG, aJ/ANG or AJ/DEG ---
	-------------------------------------------------------------------------------
	           Coordinate      Previous         Force          Change          New 
	           ----------      --------        ------          ------        ------
	               R(1,2)       1.09329        0.25660        0.03941       1.13270
	               R(1,3)       1.09329        0.25656        0.03940       1.13270
	               R(1,4)       1.09329        0.25659        0.03941       1.13270
	               R(1,5)       1.09329        0.25657        0.03941       1.13270
	             B(2,1,3)     109.47124        0.00330       11.64575     121.11699
	             B(2,1,4)     109.47116       -0.00165       -5.49010     103.98106
	             B(2,1,5)     109.47123       -0.00165       -5.49000     103.98123
	             B(3,1,4)     109.47112       -0.00165       -5.48984     103.98128
	             B(3,1,5)     109.47116       -0.00165       -5.48944     103.98172
	             B(4,1,5)     109.47141        0.00330       11.64579     121.11720
	           O(2,1,3,4)      54.73570       -0.00083       -4.53257      50.20313
	           O(2,1,3,5)     -54.73561        0.00083        4.53281     -50.20280
	           O(2,1,4,5)      54.73557        0.00165        5.82333      60.55889
	           O(3,1,2,4)     -54.73572        0.00083        4.53265     -50.20307
	           O(3,1,2,5)      54.73564       -0.00083       -4.53299      50.20265
	           O(3,1,4,5)     -54.73568       -0.00165       -5.82242     -60.55810
	           O(4,1,2,3)      54.73576        0.00165        5.82318      60.55894
	           O(4,1,2,5)     -54.73548        0.00083        4.53254     -50.20294
	           O(4,1,3,5)      54.73555       -0.00083       -4.53301      50.20254
	           O(5,1,2,3)     -54.73565       -0.00165       -5.82261     -60.55825
	           O(5,1,2,4)      54.73544       -0.00083       -4.53256      50.20289
	           O(5,1,3,4)     -54.73553        0.00083        4.53312     -50.20241
	-------------------------------------------------------------------------------

	                                 ==> Convergence Check <==                                  
    
	Measures of convergence in internal coordinates in au.
    
	Criteria marked as inactive (o), active & met (*), and active & unmet ( ).

	----------------------------------------------------------------------------------------------~
	   Step    Total Energy     Delta E     Max Force     RMS Force      Max Disp      RMS Disp   ~
	----------------------------------------------------------------------------------------------~
	  Convergence Criteria     1.00e-06 *    3.00e-04 *             o    1.20e-03 *             o~
	----------------------------------------------------------------------------------------------~
	     1     -39.99958185   -4.00e+01      4.34e-02      2.37e-02 o    2.03e-01      1.03e-01 o  ~
	----------------------------------------------------------------------------------------------

Next Geometry in Ang 
	Fragment 1 (Ang)

	    C   0.0000019807  -0.0000021933  -0.0000021399
	    H  -0.6275748746   0.9427425029  -0.0200015478
	    H  -0.3174192696  -0.9266847195  -0.5687768987
	    H  -0.0256953433  -0.3228875218   1.0854016773
	    H   0.9706875066   0.3068319316  -0.4966210909


    Structure for next step:
    Molecular point group: c1
    Full point group: C1

    Geometry (in Angstrom), charge = 1, multiplicity = 2:

    C            0.000001980746    -0.000002193280    -0.000002139885
    H           -0.627574874567     0.942742502890    -0.020001547843
    H           -0.317419269552    -0.926684719478    -0.568776898670
    H           -0.025695343252    -0.322887521772     1.085401677289
    H            0.970687506625     0.306831931641    -0.496621090891


Scratch directory: D:\BaiduSyncdisk\py\MS\psi4_scratch/
gradient() will perform analytic gradient computation.
   => Libint2 <=

    Primary   basis highest AM E, G, H:  6, 6, -
    Auxiliary basis highest AM E, G, H:  7, 7, -
    Onebody   basis highest AM E, G, H:  -, -, -
    Solid Harmonics ordering:            Gaussian

*** tstart() called on iCarus
*** at Mon Jul  7 22:34:26 2025

   => Loading Basis Set <=

    Name: 6-31G(D)
    Role: ORBITAL
    Keyword: BASIS
    atoms 1   entry C          line   111 file D:\anaconda3\envs\nistpy\Library\share\psi4\basis\6-31g_d_.gbs 
    atoms 2-5 entry H          line    44 file D:\anaconda3\envs\nistpy\Library\share\psi4\basis\6-31g_d_.gbs 


         ---------------------------------------------------------
                                   SCF
               by Justin Turney, Rob Parrish, Andy Simmonett
                          and Daniel G. A. Smith
                              UKS Reference
                       10 Threads,   -562 MiB Core
         ---------------------------------------------------------

  ==> Geometry <==

    Molecular point group: c1
    Full point group: C1

    Geometry (in Angstrom), charge = 1, multiplicity = 2:

       Center              X                  Y                   Z               Mass       
    ------------   -----------------  -----------------  -----------------  -----------------
         C            0.000001980746    -0.000002193280    -0.000002139885    12.000000000000
         H           -0.627574874567     0.942742502890    -0.020001547843     1.007825032230
         H           -0.317419269552    -0.926684719478    -0.568776898670     1.007825032230
         H           -0.025695343252    -0.322887521772     1.085401677289     1.007825032230
         H            0.970687506625     0.306831931641    -0.496621090891     1.007825032230

  Running in c1 symmetry.

  Rotational constants: A =      5.25010  B =      5.25009  C =      4.29760 [cm^-1]
  Rotational constants: A = 157394.08020  B = 157393.65886  C = 128838.71065 [MHz]
  Nuclear repulsion =   12.934697663608981

  Charge       = 1
  Multiplicity = 2
  Electrons    = 9
  Nalpha       = 5
  Nbeta        = 4

  ==> Algorithm <==

  SCF Algorithm Type is DF.
  DIIS enabled.
  MOM disabled.
  Fractional occupation disabled.
  Guess Type is READ.
  Energy threshold   = 1.00e-08
  Density threshold  = 1.00e-08
  Integral threshold = 1.00e-12

  ==> Primary Basis <==

  Basis Set: 6-31G(D)
    Blend: 6-31G(D)
    Number of shells: 14
    Number of basis functions: 23
    Number of Cartesian functions: 23
    Spherical Harmonics?: false
    Max angular momentum: 2

  ==> DFT Potential <==

   => LibXC <=

    Version 7.0.0
    S. Lehtola, C. Steigemann, M. J.T. Oliveira, and M. A.L. Marques.,  SoftwareX 7, 1–5 (2018) (10.1016/j.softx.2017.11.002)

   => Composite Functional: B3LYP <= 

    B3LYP Hyb-GGA Exchange-Correlation Functional

    P. J. Stephens, F. J. Devlin, C. F. Chabalowski, and M. J. Frisch.,  J. Phys. Chem. 98, 11623 (1994) (10.1021/j100096a001)

    Deriv               =              1
    GGA                 =           TRUE
    Meta                =          FALSE

    Exchange Hybrid     =           TRUE
    MP2 Hybrid          =          FALSE

   => Exchange Functionals <=

    0.0800   Slater exchange
    0.7200         Becke 88

   => Exact (HF) Exchange <=

    0.2000               HF 

   => Correlation Functionals <=

    0.1900   Vosko, Wilk & Nusair (VWN5_RPA)
    0.8100   Lee, Yang & Parr

   => LibXC Density Thresholds  <==

    XC_HYB_GGA_XC_B3LYP:  1.00E-15 

   => Molecular Quadrature <=

    Radial Scheme          =       TREUTLER
    Pruning Scheme         =           NONE
    Nuclear Scheme         =       TREUTLER

    Blocking Scheme        =         OCTREE
    BS radius alpha        =              1
    Pruning alpha          =              1
    Radial Points          =             75
    Spherical Points       =            302
    Total Points           =         109384
    Total Blocks           =            873
    Max Points             =            256
    Max Functions          =             23
    Weights Tolerance      =       1.00E-15

   => Loading Basis Set <=

    Name: (6-31G(D) AUX)
    Role: JKFIT
    Keyword: DF_BASIS_SCF
    atoms 1   entry C          line   121 file D:\anaconda3\envs\nistpy\Library\share\psi4\basis\cc-pvdz-jkfit.gbs 
    atoms 2-5 entry H          line    51 file D:\anaconda3\envs\nistpy\Library\share\psi4\basis\cc-pvdz-jkfit.gbs 

   => Loading Basis Set <=

    Name: 6-31G(D)
    Role: ORBITAL
    Keyword: BASIS
    atoms 1   entry C          line   111 file D:\anaconda3\envs\nistpy\Library\share\psi4\basis\6-31g_d_.gbs 
    atoms 2-5 entry H          line    44 file D:\anaconda3\envs\nistpy\Library\share\psi4\basis\6-31g_d_.gbs 

  Reading orbitals from file D:\BaiduSyncdisk\py\MS\psi4_scratch/psi4_calculation.default.33932.180.npy, no projection.

  ==> Integral Setup <==

  DFHelper Memory: AOs need 0.001 GiB; user supplied 5.515 GiB. 
  Using in-core AOs.

  ==> MemDFJK: Density-Fitted J/K Matrices <==

    J tasked:                   Yes
    K tasked:                   Yes
    wK tasked:                   No
    OpenMP threads:              10
    Memory [MiB]:              5647
    Algorithm:                 Core
    Schwarz Cutoff:           1E-12
    Mask sparsity (%):       0.0000
    Fitting Condition:        1E-10

   => Auxiliary Basis Set <=

  Basis Set: (6-31G(D) AUX)
    Blend: CC-PVDZ-JKFIT
    Number of shells: 60
    Number of basis functions: 181
    Number of Cartesian functions: 181
    Spherical Harmonics?: false
    Max angular momentum: 3

  Cached 100.0% of DFT collocation blocks in 0.073 [GiB].

  Minimum eigenvalue in the overlap matrix is 2.1108196251E-02.
  Reciprocal condition number of the overlap matrix is 3.8647928417E-03.
    Using symmetric orthogonalization.

  ==> Pre-Iterations <==

  SCF Guess: Orbitals guess was supplied from a previous computation.

   -------------------------------------------------------
    Irrep   Nso     Nmo     Nalpha   Nbeta   Ndocc  Nsocc
   -------------------------------------------------------
     A         23      23       5       4       4       1
   -------------------------------------------------------
    Total      23      23       5       4       4       1
   -------------------------------------------------------

  ==> Iterations <==

                           Total Energy        Delta E     RMS |[F,P]|

   @DF-UKS iter   0:   -40.00354564672083   -4.00035e+01   2.79966e-03 
   @DF-UKS iter   1:   -40.03461408272874   -3.10684e-02   4.38168e-04 DIIS/ADIIS
   @DF-UKS iter   2:   -40.03462107522086   -6.99249e-06   3.83820e-04 DIIS/ADIIS
   @DF-UKS iter   3:   -40.03466106535819   -3.99901e-05   2.26278e-05 DIIS
   @DF-UKS iter   4:   -40.03466123860274   -1.73245e-07   2.96871e-06 DIIS
   @DF-UKS iter   5:   -40.03466124504799   -6.44525e-09   5.56666e-07 DIIS
   @DF-UKS iter   6:   -40.03466124532558   -2.77588e-10   3.57972e-08 DIIS
   @DF-UKS iter   7:   -40.03466124532644   -8.59757e-13   4.72073e-09 DIIS
  Energy and wave function converged.


  ==> Post-Iterations <==

   Electrons on quadrature grid:
      Nalpha   =    5.0000001557 ; deviation = 1.557e-07
      Nbeta    =    4.0000003237 ; deviation = 3.237e-07
      Ntotal   =    9.0000004794 ; deviation = 4.794e-07 

   @Spin Contamination Metric:   1.801258815E-03
   @S^2 Expected:                7.500000000E-01
   @S^2 Observed:                7.518012588E-01
   @S   Expected:                5.000000000E-01
   @S   Observed:                5.000000000E-01

    Orbital Energies [Eh]
    ---------------------

    Alpha Occupied:                                                       

       1A    -10.573930     2A     -1.068660     3A     -0.769947  
       4A     -0.769946     5A     -0.741766  

    Alpha Virtual:                                                        

       6A     -0.184032     7A     -0.180610     8A     -0.127261  
       9A     -0.127261    10A      0.190346    11A      0.239503  
      12A      0.239503    13A      0.497374    14A      0.523062  
      15A      0.544039    16A      0.544039    17A      0.727036  
      18A      1.272293    19A      1.311670    20A      1.746863  
      21A      1.746865    22A      1.865546    23A      3.755442  

    Beta Occupied:                                                        

       1A    -10.565457     2A     -1.022765     3A     -0.743408  
       4A     -0.743408  

    Beta Virtual:                                                         

       5A     -0.597163     6A     -0.164332     7A     -0.150548  
       8A     -0.112280     9A     -0.112280    10A      0.227372  
      11A      0.251077    12A      0.251078    13A      0.532241  
      14A      0.563979    15A      0.569120    16A      0.569121  
      17A      0.742657    18A      1.275778    19A      1.382366  
      20A      1.793338    21A      1.793340    22A      1.876009  
      23A      3.780201  

    Final Occupation by Irrep:
              A 
    DOCC [     4 ]
    SOCC [     1 ]
    NA   [     5 ]
    NB   [     4 ]

  @DF-UKS Final Energy:   -40.03466124532644

   => Energetics <=

    Nuclear Repulsion Energy =             12.9346976636089810
    One-Electron Energy =                 -75.1496781317120508
    Two-Electron Energy =                  27.4435527358101616
    DFT Exchange-Correlation Energy =      -5.2632335130335246
    Empirical Dispersion Energy =           0.0000000000000000
    VV10 Nonlocal Energy =                  0.0000000000000000
    Total Energy =                        -40.0346612453264328

  UHF NO Occupations:
  HONO-2 :    3  A 1.9996915
  HONO-1 :    4  A 1.9996915
  HONO-0 :    5  A 1.0000000
  LUNO+0 :    6  A 0.0003085
  LUNO+1 :    7  A 0.0003085
  LUNO+2 :    8  A 0.0002835
  LUNO+3 :    9  A 0.0000002


Computation Completed


Properties will be evaluated at   0.000000,   0.000000,   0.000000 [a0]

Properties computed using the SCF density matrix


 Multipole Moments:

 ------------------------------------------------------------------------------------
     Multipole            Electronic (a.u.)      Nuclear  (a.u.)        Total (a.u.)
 ------------------------------------------------------------------------------------

 L = 1.  Multiply by 2.5417464519 to convert [e a0] to [Debye]
 Dipole X            :         -0.0000238            0.0000187           -0.0000051
 Dipole Y            :          0.0000294           -0.0000207            0.0000087
 Dipole Z            :          0.0000297           -0.0000202            0.0000095
 Magnitude           :                                                    0.0000139

 ------------------------------------------------------------------------------------

*** tstop() called on iCarus at Mon Jul  7 22:34:26 2025
Module time:
	user time   =        nan seconds =        nan minutes
	system time =        nan seconds =        nan minutes
	total time  =          0 seconds =       0.00 minutes
Total time:
	user time   =        nan seconds =        nan minutes
	system time =        nan seconds =        nan minutes
	total time  =          3 seconds =       0.05 minutes

*** tstart() called on iCarus
*** at Mon Jul  7 22:34:26 2025


         ------------------------------------------------------------
                                   SCF GRAD                          
                          Rob Parrish, Justin Turney,                
                       Andy Simmonett, and Alex Sokolov              
         ------------------------------------------------------------

  ==> Geometry <==

    Molecular point group: c1
    Full point group: C1

    Geometry (in Angstrom), charge = 1, multiplicity = 2:

       Center              X                  Y                   Z               Mass       
    ------------   -----------------  -----------------  -----------------  -----------------
         C            0.000001980746    -0.000002193280    -0.000002139885    12.000000000000
         H           -0.627574874567     0.942742502890    -0.020001547843     1.007825032230
         H           -0.317419269552    -0.926684719478    -0.568776898670     1.007825032230
         H           -0.025695343252    -0.322887521772     1.085401677289     1.007825032230
         H            0.970687506625     0.306831931641    -0.496621090891     1.007825032230

  Nuclear repulsion =   12.934697663608981

  ==> Basis Set <==

  Basis Set: 6-31G(D)
    Blend: 6-31G(D)
    Number of shells: 14
    Number of basis functions: 23
    Number of Cartesian functions: 23
    Spherical Harmonics?: false
    Max angular momentum: 2

  ==> DFJKGrad: Density-Fitted SCF Gradients <==

    Gradient:                    1
    J tasked:                  Yes
    K tasked:                  Yes
    wK tasked:                  No
    OpenMP threads:             10
    Integrals threads:          10
    Memory [MiB]:             -422
    Schwarz Cutoff:          1E-12
    Fitting Condition:       1E-10

   => Auxiliary Basis Set <=

  Basis Set: (6-31G(D) AUX)
    Blend: CC-PVDZ-JKFIT
    Number of shells: 60
    Number of basis functions: 181
    Number of Cartesian functions: 181
    Spherical Harmonics?: false
    Max angular momentum: 3

  ==> DFT Potential <==

   => LibXC <=

    Version 7.0.0
    S. Lehtola, C. Steigemann, M. J.T. Oliveira, and M. A.L. Marques.,  SoftwareX 7, 1–5 (2018) (10.1016/j.softx.2017.11.002)

   => Composite Functional: B3LYP <= 

    B3LYP Hyb-GGA Exchange-Correlation Functional

    P. J. Stephens, F. J. Devlin, C. F. Chabalowski, and M. J. Frisch.,  J. Phys. Chem. 98, 11623 (1994) (10.1021/j100096a001)

    Deriv               =              1
    GGA                 =           TRUE
    Meta                =          FALSE

    Exchange Hybrid     =           TRUE
    MP2 Hybrid          =          FALSE

   => Exchange Functionals <=

    0.0800   Slater exchange
    0.7200         Becke 88

   => Exact (HF) Exchange <=

    0.2000               HF 

   => Correlation Functionals <=

    0.1900   Vosko, Wilk & Nusair (VWN5_RPA)
    0.8100   Lee, Yang & Parr

   => LibXC Density Thresholds  <==

    XC_HYB_GGA_XC_B3LYP:  1.00E-15 

   => Molecular Quadrature <=

    Radial Scheme          =       TREUTLER
    Pruning Scheme         =           NONE
    Nuclear Scheme         =       TREUTLER

    Blocking Scheme        =         OCTREE
    BS radius alpha        =              1
    Pruning alpha          =              1
    Radial Points          =             75
    Spherical Points       =            302
    Total Points           =         109384
    Total Blocks           =            873
    Max Points             =            256
    Max Functions          =             23
    Weights Tolerance      =       1.00E-15


  -Total Gradient:
     Atom            X                  Y                   Z
    ------   -----------------  -----------------  -----------------
       1        0.000000025074    -0.000000635063    -0.000000104576
       2       -0.016458688181    -0.011797725399    -0.015062140560
       3       -0.020476253804     0.012427169700    -0.007950264868
       4        0.024923320347     0.003765145556     0.001256455025
       5        0.012011596513    -0.004393954592     0.021756055042


*** tstop() called on iCarus at Mon Jul  7 22:34:26 2025
Module time:
	user time   =        nan seconds =        nan minutes
	system time =        nan seconds =        nan minutes
	total time  =          0 seconds =       0.00 minutes
Total time:
	user time   =        nan seconds =        nan minutes
	system time =        nan seconds =        nan minutes
	total time  =          3 seconds =       0.05 minutes

    			-----------------------------------------

    			 OPTKING 3.0: for geometry optimizations 

    			     By R.A. King, Bethel University     

    			        with contributions from          

    			    A.V. Copan, J. Cayton, A. Heide      

    			-----------------------------------------

    
	                            ===> Fragment 1 <== 

	 Z (Atomic Numbers)          Masses                          Geom                  
	       6.000000            12.000000          0.00000374 -0.00000414 -0.00000404   
	       1.000000             1.007825         -1.18594464  1.78152514 -0.03779745   
	       1.000000             1.007825         -0.59983549 -1.75118032 -1.07483256   
	       1.000000             1.007825         -0.04855716 -0.61016899  2.05111191   
	       1.000000             1.007825          1.83433354  0.57982832 -0.93847785   

	 - Coordinate -           - BOHR/RAD -       - ANG/DEG -
	 R(1,2)           =         2.140502           1.132705
	 R(1,3)           =         2.140486           1.132697
	 R(1,4)           =         2.140499           1.132703
	 R(1,5)           =         2.140492           1.132699
	 B(2,1,3)         =         2.113890         121.116992
	 B(2,1,4)         =         1.814812         103.981064
	 B(2,1,5)         =         1.814815         103.981234
	 B(3,1,4)         =         1.814816         103.981279
	 B(3,1,5)         =         1.814823         103.981719
	 B(4,1,5)         =         2.113894         121.117198
	 O(2,1,3,4)       =         0.876210          50.203134
	 O(2,1,3,5)       =        -0.876204         -50.202797
	 O(2,1,4,5)       =         1.056952          60.558893
	 O(3,1,2,4)       =        -0.876209         -50.203069
	 O(3,1,2,5)       =         0.876202          50.202653
	 O(3,1,4,5)       =        -1.056938         -60.558099
	 O(4,1,2,3)       =         1.056953          60.558944
	 O(4,1,2,5)       =        -0.876207         -50.202936
	 O(4,1,3,5)       =         0.876200          50.202543
	 O(5,1,2,3)       =        -1.056941         -60.558254
	 O(5,1,2,4)       =         0.876206          50.202885
	 O(5,1,3,4)       =        -0.876197         -50.202412


	        --- Internal Coordinate Step in ANG or DEG, aJ/ANG or AJ/DEG ---
	-------------------------------------------------------------------------------
	           Coordinate      Previous         Force          Change          New 
	           ----------      --------        ------          ------        ------
	               R(1,2)       1.13270        0.00358       -0.00072       1.13198
	               R(1,3)       1.13270        0.00360       -0.00072       1.13198
	               R(1,4)       1.13270        0.00358       -0.00072       1.13198
	               R(1,5)       1.13270        0.00359       -0.00072       1.13198
	             B(2,1,3)     121.11699        0.00139        9.28029     130.39728
	             B(2,1,4)     103.98106       -0.00061       -3.84710     100.13396
	             B(2,1,5)     103.98123       -0.00061       -3.84689     100.13435
	             B(3,1,4)     103.98128       -0.00061       -3.84680     100.13448
	             B(3,1,5)     103.98172       -0.00061       -3.84691     100.13481
	             B(4,1,5)     121.11720        0.00139        9.28019     130.39738
	           O(2,1,3,4)      50.20313       -0.00072       -5.59227      44.61086
	           O(2,1,3,5)     -50.20280        0.00072        5.59230     -44.61050
	           O(2,1,4,5)      60.55889        0.00069        4.64038      65.19927
	           O(3,1,2,4)     -50.20307        0.00072        5.59230     -44.61077
	           O(3,1,2,5)      50.20265       -0.00072       -5.59224      44.61041
	           O(3,1,4,5)     -60.55810       -0.00069       -4.63991     -65.19801
	           O(4,1,2,3)      60.55894        0.00069        4.64021      65.19916
	           O(4,1,2,5)     -50.20294        0.00072        5.59213     -44.61080
	           O(4,1,3,5)      50.20254       -0.00072       -5.59223      44.61031
	           O(5,1,2,3)     -60.55825       -0.00069       -4.63997     -65.19823
	           O(5,1,2,4)      50.20289       -0.00072       -5.59215      44.61074
	           O(5,1,3,4)     -50.20241        0.00072        5.59216     -44.61025
	-------------------------------------------------------------------------------

	                                 ==> Convergence Check <==                                  
    
	Measures of convergence in internal coordinates in au.
    
	Criteria marked as inactive (o), active & met (*), and active & unmet ( ).

	----------------------------------------------------------------------------------------------
	   Step    Total Energy     Delta E     Max Force     RMS Force      Max Disp      RMS Disp   
	----------------------------------------------------------------------------------------------
	  Convergence Criteria     1.00e-06 *    3.00e-04 *             o    1.20e-03 *             o
	----------------------------------------------------------------------------------------------
	     2     -40.03466125   -3.51e-02      1.82e-02      9.47e-03 o    1.62e-01      8.87e-02 o  ~
	----------------------------------------------------------------------------------------------

Next Geometry in Ang 
	Fragment 1 (Ang)

	    C   0.0000017195  -0.0000018194  -0.0000020448
	    H  -0.5645179820   0.9805566142   0.0347662557
	    H  -0.2414245906  -0.9668653942  -0.5369083794
	    H  -0.1160063742  -0.3348395445   1.0750844350
	    H   0.9219472273   0.3211501438  -0.5729402665


    Structure for next step:
    Molecular point group: c1
    Full point group: C1

    Geometry (in Angstrom), charge = 1, multiplicity = 2:

    C            0.000001719495    -0.000001819421    -0.000002044774
    H           -0.564517981980     0.980556614230     0.034766255685
    H           -0.241424590627    -0.966865394170    -0.536908379398
    H           -0.116006374229    -0.334839544471     1.075084434955
    H            0.921947227342     0.321150143833    -0.572940266467


Scratch directory: D:\BaiduSyncdisk\py\MS\psi4_scratch/
gradient() will perform analytic gradient computation.
   => Libint2 <=

    Primary   basis highest AM E, G, H:  6, 6, -
    Auxiliary basis highest AM E, G, H:  7, 7, -
    Onebody   basis highest AM E, G, H:  -, -, -
    Solid Harmonics ordering:            Gaussian

*** tstart() called on iCarus
*** at Mon Jul  7 22:34:26 2025

   => Loading Basis Set <=

    Name: 6-31G(D)
    Role: ORBITAL
    Keyword: BASIS
    atoms 1   entry C          line   111 file D:\anaconda3\envs\nistpy\Library\share\psi4\basis\6-31g_d_.gbs 
    atoms 2-5 entry H          line    44 file D:\anaconda3\envs\nistpy\Library\share\psi4\basis\6-31g_d_.gbs 


         ---------------------------------------------------------
                                   SCF
               by Justin Turney, Rob Parrish, Andy Simmonett
                          and Daniel G. A. Smith
                              UKS Reference
                       10 Threads,   -562 MiB Core
         ---------------------------------------------------------

  ==> Geometry <==

    Molecular point group: c1
    Full point group: C1

    Geometry (in Angstrom), charge = 1, multiplicity = 2:

       Center              X                  Y                   Z               Mass       
    ------------   -----------------  -----------------  -----------------  -----------------
         C            0.000001719495    -0.000001819421    -0.000002044774    12.000000000000
         H           -0.564517981980     0.980556614230     0.034766255685     1.007825032230
         H           -0.241424590627    -0.966865394170    -0.536908379398     1.007825032230
         H           -0.116006374229    -0.334839544471     1.075084434955     1.007825032230
         H            0.921947227342     0.321150143833    -0.572940266467     1.007825032230

  Running in c1 symmetry.

  Rotational constants: A =      5.55023  B =      5.55022  C =      3.96025 [cm^-1]
  Rotational constants: A = 166391.63617  B = 166391.55610  C = 118725.40480 [MHz]
  Nuclear repulsion =   12.953761010032981

  Charge       = 1
  Multiplicity = 2
  Electrons    = 9
  Nalpha       = 5
  Nbeta        = 4

  ==> Algorithm <==

  SCF Algorithm Type is DF.
  DIIS enabled.
  MOM disabled.
  Fractional occupation disabled.
  Guess Type is READ.
  Energy threshold   = 1.00e-08
  Density threshold  = 1.00e-08
  Integral threshold = 1.00e-12

  ==> Primary Basis <==

  Basis Set: 6-31G(D)
    Blend: 6-31G(D)
    Number of shells: 14
    Number of basis functions: 23
    Number of Cartesian functions: 23
    Spherical Harmonics?: false
    Max angular momentum: 2

  ==> DFT Potential <==

   => LibXC <=

    Version 7.0.0
    S. Lehtola, C. Steigemann, M. J.T. Oliveira, and M. A.L. Marques.,  SoftwareX 7, 1–5 (2018) (10.1016/j.softx.2017.11.002)

   => Composite Functional: B3LYP <= 

    B3LYP Hyb-GGA Exchange-Correlation Functional

    P. J. Stephens, F. J. Devlin, C. F. Chabalowski, and M. J. Frisch.,  J. Phys. Chem. 98, 11623 (1994) (10.1021/j100096a001)

    Deriv               =              1
    GGA                 =           TRUE
    Meta                =          FALSE

    Exchange Hybrid     =           TRUE
    MP2 Hybrid          =          FALSE

   => Exchange Functionals <=

    0.0800   Slater exchange
    0.7200         Becke 88

   => Exact (HF) Exchange <=

    0.2000               HF 

   => Correlation Functionals <=

    0.1900   Vosko, Wilk & Nusair (VWN5_RPA)
    0.8100   Lee, Yang & Parr

   => LibXC Density Thresholds  <==

    XC_HYB_GGA_XC_B3LYP:  1.00E-15 

   => Molecular Quadrature <=

    Radial Scheme          =       TREUTLER
    Pruning Scheme         =           NONE
    Nuclear Scheme         =       TREUTLER

    Blocking Scheme        =         OCTREE
    BS radius alpha        =              1
    Pruning alpha          =              1
    Radial Points          =             75
    Spherical Points       =            302
    Total Points           =         109408
    Total Blocks           =            859
    Max Points             =            256
    Max Functions          =             23
    Weights Tolerance      =       1.00E-15

   => Loading Basis Set <=

    Name: (6-31G(D) AUX)
    Role: JKFIT
    Keyword: DF_BASIS_SCF
    atoms 1   entry C          line   121 file D:\anaconda3\envs\nistpy\Library\share\psi4\basis\cc-pvdz-jkfit.gbs 
    atoms 2-5 entry H          line    51 file D:\anaconda3\envs\nistpy\Library\share\psi4\basis\cc-pvdz-jkfit.gbs 

   => Loading Basis Set <=

    Name: 6-31G(D)
    Role: ORBITAL
    Keyword: BASIS
    atoms 1   entry C          line   111 file D:\anaconda3\envs\nistpy\Library\share\psi4\basis\6-31g_d_.gbs 
    atoms 2-5 entry H          line    44 file D:\anaconda3\envs\nistpy\Library\share\psi4\basis\6-31g_d_.gbs 

  Reading orbitals from file D:\BaiduSyncdisk\py\MS\psi4_scratch/psi4_calculation.default.33932.180.npy, no projection.

  ==> Integral Setup <==

  DFHelper Memory: AOs need 0.001 GiB; user supplied 5.515 GiB. 
  Using in-core AOs.

  ==> MemDFJK: Density-Fitted J/K Matrices <==

    J tasked:                   Yes
    K tasked:                   Yes
    wK tasked:                   No
    OpenMP threads:              10
    Memory [MiB]:              5647
    Algorithm:                 Core
    Schwarz Cutoff:           1E-12
    Mask sparsity (%):       0.0000
    Fitting Condition:        1E-10

   => Auxiliary Basis Set <=

  Basis Set: (6-31G(D) AUX)
    Blend: CC-PVDZ-JKFIT
    Number of shells: 60
    Number of basis functions: 181
    Number of Cartesian functions: 181
    Spherical Harmonics?: false
    Max angular momentum: 3

  Cached 100.0% of DFT collocation blocks in 0.073 [GiB].

  Minimum eigenvalue in the overlap matrix is 2.2505125767E-02.
  Reciprocal condition number of the overlap matrix is 4.1128191471E-03.
    Using symmetric orthogonalization.

  ==> Pre-Iterations <==

  SCF Guess: Orbitals guess was supplied from a previous computation.

   -------------------------------------------------------
    Irrep   Nso     Nmo     Nalpha   Nbeta   Ndocc  Nsocc
   -------------------------------------------------------
     A         23      23       5       4       4       1
   -------------------------------------------------------
    Total      23      23       5       4       4       1
   -------------------------------------------------------

  ==> Iterations <==

                           Total Energy        Delta E     RMS |[F,P]|

   @DF-UKS iter   0:   -40.06603078713666   -4.00660e+01   1.25754e-03 
   @DF-UKS iter   1:   -40.04756491270003    1.84659e-02   2.48569e-04 DIIS/ADIIS
   @DF-UKS iter   2:   -40.04757442081075   -9.50811e-06   2.25680e-04 DIIS/ADIIS
   @DF-UKS iter   3:   -40.04758828586380   -1.38651e-05   1.74551e-05 DIIS
   @DF-UKS iter   4:   -40.04758849316831   -2.07305e-07   4.92001e-06 DIIS
   @DF-UKS iter   5:   -40.04758851509459   -2.19263e-08   6.38493e-07 DIIS
   @DF-UKS iter   6:   -40.04758851542127   -3.26679e-10   4.05627e-08 DIIS
   @DF-UKS iter   7:   -40.04758851542215   -8.81073e-13   3.12801e-09 DIIS
  Energy and wave function converged.


  ==> Post-Iterations <==

   Electrons on quadrature grid:
      Nalpha   =    4.9999989150 ; deviation = -1.085e-06
      Nbeta    =    3.9999990725 ; deviation = -9.275e-07
      Ntotal   =    8.9999979874 ; deviation = -2.013e-06 

   @Spin Contamination Metric:   1.408437794E-03
   @S^2 Expected:                7.500000000E-01
   @S^2 Observed:                7.514084378E-01
   @S   Expected:                5.000000000E-01
   @S   Observed:                5.000000000E-01

    Orbital Energies [Eh]
    ---------------------

    Alpha Occupied:                                                       

       1A    -10.572453     2A     -1.068588     3A     -0.780344  
       4A     -0.780343     5A     -0.709182  

    Alpha Virtual:                                                        

       6A     -0.215541     7A     -0.184425     8A     -0.114262  
       9A     -0.114261    10A      0.189438    11A      0.264249  
      12A      0.264249    13A      0.478048    14A      0.507503  
      15A      0.556396    16A      0.556396    17A      0.680305  
      18A      1.263072    19A      1.411836    20A      1.687132  
      21A      1.687132    22A      1.916633    23A      3.741515  

    Beta Occupied:                                                        

       1A    -10.563444     2A     -1.024283     3A     -0.754694  
       4A     -0.754694  

    Beta Virtual:                                                         

       5A     -0.565085     6A     -0.181771     7A     -0.167170  
       8A     -0.100415     9A     -0.100414    10A      0.229854  
      11A      0.276194    12A      0.276194    13A      0.508844  
      14A      0.549272    15A      0.581244    16A      0.581244  
      17A      0.698158    18A      1.267543    19A      1.482050  
      20A      1.734513    21A      1.734514    22A      1.928388  
      23A      3.767576  

    Final Occupation by Irrep:
              A 
    DOCC [     4 ]
    SOCC [     1 ]
    NA   [     5 ]
    NB   [     4 ]

  @DF-UKS Final Energy:   -40.04758851542215

   => Energetics <=

    Nuclear Repulsion Energy =             12.9537610100329807
    One-Electron Energy =                 -75.2042022228990419
    Two-Electron Energy =                  27.4673796949274767
    DFT Exchange-Correlation Energy =      -5.2645269974835660
    Empirical Dispersion Energy =           0.0000000000000000
    VV10 Nonlocal Energy =                  0.0000000000000000
    Total Energy =                        -40.0475885154221558

  UHF NO Occupations:
  HONO-2 :    3  A 1.9997907
  HONO-1 :    4  A 1.9997145
  HONO-0 :    5  A 1.0000000
  LUNO+0 :    6  A 0.0002855
  LUNO+1 :    7  A 0.0002093
  LUNO+2 :    8  A 0.0002093
  LUNO+3 :    9  A 0.0000002


Computation Completed


Properties will be evaluated at   0.000000,   0.000000,   0.000000 [a0]

Properties computed using the SCF density matrix


 Multipole Moments:

 ------------------------------------------------------------------------------------
     Multipole            Electronic (a.u.)      Nuclear  (a.u.)        Total (a.u.)
 ------------------------------------------------------------------------------------

 L = 1.  Multiply by 2.5417464519 to convert [e a0] to [Debye]
 Dipole X            :         -0.0000198            0.0000162           -0.0000036
 Dipole Y            :          0.0000218           -0.0000172            0.0000046
 Dipole Z            :          0.0000250           -0.0000193            0.0000057
 Magnitude           :                                                    0.0000081

 ------------------------------------------------------------------------------------

*** tstop() called on iCarus at Mon Jul  7 22:34:27 2025
Module time:
	user time   =        nan seconds =        nan minutes
	system time =        nan seconds =        nan minutes
	total time  =          1 seconds =       0.02 minutes
Total time:
	user time   =        nan seconds =        nan minutes
	system time =        nan seconds =        nan minutes
	total time  =          4 seconds =       0.07 minutes

*** tstart() called on iCarus
*** at Mon Jul  7 22:34:27 2025


         ------------------------------------------------------------
                                   SCF GRAD                          
                          Rob Parrish, Justin Turney,                
                       Andy Simmonett, and Alex Sokolov              
         ------------------------------------------------------------

  ==> Geometry <==

    Molecular point group: c1
    Full point group: C1

    Geometry (in Angstrom), charge = 1, multiplicity = 2:

       Center              X                  Y                   Z               Mass       
    ------------   -----------------  -----------------  -----------------  -----------------
         C            0.000001719495    -0.000001819421    -0.000002044774    12.000000000000
         H           -0.564517981980     0.980556614230     0.034766255685     1.007825032230
         H           -0.241424590627    -0.966865394170    -0.536908379398     1.007825032230
         H           -0.116006374229    -0.334839544471     1.075084434955     1.007825032230
         H            0.921947227342     0.321150143833    -0.572940266467     1.007825032230

  Nuclear repulsion =   12.953761010032981

  ==> Basis Set <==

  Basis Set: 6-31G(D)
    Blend: 6-31G(D)
    Number of shells: 14
    Number of basis functions: 23
    Number of Cartesian functions: 23
    Spherical Harmonics?: false
    Max angular momentum: 2

  ==> DFJKGrad: Density-Fitted SCF Gradients <==

    Gradient:                    1
    J tasked:                  Yes
    K tasked:                  Yes
    wK tasked:                  No
    OpenMP threads:             10
    Integrals threads:          10
    Memory [MiB]:             -422
    Schwarz Cutoff:          1E-12
    Fitting Condition:       1E-10

   => Auxiliary Basis Set <=

  Basis Set: (6-31G(D) AUX)
    Blend: CC-PVDZ-JKFIT
    Number of shells: 60
    Number of basis functions: 181
    Number of Cartesian functions: 181
    Spherical Harmonics?: false
    Max angular momentum: 3

  ==> DFT Potential <==

   => LibXC <=

    Version 7.0.0
    S. Lehtola, C. Steigemann, M. J.T. Oliveira, and M. A.L. Marques.,  SoftwareX 7, 1–5 (2018) (10.1016/j.softx.2017.11.002)

   => Composite Functional: B3LYP <= 

    B3LYP Hyb-GGA Exchange-Correlation Functional

    P. J. Stephens, F. J. Devlin, C. F. Chabalowski, and M. J. Frisch.,  J. Phys. Chem. 98, 11623 (1994) (10.1021/j100096a001)

    Deriv               =              1
    GGA                 =           TRUE
    Meta                =          FALSE

    Exchange Hybrid     =           TRUE
    MP2 Hybrid          =          FALSE

   => Exchange Functionals <=

    0.0800   Slater exchange
    0.7200         Becke 88

   => Exact (HF) Exchange <=

    0.2000               HF 

   => Correlation Functionals <=

    0.1900   Vosko, Wilk & Nusair (VWN5_RPA)
    0.8100   Lee, Yang & Parr

   => LibXC Density Thresholds  <==

    XC_HYB_GGA_XC_B3LYP:  1.00E-15 

   => Molecular Quadrature <=

    Radial Scheme          =       TREUTLER
    Pruning Scheme         =           NONE
    Nuclear Scheme         =       TREUTLER

    Blocking Scheme        =         OCTREE
    BS radius alpha        =              1
    Pruning alpha          =              1
    Radial Points          =             75
    Spherical Points       =            302
    Total Points           =         109408
    Total Blocks           =            859
    Max Points             =            256
    Max Functions          =             23
    Weights Tolerance      =       1.00E-15


  -Total Gradient:
     Atom            X                  Y                   Z
    ------   -----------------  -----------------  -----------------
       1        0.000000358612    -0.000000192405    -0.000000203127
       2       -0.009760064786    -0.002710920415    -0.007226631552
       3       -0.010717583026     0.003059210921    -0.005531959633
       4        0.011776468021     0.000798240361     0.003937929145
       5        0.008700821108    -0.001146338389     0.008820865240


*** tstop() called on iCarus at Mon Jul  7 22:34:27 2025
Module time:
	user time   =        nan seconds =        nan minutes
	system time =        nan seconds =        nan minutes
	total time  =          0 seconds =       0.00 minutes
Total time:
	user time   =        nan seconds =        nan minutes
	system time =        nan seconds =        nan minutes
	total time  =          4 seconds =       0.07 minutes

    			-----------------------------------------

    			 OPTKING 3.0: for geometry optimizations 

    			     By R.A. King, Bethel University     

    			        with contributions from          

    			    A.V. Copan, J. Cayton, A. Heide      

    			-----------------------------------------

    
	                            ===> Fragment 1 <== 

	 Z (Atomic Numbers)          Masses                          Geom                  
	       6.000000            12.000000          0.00000325 -0.00000344 -0.00000386   
	       1.000000             1.007825         -1.06678438  1.85298345  0.06569870   
	       1.000000             1.007825         -0.45622636 -1.82711080 -1.01460979   
	       1.000000             1.007825         -0.21922028 -0.63275504  2.03161514   
	       1.000000             1.007825          1.74222776  0.60688582 -1.08270019   

	 - Coordinate -           - BOHR/RAD -       - ANG/DEG -
	 R(1,2)           =         2.139138           1.131983
	 R(1,3)           =         2.139133           1.131981
	 R(1,4)           =         2.139138           1.131983
	 R(1,5)           =         2.139134           1.131981
	 B(2,1,3)         =         2.275862         130.397281
	 B(2,1,4)         =         1.747667         100.133964
	 B(2,1,5)         =         1.747674         100.134349
	 B(3,1,4)         =         1.747676         100.134476
	 B(3,1,5)         =         1.747682         100.134811
	 B(4,1,5)         =         2.275864         130.397383
	 O(2,1,3,4)       =         0.778606          44.610861
	 O(2,1,3,5)       =        -0.778600         -44.610496
	 O(2,1,4,5)       =         1.137942          65.199270
	 O(3,1,2,4)       =        -0.778605         -44.610770
	 O(3,1,2,5)       =         0.778599          44.610414
	 O(3,1,4,5)       =        -1.137920         -65.198010
	 O(4,1,2,3)       =         1.137940          65.199157
	 O(4,1,2,5)       =        -0.778605         -44.610804
	 O(4,1,3,5)       =         0.778597          44.610311
	 O(5,1,2,3)       =        -1.137924         -65.198227
	 O(5,1,2,4)       =         0.778604          44.610736
	 O(5,1,3,4)       =        -0.778596         -44.610252


	        --- Internal Coordinate Step in ANG or DEG, aJ/ANG or AJ/DEG ---
	-------------------------------------------------------------------------------
	           Coordinate      Previous         Force          Change          New 
	           ----------      --------        ------          ------        ------
	               R(1,2)       1.13198       -0.01893       -0.00536       1.12662
	               R(1,3)       1.13198       -0.01892       -0.00536       1.12662
	               R(1,4)       1.13198       -0.01892       -0.00536       1.12662
	               R(1,5)       1.13198       -0.01892       -0.00536       1.12662
	             B(2,1,3)     130.39728        0.00055        5.74704     136.14432
	             B(2,1,4)     100.13396       -0.00021       -2.11801      98.01595
	             B(2,1,5)     100.13435       -0.00021       -2.11800      98.01635
	             B(3,1,4)     100.13448       -0.00021       -2.11801      98.01647
	             B(3,1,5)     100.13481       -0.00021       -2.11805      98.01676
	             B(4,1,5)     130.39738        0.00055        5.74699     136.14437
	           O(2,1,3,4)      44.61086       -0.00037       -4.13969      40.47117
	           O(2,1,3,5)     -44.61050        0.00037        4.13963     -40.47086
	           O(2,1,4,5)      65.19927        0.00027        2.87355      68.07282
	           O(3,1,2,4)     -44.61077        0.00037        4.13967     -40.47110
	           O(3,1,2,5)      44.61041       -0.00037       -4.13960      40.47082
	           O(3,1,4,5)     -65.19801       -0.00027       -2.87349     -68.07150
	           O(4,1,2,3)      65.19916        0.00027        2.87352      68.07267
	           O(4,1,2,5)     -44.61080        0.00037        4.13964     -40.47116
	           O(4,1,3,5)      44.61031       -0.00037       -4.13955      40.47076
	           O(5,1,2,3)     -65.19823       -0.00027       -2.87347     -68.07170
	           O(5,1,2,4)      44.61074       -0.00037       -4.13962      40.47111
	           O(5,1,3,4)     -44.61025        0.00037        4.13953     -40.47072
	-------------------------------------------------------------------------------

	                                 ==> Convergence Check <==                                  
    
	Measures of convergence in internal coordinates in au.
    
	Criteria marked as inactive (o), active & met (*), and active & unmet ( ).

	----------------------------------------------------------------------------------------------
	   Step    Total Energy     Delta E     Max Force     RMS Force      Max Disp      RMS Disp   
	----------------------------------------------------------------------------------------------
	  Convergence Criteria     1.00e-06 *    3.00e-04 *             o    1.20e-03 *             o
	----------------------------------------------------------------------------------------------
	     3     -40.04758852   -1.29e-02      7.17e-03      4.24e-03 o    1.00e-01      5.95e-02 o  ~
	----------------------------------------------------------------------------------------------

Next Geometry in Ang 
	Fragment 1 (Ang)

	    C   0.0000012949  -0.0000013822  -0.0000017242
	    H  -0.5213519376   0.9963950867   0.0682576960
	    H  -0.1927447097  -0.9842655098  -0.5131750770
	    H  -0.1707871659  -0.3396569403   1.0605358214
	    H   0.8848825184   0.3275287456  -0.6156167162


    Structure for next step:
    Molecular point group: c1
    Full point group: C1

    Geometry (in Angstrom), charge = 1, multiplicity = 2:

    C            0.000001294877    -0.000001382223    -0.000001724204
    H           -0.521351937629     0.996395086673     0.068257695981
    H           -0.192744709744    -0.984265509789    -0.513175076973
    H           -0.170787165915    -0.339656940267     1.060535821440
    H            0.884882518410     0.327528745606    -0.615616716244


Scratch directory: D:\BaiduSyncdisk\py\MS\psi4_scratch/
gradient() will perform analytic gradient computation.
   => Libint2 <=

    Primary   basis highest AM E, G, H:  6, 6, -
    Auxiliary basis highest AM E, G, H:  7, 7, -
    Onebody   basis highest AM E, G, H:  -, -, -
    Solid Harmonics ordering:            Gaussian

*** tstart() called on iCarus
*** at Mon Jul  7 22:34:27 2025

   => Loading Basis Set <=

    Name: 6-31G(D)
    Role: ORBITAL
    Keyword: BASIS
    atoms 1   entry C          line   111 file D:\anaconda3\envs\nistpy\Library\share\psi4\basis\6-31g_d_.gbs 
    atoms 2-5 entry H          line    44 file D:\anaconda3\envs\nistpy\Library\share\psi4\basis\6-31g_d_.gbs 


         ---------------------------------------------------------
                                   SCF
               by Justin Turney, Rob Parrish, Andy Simmonett
                          and Daniel G. A. Smith
                              UKS Reference
                       10 Threads,   -562 MiB Core
         ---------------------------------------------------------

  ==> Geometry <==

    Molecular point group: c1
    Full point group: C1

    Geometry (in Angstrom), charge = 1, multiplicity = 2:

       Center              X                  Y                   Z               Mass       
    ------------   -----------------  -----------------  -----------------  -----------------
         C            0.000001294877    -0.000001382223    -0.000001724204    12.000000000000
         H           -0.521351937629     0.996395086673     0.068257695981     1.007825032230
         H           -0.192744709744    -0.984265509789    -0.513175076973     1.007825032230
         H           -0.170787165915    -0.339656940267     1.060535821440     1.007825032230
         H            0.884882518410     0.327528745606    -0.615616716244     1.007825032230

  Running in c1 symmetry.

  Rotational constants: A =      5.78267  B =      5.78267  C =      3.82845 [cm^-1]
  Rotational constants: A = 173360.14994  B = 173360.01035  C = 114774.06528 [MHz]
  Nuclear repulsion =   13.023780310167689

  Charge       = 1
  Multiplicity = 2
  Electrons    = 9
  Nalpha       = 5
  Nbeta        = 4

  ==> Algorithm <==

  SCF Algorithm Type is DF.
  DIIS enabled.
  MOM disabled.
  Fractional occupation disabled.
  Guess Type is READ.
  Energy threshold   = 1.00e-08
  Density threshold  = 1.00e-08
  Integral threshold = 1.00e-12

  ==> Primary Basis <==

  Basis Set: 6-31G(D)
    Blend: 6-31G(D)
    Number of shells: 14
    Number of basis functions: 23
    Number of Cartesian functions: 23
    Spherical Harmonics?: false
    Max angular momentum: 2

  ==> DFT Potential <==

   => LibXC <=

    Version 7.0.0
    S. Lehtola, C. Steigemann, M. J.T. Oliveira, and M. A.L. Marques.,  SoftwareX 7, 1–5 (2018) (10.1016/j.softx.2017.11.002)

   => Composite Functional: B3LYP <= 

    B3LYP Hyb-GGA Exchange-Correlation Functional

    P. J. Stephens, F. J. Devlin, C. F. Chabalowski, and M. J. Frisch.,  J. Phys. Chem. 98, 11623 (1994) (10.1021/j100096a001)

    Deriv               =              1
    GGA                 =           TRUE
    Meta                =          FALSE

    Exchange Hybrid     =           TRUE
    MP2 Hybrid          =          FALSE

   => Exchange Functionals <=

    0.0800   Slater exchange
    0.7200         Becke 88

   => Exact (HF) Exchange <=

    0.2000               HF 

   => Correlation Functionals <=

    0.1900   Vosko, Wilk & Nusair (VWN5_RPA)
    0.8100   Lee, Yang & Parr

   => LibXC Density Thresholds  <==

    XC_HYB_GGA_XC_B3LYP:  1.00E-15 

   => Molecular Quadrature <=

    Radial Scheme          =       TREUTLER
    Pruning Scheme         =           NONE
    Nuclear Scheme         =       TREUTLER

    Blocking Scheme        =         OCTREE
    BS radius alpha        =              1
    Pruning alpha          =              1
    Radial Points          =             75
    Spherical Points       =            302
    Total Points           =         109384
    Total Blocks           =            865
    Max Points             =            256
    Max Functions          =             23
    Weights Tolerance      =       1.00E-15

   => Loading Basis Set <=

    Name: (6-31G(D) AUX)
    Role: JKFIT
    Keyword: DF_BASIS_SCF
    atoms 1   entry C          line   121 file D:\anaconda3\envs\nistpy\Library\share\psi4\basis\cc-pvdz-jkfit.gbs 
    atoms 2-5 entry H          line    51 file D:\anaconda3\envs\nistpy\Library\share\psi4\basis\cc-pvdz-jkfit.gbs 

   => Loading Basis Set <=

    Name: 6-31G(D)
    Role: ORBITAL
    Keyword: BASIS
    atoms 1   entry C          line   111 file D:\anaconda3\envs\nistpy\Library\share\psi4\basis\6-31g_d_.gbs 
    atoms 2-5 entry H          line    44 file D:\anaconda3\envs\nistpy\Library\share\psi4\basis\6-31g_d_.gbs 

  Reading orbitals from file D:\BaiduSyncdisk\py\MS\psi4_scratch/psi4_calculation.default.33932.180.npy, no projection.

  ==> Integral Setup <==

  DFHelper Memory: AOs need 0.001 GiB; user supplied 5.515 GiB. 
  Using in-core AOs.

  ==> MemDFJK: Density-Fitted J/K Matrices <==

    J tasked:                   Yes
    K tasked:                   Yes
    wK tasked:                   No
    OpenMP threads:              10
    Memory [MiB]:              5647
    Algorithm:                 Core
    Schwarz Cutoff:           1E-12
    Mask sparsity (%):       0.0000
    Fitting Condition:        1E-10

   => Auxiliary Basis Set <=

  Basis Set: (6-31G(D) AUX)
    Blend: CC-PVDZ-JKFIT
    Number of shells: 60
    Number of basis functions: 181
    Number of Cartesian functions: 181
    Spherical Harmonics?: false
    Max angular momentum: 3

  Cached 100.0% of DFT collocation blocks in 0.073 [GiB].

  Minimum eigenvalue in the overlap matrix is 2.3243603440E-02.
  Reciprocal condition number of the overlap matrix is 4.2280180400E-03.
    Using symmetric orthogonalization.

  ==> Pre-Iterations <==

  SCF Guess: Orbitals guess was supplied from a previous computation.

   -------------------------------------------------------
    Irrep   Nso     Nmo     Nalpha   Nbeta   Ndocc  Nsocc
   -------------------------------------------------------
     A         23      23       5       4       4       1
   -------------------------------------------------------
    Total      23      23       5       4       4       1
   -------------------------------------------------------

  ==> Iterations <==

                           Total Energy        Delta E     RMS |[F,P]|

   @DF-UKS iter   0:   -40.06534005477875   -4.00653e+01   8.65924e-04 
   @DF-UKS iter   1:   -40.05129053275760    1.40495e-02   1.10545e-04 DIIS/ADIIS
   @DF-UKS iter   2:   -40.05129588370593   -5.35095e-06   2.66005e-05 DIIS
   @DF-UKS iter   3:   -40.05129622341662   -3.39711e-07   1.75186e-05 DIIS
   @DF-UKS iter   4:   -40.05129632530144   -1.01885e-07   5.46918e-06 DIIS
   @DF-UKS iter   5:   -40.05129634219570   -1.68943e-08   4.70333e-07 DIIS
   @DF-UKS iter   6:   -40.05129634235013   -1.54436e-10   3.08079e-08 DIIS
   @DF-UKS iter   7:   -40.05129634235070   -5.61329e-13   2.27355e-09 DIIS
  Energy and wave function converged.


  ==> Post-Iterations <==

   Electrons on quadrature grid:
      Nalpha   =    5.0000011395 ; deviation = 1.140e-06
      Nbeta    =    4.0000002822 ; deviation = 2.822e-07
      Ntotal   =    9.0000014217 ; deviation = 1.422e-06 

   @Spin Contamination Metric:   1.220611876E-03
   @S^2 Expected:                7.500000000E-01
   @S^2 Observed:                7.512206119E-01
   @S   Expected:                5.000000000E-01
   @S   Observed:                5.000000000E-01

    Orbital Energies [Eh]
    ---------------------

    Alpha Occupied:                                                       

       1A    -10.570633     2A     -1.071326     3A     -0.787885  
       4A     -0.787885     5A     -0.690239  

    Alpha Virtual:                                                        

       6A     -0.234879     7A     -0.185971     8A     -0.105560  
       9A     -0.105560    10A      0.190812    11A      0.283239  
      12A      0.283239    13A      0.466567    14A      0.495458  
      15A      0.566966    16A      0.566966    17A      0.658202  
      18A      1.255695    19A      1.481932    20A      1.643724  
      21A      1.643724    22A      1.958666    23A      3.734958  

    Beta Occupied:                                                        

       1A    -10.561199     2A     -1.027832     3A     -0.762961  
       4A     -0.762960  

    Beta Virtual:                                                         

       5A     -0.545664     6A     -0.201424     7A     -0.168208  
       8A     -0.092530     9A     -0.092530    10A      0.233590  
      11A      0.295424    12A      0.295424    13A      0.494792  
      14A      0.537098    15A      0.591512    16A      0.591512  
      17A      0.677978    18A      1.260863    19A      1.552438  
      20A      1.691917    21A      1.691917    22A      1.971183  
      23A      3.762036  

    Final Occupation by Irrep:
              A 
    DOCC [     4 ]
    SOCC [     1 ]
    NA   [     5 ]
    NB   [     4 ]

  @DF-UKS Final Energy:   -40.05129634235070

   => Energetics <=

    Nuclear Repulsion Energy =             13.0237803101676892
    One-Electron Energy =                 -75.3332991463313277
    Two-Electron Energy =                  27.5279322370739905
    DFT Exchange-Correlation Energy =      -5.2697097432610560
    Empirical Dispersion Energy =           0.0000000000000000
    VV10 Nonlocal Energy =                  0.0000000000000000
    Total Energy =                        -40.0512963423507031

  UHF NO Occupations:
  HONO-2 :    3  A 1.9998453
  HONO-1 :    4  A 1.9996992
  HONO-0 :    5  A 1.0000000
  LUNO+0 :    6  A 0.0003008
  LUNO+1 :    7  A 0.0001547
  LUNO+2 :    8  A 0.0001547
  LUNO+3 :    9  A 0.0000002


Computation Completed


Properties will be evaluated at   0.000000,   0.000000,   0.000000 [a0]

Properties computed using the SCF density matrix


 Multipole Moments:

 ------------------------------------------------------------------------------------
     Multipole            Electronic (a.u.)      Nuclear  (a.u.)        Total (a.u.)
 ------------------------------------------------------------------------------------

 L = 1.  Multiply by 2.5417464519 to convert [e a0] to [Debye]
 Dipole X            :         -0.0000148            0.0000122           -0.0000026
 Dipole Y            :          0.0000161           -0.0000131            0.0000030
 Dipole Z            :          0.0000203           -0.0000163            0.0000040
 Magnitude           :                                                    0.0000057

 ------------------------------------------------------------------------------------

*** tstop() called on iCarus at Mon Jul  7 22:34:27 2025
Module time:
	user time   =        nan seconds =        nan minutes
	system time =        nan seconds =        nan minutes
	total time  =          0 seconds =       0.00 minutes
Total time:
	user time   =        nan seconds =        nan minutes
	system time =        nan seconds =        nan minutes
	total time  =          4 seconds =       0.07 minutes

*** tstart() called on iCarus
*** at Mon Jul  7 22:34:27 2025


         ------------------------------------------------------------
                                   SCF GRAD                          
                          Rob Parrish, Justin Turney,                
                       Andy Simmonett, and Alex Sokolov              
         ------------------------------------------------------------

  ==> Geometry <==

    Molecular point group: c1
    Full point group: C1

    Geometry (in Angstrom), charge = 1, multiplicity = 2:

       Center              X                  Y                   Z               Mass       
    ------------   -----------------  -----------------  -----------------  -----------------
         C            0.000001294877    -0.000001382223    -0.000001724204    12.000000000000
         H           -0.521351937629     0.996395086673     0.068257695981     1.007825032230
         H           -0.192744709744    -0.984265509789    -0.513175076973     1.007825032230
         H           -0.170787165915    -0.339656940267     1.060535821440     1.007825032230
         H            0.884882518410     0.327528745606    -0.615616716244     1.007825032230

  Nuclear repulsion =   13.023780310167689

  ==> Basis Set <==

  Basis Set: 6-31G(D)
    Blend: 6-31G(D)
    Number of shells: 14
    Number of basis functions: 23
    Number of Cartesian functions: 23
    Spherical Harmonics?: false
    Max angular momentum: 2

  ==> DFJKGrad: Density-Fitted SCF Gradients <==

    Gradient:                    1
    J tasked:                  Yes
    K tasked:                  Yes
    wK tasked:                  No
    OpenMP threads:             10
    Integrals threads:          10
    Memory [MiB]:             -422
    Schwarz Cutoff:          1E-12
    Fitting Condition:       1E-10

   => Auxiliary Basis Set <=

  Basis Set: (6-31G(D) AUX)
    Blend: CC-PVDZ-JKFIT
    Number of shells: 60
    Number of basis functions: 181
    Number of Cartesian functions: 181
    Spherical Harmonics?: false
    Max angular momentum: 3

  ==> DFT Potential <==

   => LibXC <=

    Version 7.0.0
    S. Lehtola, C. Steigemann, M. J.T. Oliveira, and M. A.L. Marques.,  SoftwareX 7, 1–5 (2018) (10.1016/j.softx.2017.11.002)

   => Composite Functional: B3LYP <= 

    B3LYP Hyb-GGA Exchange-Correlation Functional

    P. J. Stephens, F. J. Devlin, C. F. Chabalowski, and M. J. Frisch.,  J. Phys. Chem. 98, 11623 (1994) (10.1021/j100096a001)

    Deriv               =              1
    GGA                 =           TRUE
    Meta                =          FALSE

    Exchange Hybrid     =           TRUE
    MP2 Hybrid          =          FALSE

   => Exchange Functionals <=

    0.0800   Slater exchange
    0.7200         Becke 88

   => Exact (HF) Exchange <=

    0.2000               HF 

   => Correlation Functionals <=

    0.1900   Vosko, Wilk & Nusair (VWN5_RPA)
    0.8100   Lee, Yang & Parr

   => LibXC Density Thresholds  <==

    XC_HYB_GGA_XC_B3LYP:  1.00E-15 

   => Molecular Quadrature <=

    Radial Scheme          =       TREUTLER
    Pruning Scheme         =           NONE
    Nuclear Scheme         =       TREUTLER

    Blocking Scheme        =         OCTREE
    BS radius alpha        =              1
    Pruning alpha          =              1
    Radial Points          =             75
    Spherical Points       =            302
    Total Points           =         109384
    Total Blocks           =            865
    Max Points             =            256
    Max Functions          =             23
    Weights Tolerance      =       1.00E-15


  -Total Gradient:
     Atom            X                  Y                   Z
    ------   -----------------  -----------------  -----------------
       1        0.000000111462     0.000000255025     0.000000096223
       2       -0.003838260624    -0.001065989581    -0.002841823379
       3       -0.004214943076     0.001202370642    -0.002175803219
       4        0.004631076052     0.000313995199     0.001548889892
       5        0.003422016177    -0.000450631163     0.003468640518


*** tstop() called on iCarus at Mon Jul  7 22:34:27 2025
Module time:
	user time   =        nan seconds =        nan minutes
	system time =        nan seconds =        nan minutes
	total time  =          0 seconds =       0.00 minutes
Total time:
	user time   =        nan seconds =        nan minutes
	system time =        nan seconds =        nan minutes
	total time  =          4 seconds =       0.07 minutes

    			-----------------------------------------

    			 OPTKING 3.0: for geometry optimizations 

    			     By R.A. King, Bethel University     

    			        with contributions from          

    			    A.V. Copan, J. Cayton, A. Heide      

    			-----------------------------------------

    
	                            ===> Fragment 1 <== 

	 Z (Atomic Numbers)          Masses                          Geom                  
	       6.000000            12.000000          0.00000245 -0.00000261 -0.00000326   
	       1.000000             1.007825         -0.98521238  1.88291383  0.12898835   
	       1.000000             1.007825         -0.36423471 -1.85999225 -0.96976035   
	       1.000000             1.007825         -0.32274097 -0.64185859  2.00412225   
	       1.000000             1.007825          1.67218561  0.61893963 -1.16334699   

	 - Coordinate -           - BOHR/RAD -       - ANG/DEG -
	 R(1,2)           =         2.129005           1.126621
	 R(1,3)           =         2.129004           1.126621
	 R(1,4)           =         2.129005           1.126621
	 R(1,5)           =         2.129003           1.126620
	 B(2,1,3)         =         2.376167         136.144324
	 B(2,1,4)         =         1.710701          98.015952
	 B(2,1,5)         =         1.710708          98.016347
	 B(3,1,4)         =         1.710710          98.016466
	 B(3,1,5)         =         1.710715          98.016756
	 B(4,1,5)         =         2.376168         136.144370
	 O(2,1,3,4)       =         0.706355          40.471166
	 O(2,1,3,5)       =        -0.706350         -40.470865
	 O(2,1,4,5)       =         1.188095          68.072821
	 O(3,1,2,4)       =        -0.706354         -40.471105
	 O(3,1,2,5)       =         0.706349          40.470816
	 O(3,1,4,5)       =        -1.188072         -68.071502
	 O(4,1,2,3)       =         1.188092          68.072675
	 O(4,1,2,5)       =        -0.706355         -40.471162
	 O(4,1,3,5)       =         0.706348          40.470758
	 O(5,1,2,3)       =        -1.188075         -68.071695
	 O(5,1,2,4)       =         0.706354          40.471114
	 O(5,1,3,4)       =        -0.706347         -40.470723


	        --- Internal Coordinate Step in ANG or DEG, aJ/ANG or AJ/DEG ---
	-------------------------------------------------------------------------------
	           Coordinate      Previous         Force          Change          New 
	           ----------      --------        ------          ------        ------
	               R(1,2)       1.12662       -0.00545       -0.00220       1.12442
	               R(1,3)       1.12662       -0.00545       -0.00220       1.12442
	               R(1,4)       1.12662       -0.00545       -0.00220       1.12442
	               R(1,5)       1.12662       -0.00545       -0.00220       1.12442
	             B(2,1,3)     136.14432        0.00019        2.57141     138.71573
	             B(2,1,4)      98.01595       -0.00007       -0.87729      97.13866
	             B(2,1,5)      98.01635       -0.00007       -0.87736      97.13898
	             B(3,1,4)      98.01647       -0.00007       -0.87739      97.13908
	             B(3,1,5)      98.01676       -0.00007       -0.87739      97.13936
	             B(4,1,5)     136.14437        0.00019        2.57138     138.71575
	           O(2,1,3,4)      40.47117       -0.00015       -1.98959      38.48157
	           O(2,1,3,5)     -40.47086        0.00015        1.98954     -38.48133
	           O(2,1,4,5)      68.07282        0.00010        1.28564      69.35846
	           O(3,1,2,4)     -40.47110        0.00015        1.98957     -38.48153
	           O(3,1,2,5)      40.47082       -0.00015       -1.98953      38.48129
	           O(3,1,4,5)     -68.07150       -0.00010       -1.28577     -69.35727
	           O(4,1,2,3)      68.07267        0.00010        1.28566      69.35833
	           O(4,1,2,5)     -40.47116        0.00015        1.98958     -38.48159
	           O(4,1,3,5)      40.47076       -0.00015       -1.98949      38.48127
	           O(5,1,2,3)     -68.07170       -0.00010       -1.28572     -69.35742
	           O(5,1,2,4)      40.47111       -0.00015       -1.98956      38.48155
	           O(5,1,3,4)     -40.47072        0.00015        1.98949     -38.48124
	-------------------------------------------------------------------------------

	                                 ==> Convergence Check <==                                  
    
	Measures of convergence in internal coordinates in au.
    
	Criteria marked as inactive (o), active & met (*), and active & unmet ( ).

	----------------------------------------------------------------------------------------------
	   Step    Total Energy     Delta E     Max Force     RMS Force      Max Disp      RMS Disp   
	----------------------------------------------------------------------------------------------
	  Convergence Criteria     1.00e-06 *    3.00e-04 *             o    1.20e-03 *             o
	----------------------------------------------------------------------------------------------
	     4     -40.05129634   -3.71e-03      2.55e-03      1.57e-03 o    4.49e-02      2.75e-02 o  ~
	----------------------------------------------------------------------------------------------

Next Geometry in Ang 
	Fragment 1 (Ang)

	    C   0.0000010388  -0.0000011885  -0.0000015353
	    H  -0.5018219698   1.0027896817   0.0831029010
	    H  -0.1709765408  -0.9913612830  -0.5022895594
	    H  -0.1950311385  -0.3415787226   1.0533784743
	    H   0.8678286102   0.3301515123  -0.6341902805


    Structure for next step:
    Molecular point group: c1
    Full point group: C1

    Geometry (in Angstrom), charge = 1, multiplicity = 2:

    C            0.000001038795    -0.000001188477    -0.000001535332
    H           -0.501821969816     1.002789681740     0.083102900963
    H           -0.170976540768    -0.991361283026    -0.502289559442
    H           -0.195031138460    -0.341578722579     1.053378474291
    H            0.867828610249     0.330151512343    -0.634190280481


Scratch directory: D:\BaiduSyncdisk\py\MS\psi4_scratch/
gradient() will perform analytic gradient computation.
   => Libint2 <=

    Primary   basis highest AM E, G, H:  6, 6, -
    Auxiliary basis highest AM E, G, H:  7, 7, -
    Onebody   basis highest AM E, G, H:  -, -, -
    Solid Harmonics ordering:            Gaussian

*** tstart() called on iCarus
*** at Mon Jul  7 22:34:27 2025

   => Loading Basis Set <=

    Name: 6-31G(D)
    Role: ORBITAL
    Keyword: BASIS
    atoms 1   entry C          line   111 file D:\anaconda3\envs\nistpy\Library\share\psi4\basis\6-31g_d_.gbs 
    atoms 2-5 entry H          line    44 file D:\anaconda3\envs\nistpy\Library\share\psi4\basis\6-31g_d_.gbs 


         ---------------------------------------------------------
                                   SCF
               by Justin Turney, Rob Parrish, Andy Simmonett
                          and Daniel G. A. Smith
                              UKS Reference
                       10 Threads,   -562 MiB Core
         ---------------------------------------------------------

  ==> Geometry <==

    Molecular point group: c1
    Full point group: C1

    Geometry (in Angstrom), charge = 1, multiplicity = 2:

       Center              X                  Y                   Z               Mass       
    ------------   -----------------  -----------------  -----------------  -----------------
         C            0.000001038795    -0.000001188477    -0.000001535332    12.000000000000
         H           -0.501821969816     1.002789681740     0.083102900963     1.007825032230
         H           -0.170976540768    -0.991361283026    -0.502289559442     1.007825032230
         H           -0.195031138460    -0.341578722579     1.053378474291     1.007825032230
         H            0.867828610249     0.330151512343    -0.634190280481     1.007825032230

  Running in c1 symmetry.

  Rotational constants: A =      5.88370  B =      5.88370  C =      3.77683 [cm^-1]
  Rotational constants: A = 176388.84217  B = 176388.78951  C = 113226.44317 [MHz]
  Nuclear repulsion =   13.053235214897372

  Charge       = 1
  Multiplicity = 2
  Electrons    = 9
  Nalpha       = 5
  Nbeta        = 4

  ==> Algorithm <==

  SCF Algorithm Type is DF.
  DIIS enabled.
  MOM disabled.
  Fractional occupation disabled.
  Guess Type is READ.
  Energy threshold   = 1.00e-08
  Density threshold  = 1.00e-08
  Integral threshold = 1.00e-12

  ==> Primary Basis <==

  Basis Set: 6-31G(D)
    Blend: 6-31G(D)
    Number of shells: 14
    Number of basis functions: 23
    Number of Cartesian functions: 23
    Spherical Harmonics?: false
    Max angular momentum: 2

  ==> DFT Potential <==

   => LibXC <=

    Version 7.0.0
    S. Lehtola, C. Steigemann, M. J.T. Oliveira, and M. A.L. Marques.,  SoftwareX 7, 1–5 (2018) (10.1016/j.softx.2017.11.002)

   => Composite Functional: B3LYP <= 

    B3LYP Hyb-GGA Exchange-Correlation Functional

    P. J. Stephens, F. J. Devlin, C. F. Chabalowski, and M. J. Frisch.,  J. Phys. Chem. 98, 11623 (1994) (10.1021/j100096a001)

    Deriv               =              1
    GGA                 =           TRUE
    Meta                =          FALSE

    Exchange Hybrid     =           TRUE
    MP2 Hybrid          =          FALSE

   => Exchange Functionals <=

    0.0800   Slater exchange
    0.7200         Becke 88

   => Exact (HF) Exchange <=

    0.2000               HF 

   => Correlation Functionals <=

    0.1900   Vosko, Wilk & Nusair (VWN5_RPA)
    0.8100   Lee, Yang & Parr

   => LibXC Density Thresholds  <==

    XC_HYB_GGA_XC_B3LYP:  1.00E-15 

   => Molecular Quadrature <=

    Radial Scheme          =       TREUTLER
    Pruning Scheme         =           NONE
    Nuclear Scheme         =       TREUTLER

    Blocking Scheme        =         OCTREE
    BS radius alpha        =              1
    Pruning alpha          =              1
    Radial Points          =             75
    Spherical Points       =            302
    Total Points           =         109484
    Total Blocks           =            869
    Max Points             =            256
    Max Functions          =             23
    Weights Tolerance      =       1.00E-15

   => Loading Basis Set <=

    Name: (6-31G(D) AUX)
    Role: JKFIT
    Keyword: DF_BASIS_SCF
    atoms 1   entry C          line   121 file D:\anaconda3\envs\nistpy\Library\share\psi4\basis\cc-pvdz-jkfit.gbs 
    atoms 2-5 entry H          line    51 file D:\anaconda3\envs\nistpy\Library\share\psi4\basis\cc-pvdz-jkfit.gbs 

   => Loading Basis Set <=

    Name: 6-31G(D)
    Role: ORBITAL
    Keyword: BASIS
    atoms 1   entry C          line   111 file D:\anaconda3\envs\nistpy\Library\share\psi4\basis\6-31g_d_.gbs 
    atoms 2-5 entry H          line    44 file D:\anaconda3\envs\nistpy\Library\share\psi4\basis\6-31g_d_.gbs 

  Reading orbitals from file D:\BaiduSyncdisk\py\MS\psi4_scratch/psi4_calculation.default.33932.180.npy, no projection.

  ==> Integral Setup <==

  DFHelper Memory: AOs need 0.001 GiB; user supplied 5.515 GiB. 
  Using in-core AOs.

  ==> MemDFJK: Density-Fitted J/K Matrices <==

    J tasked:                   Yes
    K tasked:                   Yes
    wK tasked:                   No
    OpenMP threads:              10
    Memory [MiB]:              5647
    Algorithm:                 Core
    Schwarz Cutoff:           1E-12
    Mask sparsity (%):       0.0000
    Fitting Condition:        1E-10

   => Auxiliary Basis Set <=

  Basis Set: (6-31G(D) AUX)
    Blend: CC-PVDZ-JKFIT
    Number of shells: 60
    Number of basis functions: 181
    Number of Cartesian functions: 181
    Spherical Harmonics?: false
    Max angular momentum: 3

  Cached 100.0% of DFT collocation blocks in 0.073 [GiB].

  Minimum eigenvalue in the overlap matrix is 2.3606278109E-02.
  Reciprocal condition number of the overlap matrix is 4.2854845677E-03.
    Using symmetric orthogonalization.

  ==> Pre-Iterations <==

  SCF Guess: Orbitals guess was supplied from a previous computation.

   -------------------------------------------------------
    Irrep   Nso     Nmo     Nalpha   Nbeta   Ndocc  Nsocc
   -------------------------------------------------------
     A         23      23       5       4       4       1
   -------------------------------------------------------
    Total      23      23       5       4       4       1
   -------------------------------------------------------

  ==> Iterations <==

                           Total Energy        Delta E     RMS |[F,P]|

   @DF-UKS iter   0:   -40.05629548092146   -4.00563e+01   3.94967e-04 
   @DF-UKS iter   1:   -40.05191977836831    4.37570e-03   6.93579e-05 DIIS
   @DF-UKS iter   2:   -40.05192103442479   -1.25606e-06   3.94573e-05 DIIS
   @DF-UKS iter   3:   -40.05192148276732   -4.48343e-07   1.48948e-05 DIIS
   @DF-UKS iter   4:   -40.05192156333831   -8.05710e-08   1.96046e-06 DIIS
   @DF-UKS iter   5:   -40.05192156671694   -3.37863e-09   1.91125e-07 DIIS
   @DF-UKS iter   6:   -40.05192156674211   -2.51674e-11   1.56684e-08 DIIS
   @DF-UKS iter   7:   -40.05192156674224   -1.27898e-13   1.09701e-09 DIIS
  Energy and wave function converged.


  ==> Post-Iterations <==

   Electrons on quadrature grid:
      Nalpha   =    5.0000015829 ; deviation = 1.583e-06
      Nbeta    =    4.0000040830 ; deviation = 4.083e-06
      Ntotal   =    9.0000056659 ; deviation = 5.666e-06 

   @Spin Contamination Metric:   1.154515663E-03
   @S^2 Expected:                7.500000000E-01
   @S^2 Observed:                7.511545157E-01
   @S   Expected:                5.000000000E-01
   @S   Observed:                5.000000000E-01

    Orbital Energies [Eh]
    ---------------------

    Alpha Occupied:                                                       

       1A    -10.569744     2A     -1.072518     3A     -0.790973  
       4A     -0.790973     5A     -0.681633  

    Alpha Virtual:                                                        

       6A     -0.243781     7A     -0.186795     8A     -0.101966  
       9A     -0.101966    10A      0.192107    11A      0.293453  
      12A      0.293453    13A      0.461317    14A      0.488471  
      15A      0.571700    16A      0.571700    17A      0.650626  
      18A      1.252583    19A      1.514153    20A      1.621799  
      21A      1.621799    22A      1.977893    23A      3.732240  

    Beta Occupied:                                                        

       1A    -10.560103     2A     -1.029381     3A     -0.766441  
       4A     -0.766441  

    Beta Virtual:                                                         

       5A     -0.536733     6A     -0.210549     7A     -0.168790  
       8A     -0.089314     9A     -0.089314    10A      0.236062  
      11A      0.305713    12A      0.305713    13A      0.488306  
      14A      0.529803    15A      0.596039    16A      0.596039  
      17A      0.671409    18A      1.258068    19A      1.584942  
      20A      1.670435    21A      1.670435    22A      1.990698  
      23A      3.759813  

    Final Occupation by Irrep:
              A 
    DOCC [     4 ]
    SOCC [     1 ]
    NA   [     5 ]
    NB   [     4 ]

  @DF-UKS Final Energy:   -40.05192156674224

   => Energetics <=

    Nuclear Repulsion Energy =             13.0532352148973718
    One-Electron Energy =                 -75.3868566975907299
    Two-Electron Energy =                  27.5534071396088329
    DFT Exchange-Correlation Energy =      -5.2717072236577103
    Empirical Dispersion Energy =           0.0000000000000000
    VV10 Nonlocal Energy =                  0.0000000000000000
    Total Energy =                        -40.0519215667422372

  UHF NO Occupations:
  HONO-2 :    3  A 1.9998673
  HONO-1 :    4  A 1.9996883
  HONO-0 :    5  A 1.0000000
  LUNO+0 :    6  A 0.0003117
  LUNO+1 :    7  A 0.0001327
  LUNO+2 :    8  A 0.0001327
  LUNO+3 :    9  A 0.0000002


Computation Completed


Properties will be evaluated at   0.000000,   0.000000,   0.000000 [a0]

Properties computed using the SCF density matrix


 Multipole Moments:

 ------------------------------------------------------------------------------------
     Multipole            Electronic (a.u.)      Nuclear  (a.u.)        Total (a.u.)
 ------------------------------------------------------------------------------------

 L = 1.  Multiply by 2.5417464519 to convert [e a0] to [Debye]
 Dipole X            :         -0.0000120            0.0000098           -0.0000022
 Dipole Y            :          0.0000138           -0.0000112            0.0000026
 Dipole Z            :          0.0000179           -0.0000145            0.0000034
 Magnitude           :                                                    0.0000048

 ------------------------------------------------------------------------------------

*** tstop() called on iCarus at Mon Jul  7 22:34:28 2025
Module time:
	user time   =        nan seconds =        nan minutes
	system time =        nan seconds =        nan minutes
	total time  =          1 seconds =       0.02 minutes
Total time:
	user time   =        nan seconds =        nan minutes
	system time =        nan seconds =        nan minutes
	total time  =          5 seconds =       0.08 minutes

*** tstart() called on iCarus
*** at Mon Jul  7 22:34:28 2025


         ------------------------------------------------------------
                                   SCF GRAD                          
                          Rob Parrish, Justin Turney,                
                       Andy Simmonett, and Alex Sokolov              
         ------------------------------------------------------------

  ==> Geometry <==

    Molecular point group: c1
    Full point group: C1

    Geometry (in Angstrom), charge = 1, multiplicity = 2:

       Center              X                  Y                   Z               Mass       
    ------------   -----------------  -----------------  -----------------  -----------------
         C            0.000001038795    -0.000001188477    -0.000001535332    12.000000000000
         H           -0.501821969816     1.002789681740     0.083102900963     1.007825032230
         H           -0.170976540768    -0.991361283026    -0.502289559442     1.007825032230
         H           -0.195031138460    -0.341578722579     1.053378474291     1.007825032230
         H            0.867828610249     0.330151512343    -0.634190280481     1.007825032230

  Nuclear repulsion =   13.053235214897372

  ==> Basis Set <==

  Basis Set: 6-31G(D)
    Blend: 6-31G(D)
    Number of shells: 14
    Number of basis functions: 23
    Number of Cartesian functions: 23
    Spherical Harmonics?: false
    Max angular momentum: 2

  ==> DFJKGrad: Density-Fitted SCF Gradients <==

    Gradient:                    1
    J tasked:                  Yes
    K tasked:                  Yes
    wK tasked:                  No
    OpenMP threads:             10
    Integrals threads:          10
    Memory [MiB]:             -422
    Schwarz Cutoff:          1E-12
    Fitting Condition:       1E-10

   => Auxiliary Basis Set <=

  Basis Set: (6-31G(D) AUX)
    Blend: CC-PVDZ-JKFIT
    Number of shells: 60
    Number of basis functions: 181
    Number of Cartesian functions: 181
    Spherical Harmonics?: false
    Max angular momentum: 3

  ==> DFT Potential <==

   => LibXC <=

    Version 7.0.0
    S. Lehtola, C. Steigemann, M. J.T. Oliveira, and M. A.L. Marques.,  SoftwareX 7, 1–5 (2018) (10.1016/j.softx.2017.11.002)

   => Composite Functional: B3LYP <= 

    B3LYP Hyb-GGA Exchange-Correlation Functional

    P. J. Stephens, F. J. Devlin, C. F. Chabalowski, and M. J. Frisch.,  J. Phys. Chem. 98, 11623 (1994) (10.1021/j100096a001)

    Deriv               =              1
    GGA                 =           TRUE
    Meta                =          FALSE

    Exchange Hybrid     =           TRUE
    MP2 Hybrid          =          FALSE

   => Exchange Functionals <=

    0.0800   Slater exchange
    0.7200         Becke 88

   => Exact (HF) Exchange <=

    0.2000               HF 

   => Correlation Functionals <=

    0.1900   Vosko, Wilk & Nusair (VWN5_RPA)
    0.8100   Lee, Yang & Parr

   => LibXC Density Thresholds  <==

    XC_HYB_GGA_XC_B3LYP:  1.00E-15 

   => Molecular Quadrature <=

    Radial Scheme          =       TREUTLER
    Pruning Scheme         =           NONE
    Nuclear Scheme         =       TREUTLER

    Blocking Scheme        =         OCTREE
    BS radius alpha        =              1
    Pruning alpha          =              1
    Radial Points          =             75
    Spherical Points       =            302
    Total Points           =         109484
    Total Blocks           =            869
    Max Points             =            256
    Max Functions          =             23
    Weights Tolerance      =       1.00E-15


  -Total Gradient:
     Atom            X                  Y                   Z
    ------   -----------------  -----------------  -----------------
       1        0.000000055372     0.000000132185     0.000000033051
       2       -0.001172210334    -0.000621599239    -0.000986247862
       3       -0.001386323645     0.000664746666    -0.000607840120
       4        0.001622043899     0.000195582661     0.000252690469
       5        0.000936434798    -0.000238862168     0.001341364313


*** tstop() called on iCarus at Mon Jul  7 22:34:28 2025
Module time:
	user time   =        nan seconds =        nan minutes
	system time =        nan seconds =        nan minutes
	total time  =          0 seconds =       0.00 minutes
Total time:
	user time   =        nan seconds =        nan minutes
	system time =        nan seconds =        nan minutes
	total time  =          5 seconds =       0.08 minutes

    			-----------------------------------------

    			 OPTKING 3.0: for geometry optimizations 

    			     By R.A. King, Bethel University     

    			        with contributions from          

    			    A.V. Copan, J. Cayton, A. Heide      

    			-----------------------------------------

    
	                            ===> Fragment 1 <== 

	 Z (Atomic Numbers)          Masses                          Geom                  
	       6.000000            12.000000          0.00000196 -0.00000225 -0.00000290   
	       1.000000             1.007825         -0.94830609  1.89499786  0.15704172   
	       1.000000             1.007825         -0.32309884 -1.87340132 -0.94918970   
	       1.000000             1.007825         -0.36855544 -0.64549024  1.99059682   
	       1.000000             1.007825          1.63995840  0.62389594 -1.19844594   

	 - Coordinate -           - BOHR/RAD -       - ANG/DEG -
	 R(1,2)           =         2.124847           1.124421
	 R(1,3)           =         2.124847           1.124420
	 R(1,4)           =         2.124847           1.124421
	 R(1,5)           =         2.124846           1.124420
	 B(2,1,3)         =         2.421046         138.715733
	 B(2,1,4)         =         1.695389          97.138662
	 B(2,1,5)         =         1.695395          97.138984
	 B(3,1,4)         =         1.695397          97.139077
	 B(3,1,5)         =         1.695402          97.139363
	 B(4,1,5)         =         2.421047         138.715752
	 O(2,1,3,4)       =         0.671630          38.481572
	 O(2,1,3,5)       =        -0.671626         -38.481327
	 O(2,1,4,5)       =         1.210534          69.358463
	 O(3,1,2,4)       =        -0.671629         -38.481531
	 O(3,1,2,5)       =         0.671625          38.481289
	 O(3,1,4,5)       =        -1.210513         -69.357270
	 O(4,1,2,3)       =         1.210531          69.358333
	 O(4,1,2,5)       =        -0.671630         -38.481585
	 O(4,1,3,5)       =         0.671625          38.481265
	 O(5,1,2,3)       =        -1.210515         -69.357419
	 O(5,1,2,4)       =         0.671630          38.481553
	 O(5,1,3,4)       =        -0.671624         -38.481237


	        --- Internal Coordinate Step in ANG or DEG, aJ/ANG or AJ/DEG ---
	-------------------------------------------------------------------------------
	           Coordinate      Previous         Force          Change          New 
	           ----------      --------        ------          ------        ------
	               R(1,2)       1.12442        0.00086       -0.00038       1.12404
	               R(1,3)       1.12442        0.00085       -0.00038       1.12404
	               R(1,4)       1.12442        0.00086       -0.00038       1.12404
	               R(1,5)       1.12442        0.00086       -0.00038       1.12404
	             B(2,1,3)     138.71573        0.00006        0.97624     139.69197
	             B(2,1,4)      97.13866       -0.00002       -0.32125      96.81741
	             B(2,1,5)      97.13898       -0.00002       -0.32130      96.81769
	             B(3,1,4)      97.13908       -0.00002       -0.32131      96.81777
	             B(3,1,5)      97.13936       -0.00002       -0.32133      96.81803
	             B(4,1,5)     138.71575        0.00006        0.97622     139.69198
	           O(2,1,3,4)      38.48157       -0.00005       -0.77530      37.70627
	           O(2,1,3,5)     -38.48133        0.00005        0.77527     -37.70606
	           O(2,1,4,5)      69.35846        0.00003        0.48805      69.84652
	           O(3,1,2,4)     -38.48153        0.00005        0.77529     -37.70624
	           O(3,1,2,5)      38.48129       -0.00005       -0.77526      37.70603
	           O(3,1,4,5)     -69.35727       -0.00003       -0.48818     -69.84545
	           O(4,1,2,3)      69.35833        0.00003        0.48807      69.84640
	           O(4,1,2,5)     -38.48159        0.00005        0.77530     -37.70629
	           O(4,1,3,5)      38.48127       -0.00005       -0.77525      37.70602
	           O(5,1,2,3)     -69.35742       -0.00003       -0.48815     -69.84557
	           O(5,1,2,4)      38.48155       -0.00005       -0.77529      37.70626
	           O(5,1,3,4)     -38.48124        0.00005        0.77525     -37.70599
	-------------------------------------------------------------------------------

	                                 ==> Convergence Check <==                                  
    
	Measures of convergence in internal coordinates in au.
    
	Criteria marked as inactive (o), active & met (*), and active & unmet ( ).

	----------------------------------------------------------------------------------------------
	   Step    Total Energy     Delta E     Max Force     RMS Force      Max Disp      RMS Disp   
	----------------------------------------------------------------------------------------------
	  Convergence Criteria     1.00e-06 *    3.00e-04 *             o    1.20e-03 *             o
	----------------------------------------------------------------------------------------------
	     5     -40.05192157   -6.25e-04      8.33e-04      5.17e-04 o    1.70e-02      1.06e-02 o  ~
	----------------------------------------------------------------------------------------------

Next Geometry in Ang 
	Fragment 1 (Ang)

	    C   0.0000009019  -0.0000010845  -0.0000014124
	    H  -0.4945601514   1.0054843735   0.0887488116
	    H  -0.1627768734  -0.9943181243  -0.4983023506
	    H  -0.2042678370  -0.3423996400   1.0509532166
	    H   0.8616039598   0.3312344754  -0.6413982652


    Structure for next step:
    Molecular point group: c1
    Full point group: C1

    Geometry (in Angstrom), charge = 1, multiplicity = 2:

    C            0.000000901948    -0.000001084548    -0.000001412360
    H           -0.494560151380     1.005484373472     0.088748811590
    H           -0.162776873355    -0.994318124305    -0.498302350596
    H           -0.204267836992    -0.342399639990     1.050953216553
    H            0.861603959779     0.331234475371    -0.641398265188


Scratch directory: D:\BaiduSyncdisk\py\MS\psi4_scratch/
gradient() will perform analytic gradient computation.
   => Libint2 <=

    Primary   basis highest AM E, G, H:  6, 6, -
    Auxiliary basis highest AM E, G, H:  7, 7, -
    Onebody   basis highest AM E, G, H:  -, -, -
    Solid Harmonics ordering:            Gaussian

*** tstart() called on iCarus
*** at Mon Jul  7 22:34:28 2025

   => Loading Basis Set <=

    Name: 6-31G(D)
    Role: ORBITAL
    Keyword: BASIS
    atoms 1   entry C          line   111 file D:\anaconda3\envs\nistpy\Library\share\psi4\basis\6-31g_d_.gbs 
    atoms 2-5 entry H          line    44 file D:\anaconda3\envs\nistpy\Library\share\psi4\basis\6-31g_d_.gbs 


         ---------------------------------------------------------
                                   SCF
               by Justin Turney, Rob Parrish, Andy Simmonett
                          and Daniel G. A. Smith
                              UKS Reference
                       10 Threads,   -562 MiB Core
         ---------------------------------------------------------

  ==> Geometry <==

    Molecular point group: c1
    Full point group: C1

    Geometry (in Angstrom), charge = 1, multiplicity = 2:

       Center              X                  Y                   Z               Mass       
    ------------   -----------------  -----------------  -----------------  -----------------
         C            0.000000901948    -0.000001084548    -0.000001412360    12.000000000000
         H           -0.494560151380     1.005484373472     0.088748811590     1.007825032230
         H           -0.162776873355    -0.994318124305    -0.498302350596     1.007825032230
         H           -0.204267836992    -0.342399639990     1.050953216553     1.007825032230
         H            0.861603959779     0.331234475371    -0.641398265188     1.007825032230

  Running in c1 symmetry.

  Rotational constants: A =      5.91697  B =      5.91697  C =      3.75551 [cm^-1]
  Rotational constants: A = 177386.28541  B = 177386.27515  C = 112587.38309 [MHz]
  Nuclear repulsion =   13.059176013037096

  Charge       = 1
  Multiplicity = 2
  Electrons    = 9
  Nalpha       = 5
  Nbeta        = 4

  ==> Algorithm <==

  SCF Algorithm Type is DF.
  DIIS enabled.
  MOM disabled.
  Fractional occupation disabled.
  Guess Type is READ.
  Energy threshold   = 1.00e-08
  Density threshold  = 1.00e-08
  Integral threshold = 1.00e-12

  ==> Primary Basis <==

  Basis Set: 6-31G(D)
    Blend: 6-31G(D)
    Number of shells: 14
    Number of basis functions: 23
    Number of Cartesian functions: 23
    Spherical Harmonics?: false
    Max angular momentum: 2

  ==> DFT Potential <==

   => LibXC <=

    Version 7.0.0
    S. Lehtola, C. Steigemann, M. J.T. Oliveira, and M. A.L. Marques.,  SoftwareX 7, 1–5 (2018) (10.1016/j.softx.2017.11.002)

   => Composite Functional: B3LYP <= 

    B3LYP Hyb-GGA Exchange-Correlation Functional

    P. J. Stephens, F. J. Devlin, C. F. Chabalowski, and M. J. Frisch.,  J. Phys. Chem. 98, 11623 (1994) (10.1021/j100096a001)

    Deriv               =              1
    GGA                 =           TRUE
    Meta                =          FALSE

    Exchange Hybrid     =           TRUE
    MP2 Hybrid          =          FALSE

   => Exchange Functionals <=

    0.0800   Slater exchange
    0.7200         Becke 88

   => Exact (HF) Exchange <=

    0.2000               HF 

   => Correlation Functionals <=

    0.1900   Vosko, Wilk & Nusair (VWN5_RPA)
    0.8100   Lee, Yang & Parr

   => LibXC Density Thresholds  <==

    XC_HYB_GGA_XC_B3LYP:  1.00E-15 

   => Molecular Quadrature <=

    Radial Scheme          =       TREUTLER
    Pruning Scheme         =           NONE
    Nuclear Scheme         =       TREUTLER

    Blocking Scheme        =         OCTREE
    BS radius alpha        =              1
    Pruning alpha          =              1
    Radial Points          =             75
    Spherical Points       =            302
    Total Points           =         109480
    Total Blocks           =            865
    Max Points             =            256
    Max Functions          =             23
    Weights Tolerance      =       1.00E-15

   => Loading Basis Set <=

    Name: (6-31G(D) AUX)
    Role: JKFIT
    Keyword: DF_BASIS_SCF
    atoms 1   entry C          line   121 file D:\anaconda3\envs\nistpy\Library\share\psi4\basis\cc-pvdz-jkfit.gbs 
    atoms 2-5 entry H          line    51 file D:\anaconda3\envs\nistpy\Library\share\psi4\basis\cc-pvdz-jkfit.gbs 

   => Loading Basis Set <=

    Name: 6-31G(D)
    Role: ORBITAL
    Keyword: BASIS
    atoms 1   entry C          line   111 file D:\anaconda3\envs\nistpy\Library\share\psi4\basis\6-31g_d_.gbs 
    atoms 2-5 entry H          line    44 file D:\anaconda3\envs\nistpy\Library\share\psi4\basis\6-31g_d_.gbs 

  Reading orbitals from file D:\BaiduSyncdisk\py\MS\psi4_scratch/psi4_calculation.default.33932.180.npy, no projection.

  ==> Integral Setup <==

  DFHelper Memory: AOs need 0.001 GiB; user supplied 5.515 GiB. 
  Using in-core AOs.

  ==> MemDFJK: Density-Fitted J/K Matrices <==

    J tasked:                   Yes
    K tasked:                   Yes
    wK tasked:                   No
    OpenMP threads:              10
    Memory [MiB]:              5647
    Algorithm:                 Core
    Schwarz Cutoff:           1E-12
    Mask sparsity (%):       0.0000
    Fitting Condition:        1E-10

   => Auxiliary Basis Set <=

  Basis Set: (6-31G(D) AUX)
    Blend: CC-PVDZ-JKFIT
    Number of shells: 60
    Number of basis functions: 181
    Number of Cartesian functions: 181
    Spherical Harmonics?: false
    Max angular momentum: 3

  Cached 100.0% of DFT collocation blocks in 0.073 [GiB].

  Minimum eigenvalue in the overlap matrix is 2.3775204216E-02.
  Reciprocal condition number of the overlap matrix is 4.3142062251E-03.
    Using symmetric orthogonalization.

  ==> Pre-Iterations <==

  SCF Guess: Orbitals guess was supplied from a previous computation.

   -------------------------------------------------------
    Irrep   Nso     Nmo     Nalpha   Nbeta   Ndocc  Nsocc
   -------------------------------------------------------
     A         23      23       5       4       4       1
   -------------------------------------------------------
    Total      23      23       5       4       4       1
   -------------------------------------------------------

  ==> Iterations <==

                           Total Energy        Delta E     RMS |[F,P]|

   @DF-UKS iter   0:   -40.05254170977499   -4.00525e+01   1.44092e-04 
   @DF-UKS iter   1:   -40.05199625068629    5.45459e-04   3.46040e-05 DIIS
   @DF-UKS iter   2:   -40.05199644274204   -1.92056e-07   2.65698e-05 DIIS
   @DF-UKS iter   3:   -40.05199664514588   -2.02404e-07   4.74946e-06 DIIS
   @DF-UKS iter   4:   -40.05199665596071   -1.08148e-08   6.90612e-07 DIIS
   @DF-UKS iter   5:   -40.05199665638300   -4.22290e-10   6.04652e-08 DIIS
   @DF-UKS iter   6:   -40.05199665638521   -2.21689e-12   4.46487e-09 DIIS
  Energy and wave function converged.


  ==> Post-Iterations <==

   Electrons on quadrature grid:
      Nalpha   =    5.0000000818 ; deviation = 8.183e-08
      Nbeta    =    4.0000027028 ; deviation = 2.703e-06
      Ntotal   =    9.0000027846 ; deviation = 2.785e-06 

   @Spin Contamination Metric:   1.132870054E-03
   @S^2 Expected:                7.500000000E-01
   @S^2 Observed:                7.511328701E-01
   @S   Expected:                5.000000000E-01
   @S   Observed:                5.000000000E-01

    Orbital Energies [Eh]
    ---------------------

    Alpha Occupied:                                                       

       1A    -10.569452     2A     -1.072751     3A     -0.791922  
       4A     -0.791922     5A     -0.678242  

    Alpha Virtual:                                                        

       6A     -0.247306     7A     -0.187256     8A     -0.100764  
       9A     -0.100764    10A      0.192780    11A      0.297801  
      12A      0.297801    13A      0.459288    14A      0.485454  
      15A      0.573200    16A      0.573201    17A      0.647989  
      18A      1.251629    19A      1.526233    20A      1.612854  
      21A      1.612854    22A      1.984526    23A      3.731052  

    Beta Occupied:                                                        

       1A    -10.559731     2A     -1.029756     3A     -0.767555  
       4A     -0.767555  

    Beta Virtual:                                                         

       5A     -0.533231     6A     -0.214154     7A     -0.169143  
       8A     -0.088252     9A     -0.088252    10A      0.237211  
      11A      0.310076    12A      0.310076    13A      0.485777  
      14A      0.526624    15A      0.597445    16A      0.597445  
      17A      0.669148    18A      1.257228    19A      1.597160  
      20A      1.661673    21A      1.661673    22A      1.997423  
      23A      3.758814  

    Final Occupation by Irrep:
              A 
    DOCC [     4 ]
    SOCC [     1 ]
    NA   [     5 ]
    NB   [     4 ]

  @DF-UKS Final Energy:   -40.05199665638521

   => Energetics <=

    Nuclear Repulsion Energy =             13.0591760130370957
    One-Electron Energy =                 -75.3980440939379832
    Two-Electron Energy =                  27.5588569738249447
    DFT Exchange-Correlation Energy =      -5.2719855493092744
    Empirical Dispersion Energy =           0.0000000000000000
    VV10 Nonlocal Energy =                  0.0000000000000000
    Total Energy =                        -40.0519966563852137

  UHF NO Occupations:
  HONO-2 :    3  A 1.9998751
  HONO-1 :    4  A 1.9996835
  HONO-0 :    5  A 1.0000000
  LUNO+0 :    6  A 0.0003165
  LUNO+1 :    7  A 0.0001249
  LUNO+2 :    8  A 0.0001249
  LUNO+3 :    9  A 0.0000002


Computation Completed


Properties will be evaluated at   0.000000,   0.000000,   0.000000 [a0]

Properties computed using the SCF density matrix


 Multipole Moments:

 ------------------------------------------------------------------------------------
     Multipole            Electronic (a.u.)      Nuclear  (a.u.)        Total (a.u.)
 ------------------------------------------------------------------------------------

 L = 1.  Multiply by 2.5417464519 to convert [e a0] to [Debye]
 Dipole X            :         -0.0000104            0.0000085           -0.0000019
 Dipole Y            :          0.0000126           -0.0000102            0.0000024
 Dipole Z            :          0.0000164           -0.0000133            0.0000031
 Magnitude           :                                                    0.0000043

 ------------------------------------------------------------------------------------

*** tstop() called on iCarus at Mon Jul  7 22:34:28 2025
Module time:
	user time   =        nan seconds =        nan minutes
	system time =        nan seconds =        nan minutes
	total time  =          0 seconds =       0.00 minutes
Total time:
	user time   =        nan seconds =        nan minutes
	system time =        nan seconds =        nan minutes
	total time  =          5 seconds =       0.08 minutes

*** tstart() called on iCarus
*** at Mon Jul  7 22:34:28 2025


         ------------------------------------------------------------
                                   SCF GRAD                          
                          Rob Parrish, Justin Turney,                
                       Andy Simmonett, and Alex Sokolov              
         ------------------------------------------------------------

  ==> Geometry <==

    Molecular point group: c1
    Full point group: C1

    Geometry (in Angstrom), charge = 1, multiplicity = 2:

       Center              X                  Y                   Z               Mass       
    ------------   -----------------  -----------------  -----------------  -----------------
         C            0.000000901948    -0.000001084548    -0.000001412360    12.000000000000
         H           -0.494560151380     1.005484373472     0.088748811590     1.007825032230
         H           -0.162776873355    -0.994318124305    -0.498302350596     1.007825032230
         H           -0.204267836992    -0.342399639990     1.050953216553     1.007825032230
         H            0.861603959779     0.331234475371    -0.641398265188     1.007825032230

  Nuclear repulsion =   13.059176013037096

  ==> Basis Set <==

  Basis Set: 6-31G(D)
    Blend: 6-31G(D)
    Number of shells: 14
    Number of basis functions: 23
    Number of Cartesian functions: 23
    Spherical Harmonics?: false
    Max angular momentum: 2

  ==> DFJKGrad: Density-Fitted SCF Gradients <==

    Gradient:                    1
    J tasked:                  Yes
    K tasked:                  Yes
    wK tasked:                  No
    OpenMP threads:             10
    Integrals threads:          10
    Memory [MiB]:             -422
    Schwarz Cutoff:          1E-12
    Fitting Condition:       1E-10

   => Auxiliary Basis Set <=

  Basis Set: (6-31G(D) AUX)
    Blend: CC-PVDZ-JKFIT
    Number of shells: 60
    Number of basis functions: 181
    Number of Cartesian functions: 181
    Spherical Harmonics?: false
    Max angular momentum: 3

  ==> DFT Potential <==

   => LibXC <=

    Version 7.0.0
    S. Lehtola, C. Steigemann, M. J.T. Oliveira, and M. A.L. Marques.,  SoftwareX 7, 1–5 (2018) (10.1016/j.softx.2017.11.002)

   => Composite Functional: B3LYP <= 

    B3LYP Hyb-GGA Exchange-Correlation Functional

    P. J. Stephens, F. J. Devlin, C. F. Chabalowski, and M. J. Frisch.,  J. Phys. Chem. 98, 11623 (1994) (10.1021/j100096a001)

    Deriv               =              1
    GGA                 =           TRUE
    Meta                =          FALSE

    Exchange Hybrid     =           TRUE
    MP2 Hybrid          =          FALSE

   => Exchange Functionals <=

    0.0800   Slater exchange
    0.7200         Becke 88

   => Exact (HF) Exchange <=

    0.2000               HF 

   => Correlation Functionals <=

    0.1900   Vosko, Wilk & Nusair (VWN5_RPA)
    0.8100   Lee, Yang & Parr

   => LibXC Density Thresholds  <==

    XC_HYB_GGA_XC_B3LYP:  1.00E-15 

   => Molecular Quadrature <=

    Radial Scheme          =       TREUTLER
    Pruning Scheme         =           NONE
    Nuclear Scheme         =       TREUTLER

    Blocking Scheme        =         OCTREE
    BS radius alpha        =              1
    Pruning alpha          =              1
    Radial Points          =             75
    Spherical Points       =            302
    Total Points           =         109480
    Total Blocks           =            865
    Max Points             =            256
    Max Functions          =             23
    Weights Tolerance      =       1.00E-15


  -Total Gradient:
     Atom            X                  Y                   Z
    ------   -----------------  -----------------  -----------------
       1        0.000000070463    -0.000000021593    -0.000000072912
       2       -0.000264409484    -0.000279985684    -0.000278666120
       3       -0.000359798177     0.000290473346    -0.000110166419
       4        0.000464089516     0.000091495732    -0.000046942914
       5        0.000160047781    -0.000101961706     0.000435848201


*** tstop() called on iCarus at Mon Jul  7 22:34:28 2025
Module time:
	user time   =        nan seconds =        nan minutes
	system time =        nan seconds =        nan minutes
	total time  =          0 seconds =       0.00 minutes
Total time:
	user time   =        nan seconds =        nan minutes
	system time =        nan seconds =        nan minutes
	total time  =          5 seconds =       0.08 minutes

    			-----------------------------------------

    			 OPTKING 3.0: for geometry optimizations 

    			     By R.A. King, Bethel University     

    			        with contributions from          

    			    A.V. Copan, J. Cayton, A. Heide      

    			-----------------------------------------

    
	                            ===> Fragment 1 <== 

	 Z (Atomic Numbers)          Masses                          Geom                  
	       6.000000            12.000000          0.00000170 -0.00000205 -0.00000267   
	       1.000000             1.007825         -0.93458324  1.90009009  0.16771095   
	       1.000000             1.007825         -0.30760371 -1.87898894 -0.94165497   
	       1.000000             1.007825         -0.38601027 -0.64704155  1.98601375   
	       1.000000             1.007825          1.62819551  0.62594244 -1.21206706   

	 - Coordinate -           - BOHR/RAD -       - ANG/DEG -
	 R(1,2)           =         2.124130           1.124041
	 R(1,3)           =         2.124128           1.124040
	 R(1,4)           =         2.124130           1.124041
	 R(1,5)           =         2.124128           1.124040
	 B(2,1,3)         =         2.438085         139.691968
	 B(2,1,4)         =         1.689783          96.817414
	 B(2,1,5)         =         1.689787          96.817689
	 B(3,1,4)         =         1.689789          96.817765
	 B(3,1,5)         =         1.689793          96.818033
	 B(4,1,5)         =         2.438085         139.691976
	 O(2,1,3,4)       =         0.658099          37.706270
	 O(2,1,3,5)       =        -0.658095         -37.706058
	 O(2,1,4,5)       =         1.219052          69.846517
	 O(3,1,2,4)       =        -0.658098         -37.706237
	 O(3,1,2,5)       =         0.658094          37.706026
	 O(3,1,4,5)       =        -1.219033         -69.845451
	 O(4,1,2,3)       =         1.219050          69.846405
	 O(4,1,2,5)       =        -0.658099         -37.706287
	 O(4,1,3,5)       =         0.658094          37.706017
	 O(5,1,2,3)       =        -1.219035         -69.845571
	 O(5,1,2,4)       =         0.658098          37.706262
	 O(5,1,3,4)       =        -0.658094         -37.705992


	        --- Internal Coordinate Step in ANG or DEG, aJ/ANG or AJ/DEG ---
	-------------------------------------------------------------------------------
	           Coordinate      Previous         Force          Change          New 
	           ----------      --------        ------          ------        ------
	               R(1,2)       1.12404        0.00129        0.00009       1.12413
	               R(1,3)       1.12404        0.00129        0.00009       1.12413
	               R(1,4)       1.12404        0.00129        0.00009       1.12413
	               R(1,5)       1.12404        0.00129        0.00009       1.12413
	             B(2,1,3)     139.69197        0.00002        0.26384     139.95580
	             B(2,1,4)      96.81741       -0.00001       -0.08568      96.73174
	             B(2,1,5)      96.81769       -0.00001       -0.08570      96.73199
	             B(3,1,4)      96.81777       -0.00001       -0.08571      96.73206
	             B(3,1,5)      96.81803       -0.00001       -0.08573      96.73230
	             B(4,1,5)     139.69198        0.00002        0.26383     139.95581
	           O(2,1,3,4)      37.70627       -0.00001       -0.21134      37.49493
	           O(2,1,3,5)     -37.70606        0.00001        0.21132     -37.49474
	           O(2,1,4,5)      69.84652        0.00001        0.13187      69.97839
	           O(3,1,2,4)     -37.70624        0.00001        0.21134     -37.49490
	           O(3,1,2,5)      37.70603       -0.00001       -0.21132      37.49471
	           O(3,1,4,5)     -69.84545       -0.00001       -0.13196     -69.97741
	           O(4,1,2,3)      69.84640        0.00001        0.13188      69.97829
	           O(4,1,2,5)     -37.70629        0.00001        0.21134     -37.49495
	           O(4,1,3,5)      37.70602       -0.00001       -0.21131      37.49470
	           O(5,1,2,3)     -69.84557       -0.00001       -0.13195     -69.97752
	           O(5,1,2,4)      37.70626       -0.00001       -0.21134      37.49493
	           O(5,1,3,4)     -37.70599        0.00001        0.21131     -37.49468
	-------------------------------------------------------------------------------

	                                 ==> Convergence Check <==                                  
    
	Measures of convergence in internal coordinates in au.
    
	Criteria marked as inactive (o), active & met (*), and active & unmet ( ).

	----------------------------------------------------------------------------------------------
	   Step    Total Energy     Delta E     Max Force     RMS Force      Max Disp      RMS Disp   
	----------------------------------------------------------------------------------------------
	  Convergence Criteria     1.00e-06 *    3.00e-04 *             o    1.20e-03 *             o
	----------------------------------------------------------------------------------------------
	     6     -40.05199666   -7.51e-05      2.23e-04 *    1.54e-04 o    4.60e-03      2.87e-03 o  ~
	----------------------------------------------------------------------------------------------

Next Geometry in Ang 
	Fragment 1 (Ang)

	    C   0.0000008241  -0.0000010103  -0.0000013186
	    H  -0.4926771763   1.0063733016   0.0902884123
	    H  -0.1605873443  -0.9952759358  -0.4973047344
	    H  -0.2067961420  -0.3426762875   1.0504660081
	    H   0.8600598386   0.3315799321  -0.6434483675


    Structure for next step:
    Molecular point group: c1
    Full point group: C1

    Geometry (in Angstrom), charge = 1, multiplicity = 2:

    C            0.000000824072    -0.000001010328    -0.000001318552
    H           -0.492677176290     1.006373301611     0.090288412349
    H           -0.160587344338    -0.995275935826    -0.497304734417
    H           -0.206796142021    -0.342676287542     1.050466008133
    H            0.860059838577     0.331579932085    -0.643448367514


Scratch directory: D:\BaiduSyncdisk\py\MS\psi4_scratch/
gradient() will perform analytic gradient computation.
   => Libint2 <=

    Primary   basis highest AM E, G, H:  6, 6, -
    Auxiliary basis highest AM E, G, H:  7, 7, -
    Onebody   basis highest AM E, G, H:  -, -, -
    Solid Harmonics ordering:            Gaussian

*** tstart() called on iCarus
*** at Mon Jul  7 22:34:28 2025

   => Loading Basis Set <=

    Name: 6-31G(D)
    Role: ORBITAL
    Keyword: BASIS
    atoms 1   entry C          line   111 file D:\anaconda3\envs\nistpy\Library\share\psi4\basis\6-31g_d_.gbs 
    atoms 2-5 entry H          line    44 file D:\anaconda3\envs\nistpy\Library\share\psi4\basis\6-31g_d_.gbs 


         ---------------------------------------------------------
                                   SCF
               by Justin Turney, Rob Parrish, Andy Simmonett
                          and Daniel G. A. Smith
                              UKS Reference
                       10 Threads,   -562 MiB Core
         ---------------------------------------------------------

  ==> Geometry <==

    Molecular point group: c1
    Full point group: C1

    Geometry (in Angstrom), charge = 1, multiplicity = 2:

       Center              X                  Y                   Z               Mass       
    ------------   -----------------  -----------------  -----------------  -----------------
         C            0.000000824072    -0.000001010328    -0.000001318552    12.000000000000
         H           -0.492677176290     1.006373301611     0.090288412349     1.007825032230
         H           -0.160587344338    -0.995275935826    -0.497304734417     1.007825032230
         H           -0.206796142021    -0.342676287542     1.050466008133     1.007825032230
         H            0.860059838577     0.331579932085    -0.643448367514     1.007825032230

  Running in c1 symmetry.

  Rotational constants: A =      5.92388  B =      5.92388  C =      3.74858 [cm^-1]
  Rotational constants: A = 177593.33476  B = 177593.33135  C = 112379.73051 [MHz]
  Nuclear repulsion =   13.058532383596562

  Charge       = 1
  Multiplicity = 2
  Electrons    = 9
  Nalpha       = 5
  Nbeta        = 4

  ==> Algorithm <==

  SCF Algorithm Type is DF.
  DIIS enabled.
  MOM disabled.
  Fractional occupation disabled.
  Guess Type is READ.
  Energy threshold   = 1.00e-08
  Density threshold  = 1.00e-08
  Integral threshold = 1.00e-12

  ==> Primary Basis <==

  Basis Set: 6-31G(D)
    Blend: 6-31G(D)
    Number of shells: 14
    Number of basis functions: 23
    Number of Cartesian functions: 23
    Spherical Harmonics?: false
    Max angular momentum: 2

  ==> DFT Potential <==

   => LibXC <=

    Version 7.0.0
    S. Lehtola, C. Steigemann, M. J.T. Oliveira, and M. A.L. Marques.,  SoftwareX 7, 1–5 (2018) (10.1016/j.softx.2017.11.002)

   => Composite Functional: B3LYP <= 

    B3LYP Hyb-GGA Exchange-Correlation Functional

    P. J. Stephens, F. J. Devlin, C. F. Chabalowski, and M. J. Frisch.,  J. Phys. Chem. 98, 11623 (1994) (10.1021/j100096a001)

    Deriv               =              1
    GGA                 =           TRUE
    Meta                =          FALSE

    Exchange Hybrid     =           TRUE
    MP2 Hybrid          =          FALSE

   => Exchange Functionals <=

    0.0800   Slater exchange
    0.7200         Becke 88

   => Exact (HF) Exchange <=

    0.2000               HF 

   => Correlation Functionals <=

    0.1900   Vosko, Wilk & Nusair (VWN5_RPA)
    0.8100   Lee, Yang & Parr

   => LibXC Density Thresholds  <==

    XC_HYB_GGA_XC_B3LYP:  1.00E-15 

   => Molecular Quadrature <=

    Radial Scheme          =       TREUTLER
    Pruning Scheme         =           NONE
    Nuclear Scheme         =       TREUTLER

    Blocking Scheme        =         OCTREE
    BS radius alpha        =              1
    Pruning alpha          =              1
    Radial Points          =             75
    Spherical Points       =            302
    Total Points           =         109380
    Total Blocks           =            869
    Max Points             =            254
    Max Functions          =             23
    Weights Tolerance      =       1.00E-15

   => Loading Basis Set <=

    Name: (6-31G(D) AUX)
    Role: JKFIT
    Keyword: DF_BASIS_SCF
    atoms 1   entry C          line   121 file D:\anaconda3\envs\nistpy\Library\share\psi4\basis\cc-pvdz-jkfit.gbs 
    atoms 2-5 entry H          line    51 file D:\anaconda3\envs\nistpy\Library\share\psi4\basis\cc-pvdz-jkfit.gbs 

   => Loading Basis Set <=

    Name: 6-31G(D)
    Role: ORBITAL
    Keyword: BASIS
    atoms 1   entry C          line   111 file D:\anaconda3\envs\nistpy\Library\share\psi4\basis\6-31g_d_.gbs 
    atoms 2-5 entry H          line    44 file D:\anaconda3\envs\nistpy\Library\share\psi4\basis\6-31g_d_.gbs 

  Reading orbitals from file D:\BaiduSyncdisk\py\MS\psi4_scratch/psi4_calculation.default.33932.180.npy, no projection.

  ==> Integral Setup <==

  DFHelper Memory: AOs need 0.001 GiB; user supplied 5.515 GiB. 
  Using in-core AOs.

  ==> MemDFJK: Density-Fitted J/K Matrices <==

    J tasked:                   Yes
    K tasked:                   Yes
    wK tasked:                   No
    OpenMP threads:              10
    Memory [MiB]:              5647
    Algorithm:                 Core
    Schwarz Cutoff:           1E-12
    Mask sparsity (%):       0.0000
    Fitting Condition:        1E-10

   => Auxiliary Basis Set <=

  Basis Set: (6-31G(D) AUX)
    Blend: CC-PVDZ-JKFIT
    Number of shells: 60
    Number of basis functions: 181
    Number of Cartesian functions: 181
    Spherical Harmonics?: false
    Max angular momentum: 3

  Cached 100.0% of DFT collocation blocks in 0.073 [GiB].

  Minimum eigenvalue in the overlap matrix is 2.3833806792E-02.
  Reciprocal condition number of the overlap matrix is 4.3248728899E-03.
    Using symmetric orthogonalization.

  ==> Pre-Iterations <==

  SCF Guess: Orbitals guess was supplied from a previous computation.

   -------------------------------------------------------
    Irrep   Nso     Nmo     Nalpha   Nbeta   Ndocc  Nsocc
   -------------------------------------------------------
     A         23      23       5       4       4       1
   -------------------------------------------------------
    Total      23      23       5       4       4       1
   -------------------------------------------------------

  ==> Iterations <==

                           Total Energy        Delta E     RMS |[F,P]|

   @DF-UKS iter   0:   -40.05175221209653   -4.00518e+01   3.80852e-05 
   @DF-UKS iter   1:   -40.05200144137662   -2.49229e-04   1.13891e-05 DIIS
   @DF-UKS iter   2:   -40.05200145608476   -1.47081e-08   9.85476e-06 DIIS
   @DF-UKS iter   3:   -40.05200148364171   -2.75570e-08   1.06595e-06 DIIS
   @DF-UKS iter   4:   -40.05200148432419   -6.82476e-10   2.03698e-07 DIIS
   @DF-UKS iter   5:   -40.05200148435698   -3.27915e-11   2.55888e-08 DIIS
   @DF-UKS iter   6:   -40.05200148435733   -3.48166e-13   1.23091e-09 DIIS
  Energy and wave function converged.


  ==> Post-Iterations <==

   Electrons on quadrature grid:
      Nalpha   =    5.0000012490 ; deviation = 1.249e-06
      Nbeta    =    4.0000003373 ; deviation = 3.373e-07
      Ntotal   =    9.0000015863 ; deviation = 1.586e-06 

   @Spin Contamination Metric:   1.127448415E-03
   @S^2 Expected:                7.500000000E-01
   @S^2 Observed:                7.511274484E-01
   @S   Expected:                5.000000000E-01
   @S   Observed:                5.000000000E-01

    Orbital Energies [Eh]
    ---------------------

    Alpha Occupied:                                                       

       1A    -10.569399     2A     -1.072719     3A     -0.792095  
       4A     -0.792095     5A     -0.677277  

    Alpha Virtual:                                                        

       6A     -0.248312     7A     -0.187443     8A     -0.100493  
       9A     -0.100493    10A      0.193008    11A      0.299081  
      12A      0.299081    13A      0.458728    14A      0.484553  
      15A      0.573470    16A      0.573470    17A      0.647249  
      18A      1.251461    19A      1.529402    20A      1.610290  
      21A      1.610290    22A      1.986001    23A      3.730639  

    Beta Occupied:                                                        

       1A    -10.559658     2A     -1.029765     3A     -0.767775  
       4A     -0.767775  

    Beta Virtual:                                                         

       5A     -0.532251     6A     -0.215174     7A     -0.169294  
       8A     -0.088016     9A     -0.088016    10A      0.237573  
      11A      0.311357    12A      0.311357    13A      0.485074  
      14A      0.525670    15A      0.597686    16A      0.597686  
      17A      0.668501    18A      1.257090    19A      1.600371  
      20A      1.659160    21A      1.659160    22A      1.998919  
      23A      3.758449  

    Final Occupation by Irrep:
              A 
    DOCC [     4 ]
    SOCC [     1 ]
    NA   [     5 ]
    NB   [     4 ]

  @DF-UKS Final Energy:   -40.05200148435733

   => Energetics <=

    Nuclear Repulsion Energy =             13.0585323835965621
    One-Electron Energy =                 -75.3971866112705698
    Two-Electron Energy =                  27.5585155507298332
    DFT Exchange-Correlation Energy =      -5.2718628074131511
    Empirical Dispersion Energy =           0.0000000000000000
    VV10 Nonlocal Energy =                  0.0000000000000000
    Total Energy =                        -40.0520014843573264

  UHF NO Occupations:
  HONO-2 :    3  A 1.9998771
  HONO-1 :    4  A 1.9996822
  HONO-0 :    5  A 1.0000000
  LUNO+0 :    6  A 0.0003178
  LUNO+1 :    7  A 0.0001229
  LUNO+2 :    8  A 0.0001229
  LUNO+3 :    9  A 0.0000002


Computation Completed


Properties will be evaluated at   0.000000,   0.000000,   0.000000 [a0]

Properties computed using the SCF density matrix


 Multipole Moments:

 ------------------------------------------------------------------------------------
     Multipole            Electronic (a.u.)      Nuclear  (a.u.)        Total (a.u.)
 ------------------------------------------------------------------------------------

 L = 1.  Multiply by 2.5417464519 to convert [e a0] to [Debye]
 Dipole X            :         -0.0000096            0.0000078           -0.0000018
 Dipole Y            :          0.0000117           -0.0000095            0.0000022
 Dipole Z            :          0.0000153           -0.0000125            0.0000029
 Magnitude           :                                                    0.0000040

 ------------------------------------------------------------------------------------

*** tstop() called on iCarus at Mon Jul  7 22:34:29 2025
Module time:
	user time   =        nan seconds =        nan minutes
	system time =        nan seconds =        nan minutes
	total time  =          1 seconds =       0.02 minutes
Total time:
	user time   =        nan seconds =        nan minutes
	system time =        nan seconds =        nan minutes
	total time  =          6 seconds =       0.10 minutes

*** tstart() called on iCarus
*** at Mon Jul  7 22:34:29 2025


         ------------------------------------------------------------
                                   SCF GRAD                          
                          Rob Parrish, Justin Turney,                
                       Andy Simmonett, and Alex Sokolov              
         ------------------------------------------------------------

  ==> Geometry <==

    Molecular point group: c1
    Full point group: C1

    Geometry (in Angstrom), charge = 1, multiplicity = 2:

       Center              X                  Y                   Z               Mass       
    ------------   -----------------  -----------------  -----------------  -----------------
         C            0.000000824072    -0.000001010328    -0.000001318552    12.000000000000
         H           -0.492677176290     1.006373301611     0.090288412349     1.007825032230
         H           -0.160587344338    -0.995275935826    -0.497304734417     1.007825032230
         H           -0.206796142021    -0.342676287542     1.050466008133     1.007825032230
         H            0.860059838577     0.331579932085    -0.643448367514     1.007825032230

  Nuclear repulsion =   13.058532383596562

  ==> Basis Set <==

  Basis Set: 6-31G(D)
    Blend: 6-31G(D)
    Number of shells: 14
    Number of basis functions: 23
    Number of Cartesian functions: 23
    Spherical Harmonics?: false
    Max angular momentum: 2

  ==> DFJKGrad: Density-Fitted SCF Gradients <==

    Gradient:                    1
    J tasked:                  Yes
    K tasked:                  Yes
    wK tasked:                  No
    OpenMP threads:             10
    Integrals threads:          10
    Memory [MiB]:             -422
    Schwarz Cutoff:          1E-12
    Fitting Condition:       1E-10

   => Auxiliary Basis Set <=

  Basis Set: (6-31G(D) AUX)
    Blend: CC-PVDZ-JKFIT
    Number of shells: 60
    Number of basis functions: 181
    Number of Cartesian functions: 181
    Spherical Harmonics?: false
    Max angular momentum: 3

  ==> DFT Potential <==

   => LibXC <=

    Version 7.0.0
    S. Lehtola, C. Steigemann, M. J.T. Oliveira, and M. A.L. Marques.,  SoftwareX 7, 1–5 (2018) (10.1016/j.softx.2017.11.002)

   => Composite Functional: B3LYP <= 

    B3LYP Hyb-GGA Exchange-Correlation Functional

    P. J. Stephens, F. J. Devlin, C. F. Chabalowski, and M. J. Frisch.,  J. Phys. Chem. 98, 11623 (1994) (10.1021/j100096a001)

    Deriv               =              1
    GGA                 =           TRUE
    Meta                =          FALSE

    Exchange Hybrid     =           TRUE
    MP2 Hybrid          =          FALSE

   => Exchange Functionals <=

    0.0800   Slater exchange
    0.7200         Becke 88

   => Exact (HF) Exchange <=

    0.2000               HF 

   => Correlation Functionals <=

    0.1900   Vosko, Wilk & Nusair (VWN5_RPA)
    0.8100   Lee, Yang & Parr

   => LibXC Density Thresholds  <==

    XC_HYB_GGA_XC_B3LYP:  1.00E-15 

   => Molecular Quadrature <=

    Radial Scheme          =       TREUTLER
    Pruning Scheme         =           NONE
    Nuclear Scheme         =       TREUTLER

    Blocking Scheme        =         OCTREE
    BS radius alpha        =              1
    Pruning alpha          =              1
    Radial Points          =             75
    Spherical Points       =            302
    Total Points           =         109380
    Total Blocks           =            869
    Max Points             =            254
    Max Functions          =             23
    Weights Tolerance      =       1.00E-15


  -Total Gradient:
     Atom            X                  Y                   Z
    ------   -----------------  -----------------  -----------------
       1        0.000000082201    -0.000000080004    -0.000000117864
       2       -0.000065537828    -0.000094187649    -0.000078765349
       3       -0.000097331208     0.000096925348    -0.000022606772
       4        0.000132314861     0.000030909754    -0.000030130290
       5        0.000030471944    -0.000033567536     0.000131620325


*** tstop() called on iCarus at Mon Jul  7 22:34:29 2025
Module time:
	user time   =        nan seconds =        nan minutes
	system time =        nan seconds =        nan minutes
	total time  =          0 seconds =       0.00 minutes
Total time:
	user time   =        nan seconds =        nan minutes
	system time =        nan seconds =        nan minutes
	total time  =          6 seconds =       0.10 minutes

    			-----------------------------------------

    			 OPTKING 3.0: for geometry optimizations 

    			     By R.A. King, Bethel University     

    			        with contributions from          

    			    A.V. Copan, J. Cayton, A. Heide      

    			-----------------------------------------

    
	                            ===> Fragment 1 <== 

	 Z (Atomic Numbers)          Masses                          Geom                  
	       6.000000            12.000000          0.00000156 -0.00000191 -0.00000249   
	       1.000000             1.007825         -0.93102493  1.90176992  0.17062037   
	       1.000000             1.007825         -0.30346610 -1.88079894 -0.93976975   
	       1.000000             1.007825         -0.39078807 -0.64756433  1.98509306   
	       1.000000             1.007825          1.62527755  0.62659526 -1.21594119   

	 - Coordinate -           - BOHR/RAD -       - ANG/DEG -
	 R(1,2)           =         2.124302           1.124132
	 R(1,3)           =         2.124300           1.124131
	 R(1,4)           =         2.124302           1.124132
	 R(1,5)           =         2.124301           1.124131
	 B(2,1,3)         =         2.442690         139.955803
	 B(2,1,4)         =         1.688287          96.731739
	 B(2,1,5)         =         1.688292          96.731989
	 B(3,1,4)         =         1.688293          96.732057
	 B(3,1,5)         =         1.688297          96.732305
	 B(4,1,5)         =         2.442690         139.955807
	 O(2,1,3,4)       =         0.654410          37.494931
	 O(2,1,3,5)       =        -0.654407         -37.494738
	 O(2,1,4,5)       =         1.221353          69.978390
	 O(3,1,2,4)       =        -0.654409         -37.494902
	 O(3,1,2,5)       =         0.654406          37.494710
	 O(3,1,4,5)       =        -1.221336         -69.977413
	 O(4,1,2,3)       =         1.221352          69.978288
	 O(4,1,2,5)       =        -0.654410         -37.494950
	 O(4,1,3,5)       =         0.654406          37.494705
	 O(5,1,2,3)       =        -1.221338         -69.977519
	 O(5,1,2,4)       =         0.654410          37.494927
	 O(5,1,3,4)       =        -0.654406         -37.494682


	        --- Internal Coordinate Step in ANG or DEG, aJ/ANG or AJ/DEG ---
	-------------------------------------------------------------------------------
	           Coordinate      Previous         Force          Change          New 
	           ----------      --------        ------          ------        ------
	               R(1,2)       1.12413        0.00051        0.00006       1.12419
	               R(1,3)       1.12413        0.00051        0.00006       1.12419
	               R(1,4)       1.12413        0.00051        0.00006       1.12419
	               R(1,5)       1.12413        0.00051        0.00006       1.12419
	             B(2,1,3)     139.95580        0.00000        0.08474     140.04055
	             B(2,1,4)      96.73174       -0.00000       -0.02740      96.70434
	             B(2,1,5)      96.73199       -0.00000       -0.02742      96.70457
	             B(3,1,4)      96.73206       -0.00000       -0.02743      96.70463
	             B(3,1,5)      96.73230       -0.00000       -0.02745      96.70486
	             B(4,1,5)     139.95581        0.00000        0.08474     140.04055
	           O(2,1,3,4)      37.49493       -0.00000       -0.06805      37.42688
	           O(2,1,3,5)     -37.49474        0.00000        0.06803     -37.42671
	           O(2,1,4,5)      69.97839        0.00000        0.04233      70.02073
	           O(3,1,2,4)     -37.49490        0.00000        0.06804     -37.42686
	           O(3,1,2,5)      37.49471       -0.00000       -0.06803      37.42668
	           O(3,1,4,5)     -69.97741       -0.00000       -0.04241     -70.01982
	           O(4,1,2,3)      69.97829        0.00000        0.04234      70.02063
	           O(4,1,2,5)     -37.49495        0.00000        0.06805     -37.42690
	           O(4,1,3,5)      37.49470       -0.00000       -0.06803      37.42668
	           O(5,1,2,3)     -69.97752       -0.00000       -0.04240     -70.01992
	           O(5,1,2,4)      37.49493       -0.00000       -0.06804      37.42688
	           O(5,1,3,4)     -37.49468        0.00000        0.06803     -37.42666
	-------------------------------------------------------------------------------

	                                 ==> Convergence Check <==                                  
    
	Measures of convergence in internal coordinates in au.
    
	Criteria marked as inactive (o), active & met (*), and active & unmet ( ).

	----------------------------------------------------------------------------------------------
	   Step    Total Energy     Delta E     Max Force     RMS Force      Max Disp      RMS Disp   
	----------------------------------------------------------------------------------------------
	  Convergence Criteria     1.00e-06 *    3.00e-04 *             o    1.20e-03 *             o
	----------------------------------------------------------------------------------------------
	     7     -40.05200148   -4.83e-06      6.19e-05 *    4.67e-05 o    1.48e-03      9.24e-04 o  ~
	----------------------------------------------------------------------------------------------

Next Geometry in Ang 
	Fragment 1 (Ang)

	    C   0.0000007612  -0.0000009416  -0.0000012298
	    H  -0.4920831402   1.0066810512   0.0907849238
	    H  -0.1598874614  -0.9956054651  -0.4969952129
	    H  -0.2076128576  -0.3427727715   1.0503326248
	    H   0.8595826980   0.3316981270  -0.6441211059


    Structure for next step:
    Molecular point group: c1
    Full point group: C1

    Geometry (in Angstrom), charge = 1, multiplicity = 2:

    C            0.000000761244    -0.000000941609    -0.000001229847
    H           -0.492083140238     1.006681051178     0.090784923826
    H           -0.159887461396    -0.995605465084    -0.496995212852
    H           -0.207612857634    -0.342772771453     1.050332624760
    H            0.859582698024     0.331698126969    -0.644121105886


Scratch directory: D:\BaiduSyncdisk\py\MS\psi4_scratch/
gradient() will perform analytic gradient computation.
   => Libint2 <=

    Primary   basis highest AM E, G, H:  6, 6, -
    Auxiliary basis highest AM E, G, H:  7, 7, -
    Onebody   basis highest AM E, G, H:  -, -, -
    Solid Harmonics ordering:            Gaussian

*** tstart() called on iCarus
*** at Mon Jul  7 22:34:29 2025

   => Loading Basis Set <=

    Name: 6-31G(D)
    Role: ORBITAL
    Keyword: BASIS
    atoms 1   entry C          line   111 file D:\anaconda3\envs\nistpy\Library\share\psi4\basis\6-31g_d_.gbs 
    atoms 2-5 entry H          line    44 file D:\anaconda3\envs\nistpy\Library\share\psi4\basis\6-31g_d_.gbs 


         ---------------------------------------------------------
                                   SCF
               by Justin Turney, Rob Parrish, Andy Simmonett
                          and Daniel G. A. Smith
                              UKS Reference
                       10 Threads,   -562 MiB Core
         ---------------------------------------------------------

  ==> Geometry <==

    Molecular point group: c1
    Full point group: C1

    Geometry (in Angstrom), charge = 1, multiplicity = 2:

       Center              X                  Y                   Z               Mass       
    ------------   -----------------  -----------------  -----------------  -----------------
         C            0.000000761244    -0.000000941609    -0.000001229847    12.000000000000
         H           -0.492083140238     1.006681051178     0.090784923826     1.007825032230
         H           -0.159887461396    -0.995605465084    -0.496995212852     1.007825032230
         H           -0.207612857634    -0.342772771453     1.050332624760     1.007825032230
         H            0.859582698024     0.331698126969    -0.644121105886     1.007825032230

  Running in c1 symmetry.

  Rotational constants: A =      5.92582  B =      5.92582  C =      3.74620 [cm^-1]
  Rotational constants: A = 177651.46640  B = 177651.46491  C = 112308.20669 [MHz]
  Nuclear repulsion =   13.058023797269321

  Charge       = 1
  Multiplicity = 2
  Electrons    = 9
  Nalpha       = 5
  Nbeta        = 4

  ==> Algorithm <==

  SCF Algorithm Type is DF.
  DIIS enabled.
  MOM disabled.
  Fractional occupation disabled.
  Guess Type is READ.
  Energy threshold   = 1.00e-08
  Density threshold  = 1.00e-08
  Integral threshold = 1.00e-12

  ==> Primary Basis <==

  Basis Set: 6-31G(D)
    Blend: 6-31G(D)
    Number of shells: 14
    Number of basis functions: 23
    Number of Cartesian functions: 23
    Spherical Harmonics?: false
    Max angular momentum: 2

  ==> DFT Potential <==

   => LibXC <=

    Version 7.0.0
    S. Lehtola, C. Steigemann, M. J.T. Oliveira, and M. A.L. Marques.,  SoftwareX 7, 1–5 (2018) (10.1016/j.softx.2017.11.002)

   => Composite Functional: B3LYP <= 

    B3LYP Hyb-GGA Exchange-Correlation Functional

    P. J. Stephens, F. J. Devlin, C. F. Chabalowski, and M. J. Frisch.,  J. Phys. Chem. 98, 11623 (1994) (10.1021/j100096a001)

    Deriv               =              1
    GGA                 =           TRUE
    Meta                =          FALSE

    Exchange Hybrid     =           TRUE
    MP2 Hybrid          =          FALSE

   => Exchange Functionals <=

    0.0800   Slater exchange
    0.7200         Becke 88

   => Exact (HF) Exchange <=

    0.2000               HF 

   => Correlation Functionals <=

    0.1900   Vosko, Wilk & Nusair (VWN5_RPA)
    0.8100   Lee, Yang & Parr

   => LibXC Density Thresholds  <==

    XC_HYB_GGA_XC_B3LYP:  1.00E-15 

   => Molecular Quadrature <=

    Radial Scheme          =       TREUTLER
    Pruning Scheme         =           NONE
    Nuclear Scheme         =       TREUTLER

    Blocking Scheme        =         OCTREE
    BS radius alpha        =              1
    Pruning alpha          =              1
    Radial Points          =             75
    Spherical Points       =            302
    Total Points           =         109408
    Total Blocks           =            873
    Max Points             =            256
    Max Functions          =             23
    Weights Tolerance      =       1.00E-15

   => Loading Basis Set <=

    Name: (6-31G(D) AUX)
    Role: JKFIT
    Keyword: DF_BASIS_SCF
    atoms 1   entry C          line   121 file D:\anaconda3\envs\nistpy\Library\share\psi4\basis\cc-pvdz-jkfit.gbs 
    atoms 2-5 entry H          line    51 file D:\anaconda3\envs\nistpy\Library\share\psi4\basis\cc-pvdz-jkfit.gbs 

   => Loading Basis Set <=

    Name: 6-31G(D)
    Role: ORBITAL
    Keyword: BASIS
    atoms 1   entry C          line   111 file D:\anaconda3\envs\nistpy\Library\share\psi4\basis\6-31g_d_.gbs 
    atoms 2-5 entry H          line    44 file D:\anaconda3\envs\nistpy\Library\share\psi4\basis\6-31g_d_.gbs 

  Reading orbitals from file D:\BaiduSyncdisk\py\MS\psi4_scratch/psi4_calculation.default.33932.180.npy, no projection.

  ==> Integral Setup <==

  DFHelper Memory: AOs need 0.001 GiB; user supplied 5.515 GiB. 
  Using in-core AOs.

  ==> MemDFJK: Density-Fitted J/K Matrices <==

    J tasked:                   Yes
    K tasked:                   Yes
    wK tasked:                   No
    OpenMP threads:              10
    Memory [MiB]:              5647
    Algorithm:                 Core
    Schwarz Cutoff:           1E-12
    Mask sparsity (%):       0.0000
    Fitting Condition:        1E-10

   => Auxiliary Basis Set <=

  Basis Set: (6-31G(D) AUX)
    Blend: CC-PVDZ-JKFIT
    Number of shells: 60
    Number of basis functions: 181
    Number of Cartesian functions: 181
    Spherical Harmonics?: false
    Max angular momentum: 3

  Cached 100.0% of DFT collocation blocks in 0.073 [GiB].

  Minimum eigenvalue in the overlap matrix is 2.3854368990E-02.
  Reciprocal condition number of the overlap matrix is 4.3286898074E-03.
    Using symmetric orthogonalization.

  ==> Pre-Iterations <==

  SCF Guess: Orbitals guess was supplied from a previous computation.

   -------------------------------------------------------
    Irrep   Nso     Nmo     Nalpha   Nbeta   Ndocc  Nsocc
   -------------------------------------------------------
     A         23      23       5       4       4       1
   -------------------------------------------------------
    Total      23      23       5       4       4       1
   -------------------------------------------------------

  ==> Iterations <==

                           Total Energy        Delta E     RMS |[F,P]|

   @DF-UKS iter   0:   -40.05186930462223   -4.00519e+01   1.23856e-05 
   @DF-UKS iter   1:   -40.05200154594463   -1.32241e-04   3.94525e-06 DIIS
   @DF-UKS iter   2:   -40.05200154753142   -1.58679e-09   3.51212e-06 DIIS
   @DF-UKS iter   3:   -40.05200155101836   -3.48695e-09   3.22104e-07 DIIS
   @DF-UKS iter   4:   -40.05200155108473   -6.63647e-11   7.17849e-08 DIIS
   @DF-UKS iter   5:   -40.05200155108857   -3.83693e-12   9.12745e-09 DIIS
  Energy and wave function converged.


  ==> Post-Iterations <==

   Electrons on quadrature grid:
      Nalpha   =    4.9999980980 ; deviation = -1.902e-06
      Nbeta    =    3.9999981355 ; deviation = -1.864e-06
      Ntotal   =    8.9999962335 ; deviation = -3.766e-06 

   @Spin Contamination Metric:   1.125767173E-03
   @S^2 Expected:                7.500000000E-01
   @S^2 Observed:                7.511257672E-01
   @S   Expected:                5.000000000E-01
   @S   Observed:                5.000000000E-01

    Orbital Energies [Eh]
    ---------------------

    Alpha Occupied:                                                       

       1A    -10.569385     2A     -1.072695     3A     -0.792139  
       4A     -0.792139     5A     -0.676961  

    Alpha Virtual:                                                        

       6A     -0.248642     7A     -0.187511     8A     -0.100413  
       9A     -0.100413    10A      0.193089    11A      0.299508  
      12A      0.299508    13A      0.458546    14A      0.484254  
      15A      0.573538    16A      0.573538    17A      0.647007  
      18A      1.251420    19A      1.530407    20A      1.609449  
      21A      1.609449    22A      1.986431    23A      3.730494  

    Beta Occupied:                                                        

       1A    -10.559638     2A     -1.029755     3A     -0.767835  
       4A     -0.767835  

    Beta Virtual:                                                         

       5A     -0.531931     6A     -0.215508     7A     -0.169349  
       8A     -0.087947     9A     -0.087947    10A      0.237698  
      11A      0.311784    12A      0.311784    13A      0.484846  
      14A      0.525352    15A      0.597744    16A      0.597744  
      17A      0.668288    18A      1.257057    19A      1.601390  
      20A      1.658335    21A      1.658335    22A      1.999355  
      23A      3.758319  

    Final Occupation by Irrep:
              A 
    DOCC [     4 ]
    SOCC [     1 ]
    NA   [     5 ]
    NB   [     4 ]

  @DF-UKS Final Energy:   -40.05200155108857

   => Energetics <=

    Nuclear Repulsion Energy =             13.0580237972693212
    One-Electron Energy =                 -75.3963918414915497
    Two-Electron Energy =                  27.5581639049237985
    DFT Exchange-Correlation Energy =      -5.2717974117901312
    Empirical Dispersion Energy =           0.0000000000000000
    VV10 Nonlocal Energy =                  0.0000000000000000
    Total Energy =                        -40.0520015510885656

  UHF NO Occupations:
  HONO-2 :    3  A 1.9998777
  HONO-1 :    4  A 1.9996818
  HONO-0 :    5  A 1.0000000
  LUNO+0 :    6  A 0.0003182
  LUNO+1 :    7  A 0.0001223
  LUNO+2 :    8  A 0.0001223
  LUNO+3 :    9  A 0.0000002


Computation Completed


Properties will be evaluated at   0.000000,   0.000000,   0.000000 [a0]

Properties computed using the SCF density matrix


 Multipole Moments:

 ------------------------------------------------------------------------------------
     Multipole            Electronic (a.u.)      Nuclear  (a.u.)        Total (a.u.)
 ------------------------------------------------------------------------------------

 L = 1.  Multiply by 2.5417464519 to convert [e a0] to [Debye]
 Dipole X            :         -0.0000088            0.0000072           -0.0000016
 Dipole Y            :          0.0000109           -0.0000089            0.0000020
 Dipole Z            :          0.0000143           -0.0000116            0.0000027
 Magnitude           :                                                    0.0000038

 ------------------------------------------------------------------------------------

*** tstop() called on iCarus at Mon Jul  7 22:34:29 2025
Module time:
	user time   =        nan seconds =        nan minutes
	system time =        nan seconds =        nan minutes
	total time  =          0 seconds =       0.00 minutes
Total time:
	user time   =        nan seconds =        nan minutes
	system time =        nan seconds =        nan minutes
	total time  =          6 seconds =       0.10 minutes

*** tstart() called on iCarus
*** at Mon Jul  7 22:34:29 2025


         ------------------------------------------------------------
                                   SCF GRAD                          
                          Rob Parrish, Justin Turney,                
                       Andy Simmonett, and Alex Sokolov              
         ------------------------------------------------------------

  ==> Geometry <==

    Molecular point group: c1
    Full point group: C1

    Geometry (in Angstrom), charge = 1, multiplicity = 2:

       Center              X                  Y                   Z               Mass       
    ------------   -----------------  -----------------  -----------------  -----------------
         C            0.000000761244    -0.000000941609    -0.000001229847    12.000000000000
         H           -0.492083140238     1.006681051178     0.090784923826     1.007825032230
         H           -0.159887461396    -0.995605465084    -0.496995212852     1.007825032230
         H           -0.207612857634    -0.342772771453     1.050332624760     1.007825032230
         H            0.859582698024     0.331698126969    -0.644121105886     1.007825032230

  Nuclear repulsion =   13.058023797269321

  ==> Basis Set <==

  Basis Set: 6-31G(D)
    Blend: 6-31G(D)
    Number of shells: 14
    Number of basis functions: 23
    Number of Cartesian functions: 23
    Spherical Harmonics?: false
    Max angular momentum: 2

  ==> DFJKGrad: Density-Fitted SCF Gradients <==

    Gradient:                    1
    J tasked:                  Yes
    K tasked:                  Yes
    wK tasked:                  No
    OpenMP threads:             10
    Integrals threads:          10
    Memory [MiB]:             -422
    Schwarz Cutoff:          1E-12
    Fitting Condition:       1E-10

   => Auxiliary Basis Set <=

  Basis Set: (6-31G(D) AUX)
    Blend: CC-PVDZ-JKFIT
    Number of shells: 60
    Number of basis functions: 181
    Number of Cartesian functions: 181
    Spherical Harmonics?: false
    Max angular momentum: 3

  ==> DFT Potential <==

   => LibXC <=

    Version 7.0.0
    S. Lehtola, C. Steigemann, M. J.T. Oliveira, and M. A.L. Marques.,  SoftwareX 7, 1–5 (2018) (10.1016/j.softx.2017.11.002)

   => Composite Functional: B3LYP <= 

    B3LYP Hyb-GGA Exchange-Correlation Functional

    P. J. Stephens, F. J. Devlin, C. F. Chabalowski, and M. J. Frisch.,  J. Phys. Chem. 98, 11623 (1994) (10.1021/j100096a001)

    Deriv               =              1
    GGA                 =           TRUE
    Meta                =          FALSE

    Exchange Hybrid     =           TRUE
    MP2 Hybrid          =          FALSE

   => Exchange Functionals <=

    0.0800   Slater exchange
    0.7200         Becke 88

   => Exact (HF) Exchange <=

    0.2000               HF 

   => Correlation Functionals <=

    0.1900   Vosko, Wilk & Nusair (VWN5_RPA)
    0.8100   Lee, Yang & Parr

   => LibXC Density Thresholds  <==

    XC_HYB_GGA_XC_B3LYP:  1.00E-15 

   => Molecular Quadrature <=

    Radial Scheme          =       TREUTLER
    Pruning Scheme         =           NONE
    Nuclear Scheme         =       TREUTLER

    Blocking Scheme        =         OCTREE
    BS radius alpha        =              1
    Pruning alpha          =              1
    Radial Points          =             75
    Spherical Points       =            302
    Total Points           =         109408
    Total Blocks           =            873
    Max Points             =            256
    Max Functions          =             23
    Weights Tolerance      =       1.00E-15


  -Total Gradient:
     Atom            X                  Y                   Z
    ------   -----------------  -----------------  -----------------
       1        0.000000083304    -0.000000097245    -0.000000129129
       2       -0.000007398645    -0.000020237192    -0.000012319478
       3       -0.000013890507     0.000020602451    -0.000000837522
       4        0.000021474627     0.000006455273    -0.000010645111
       5       -0.000000268900    -0.000006723171     0.000023931436


*** tstop() called on iCarus at Mon Jul  7 22:34:29 2025
Module time:
	user time   =        nan seconds =        nan minutes
	system time =        nan seconds =        nan minutes
	total time  =          0 seconds =       0.00 minutes
Total time:
	user time   =        nan seconds =        nan minutes
	system time =        nan seconds =        nan minutes
	total time  =          6 seconds =       0.10 minutes

    			-----------------------------------------

    			 OPTKING 3.0: for geometry optimizations 

    			     By R.A. King, Bethel University     

    			        with contributions from          

    			    A.V. Copan, J. Cayton, A. Heide      

    			-----------------------------------------

    
	                            ===> Fragment 1 <== 

	 Z (Atomic Numbers)          Masses                          Geom                  
	       6.000000            12.000000          0.00000144 -0.00000178 -0.00000232   
	       1.000000             1.007825         -0.92990237  1.90235148  0.17155864   
	       1.000000             1.007825         -0.30214351 -1.88142166 -0.93918484   
	       1.000000             1.007825         -0.39233144 -0.64774666  1.98484100   
	       1.000000             1.007825          1.62437588  0.62681862 -1.21721248   

	 - Coordinate -           - BOHR/RAD -       - ANG/DEG -
	 R(1,2)           =         2.124406           1.124187
	 R(1,3)           =         2.124405           1.124187
	 R(1,4)           =         2.124406           1.124187
	 R(1,5)           =         2.124405           1.124187
	 B(2,1,3)         =         2.444169         140.040546
	 B(2,1,4)         =         1.687809          96.704336
	 B(2,1,5)         =         1.687813          96.704567
	 B(3,1,4)         =         1.687814          96.704628
	 B(3,1,5)         =         1.687818          96.704859
	 B(4,1,5)         =         2.444169         140.040548
	 O(2,1,3,4)       =         0.653222          37.426884
	 O(2,1,3,5)       =        -0.653219         -37.426707
	 O(2,1,4,5)       =         1.222092          70.020725
	 O(3,1,2,4)       =        -0.653222         -37.426858
	 O(3,1,2,5)       =         0.653219          37.426680
	 O(3,1,4,5)       =        -1.222076         -70.019821
	 O(4,1,2,3)       =         1.222091          70.020631
	 O(4,1,2,5)       =        -0.653223         -37.426903
	 O(4,1,3,5)       =         0.653219          37.426678
	 O(5,1,2,3)       =        -1.222078         -70.019917
	 O(5,1,2,4)       =         0.653222          37.426882
	 O(5,1,3,4)       =        -0.653218         -37.426657


	        --- Internal Coordinate Step in ANG or DEG, aJ/ANG or AJ/DEG ---
	-------------------------------------------------------------------------------
	           Coordinate      Previous         Force          Change          New 
	           ----------      --------        ------          ------        ------
	               R(1,2)       1.12419        0.00013        0.00002       1.12421
	               R(1,3)       1.12419        0.00013        0.00002       1.12421
	               R(1,4)       1.12419        0.00013        0.00002       1.12421
	               R(1,5)       1.12419        0.00013        0.00002       1.12421
	             B(2,1,3)     140.04055        0.00000        0.01421     140.05475
	             B(2,1,4)      96.70434       -0.00000       -0.00457      96.69976
	             B(2,1,5)      96.70457       -0.00000       -0.00459      96.69998
	             B(3,1,4)      96.70463       -0.00000       -0.00459      96.70003
	             B(3,1,5)      96.70486       -0.00000       -0.00461      96.70025
	             B(4,1,5)     140.04055        0.00000        0.01421     140.05475
	           O(2,1,3,4)      37.42688       -0.00000       -0.01142      37.41546
	           O(2,1,3,5)     -37.42671        0.00000        0.01141     -37.41530
	           O(2,1,4,5)      70.02073        0.00000        0.00707      70.02780
	           O(3,1,2,4)     -37.42686        0.00000        0.01142     -37.41544
	           O(3,1,2,5)      37.42668       -0.00000       -0.01141      37.41527
	           O(3,1,4,5)     -70.01982       -0.00000       -0.00713     -70.02696
	           O(4,1,2,3)      70.02063        0.00000        0.00708      70.02771
	           O(4,1,2,5)     -37.42690        0.00000        0.01142     -37.41548
	           O(4,1,3,5)      37.42668       -0.00000       -0.01141      37.41527
	           O(5,1,2,3)     -70.01992       -0.00000       -0.00713     -70.02704
	           O(5,1,2,4)      37.42688       -0.00000       -0.01142      37.41546
	           O(5,1,3,4)     -37.42666        0.00000        0.01140     -37.41525
	-------------------------------------------------------------------------------

	                                 ==> Convergence Check <==                                  
    
	Measures of convergence in internal coordinates in au.
    
	Criteria marked as inactive (o), active & met (*), and active & unmet ( ).

	----------------------------------------------------------------------------------------------
	   Step    Total Energy     Delta E     Max Force     RMS Force      Max Disp      RMS Disp   
	----------------------------------------------------------------------------------------------
	  Convergence Criteria     1.00e-06 *    3.00e-04 *             o    1.20e-03 *             o
	----------------------------------------------------------------------------------------------
	     8     -40.05200155   -6.67e-08 *    1.59e-05 *    8.99e-06 o    2.48e-04 *    1.56e-04 o  ~
	----------------------------------------------------------------------------------------------

Next Geometry in Ang 
	Fragment 1 (Ang)

	    C   0.0000007080  -0.0000008785  -0.0000011481
	    H  -0.4919880855   1.0067419614   0.0908689408
	    H  -0.1597715350  -0.9956698124  -0.4969479060
	    H  -0.2077516661  -0.3427921822   1.0503199427
	    H   0.8595105786   0.3317209117  -0.6442398294


    Final optimized geometry and variables:
    Molecular point group: c1
    Full point group: C1

    Geometry (in Angstrom), charge = 1, multiplicity = 2:

    C            0.000000761244    -0.000000941609    -0.000001229847
    H           -0.492083140238     1.006681051178     0.090784923826
    H           -0.159887461396    -0.995605465084    -0.496995212852
    H           -0.207612857634    -0.342772771453     1.050332624760
    H            0.859582698024     0.331698126969    -0.644121105886


==================================================
=== PSI4计算完成 ===
绝热电离能: 12.692 eV
中性分子能量: -40.51840708 Hartree
阳离子能量: -40.05200155 Hartree
完成时间: 2025-07-07 22:34:29
