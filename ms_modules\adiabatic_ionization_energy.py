#!/usr/bin/env python3
"""
绝热电离能计算程序
使用PSI4和RDKit进行分子构象搜索和电离能计算

作者: AI Assistant
版本: 1.0
"""

import os
import sys
import numpy as np
from typing import List, Tuple, Optional
import tempfile
import shutil

try:
    import psi4
    import rdkit
    from rdkit import Chem
    from rdkit.Chem import AllChem, rdMolDescriptors
    from rdkit.Chem.rdForceFieldHelpers import UFFOptimizeMolecule, MMFFOptimizeMolecule
except ImportError as e:
    print(f"导入错误: {e}")
    print("请确保已安装 psi4 和 rdkit")
    print("安装命令:")
    print("conda install -c conda-forge psi4 rdkit")
    sys.exit(1)


class AdiabaticIonizationEnergyCalculator:
    """绝热电离能计算器"""
    
    def __init__(self):
        """初始化计算器"""
        self.temp_dir = None
        self.psi4_scratch_dir = None
        
        # 设置PSI4全局配置
        self._setup_psi4_environment()
        
    def __enter__(self):
        """上下文管理器入口"""
        self.temp_dir = tempfile.mkdtemp()
        return self
        
    def __exit__(self, exc_type, exc_val, exc_tb):
        """上下文管理器出口，清理临时文件"""
        if self.temp_dir and os.path.exists(self.temp_dir):
            shutil.rmtree(self.temp_dir)
        # 清理PSI4临时文件
        if self.psi4_scratch_dir and os.path.exists(self.psi4_scratch_dir):
            try:
                shutil.rmtree(self.psi4_scratch_dir)
            except:
                pass  # 忽略清理错误
    
    def _setup_psi4_environment(self):
        """设置PSI4环境"""
        try:
            # 创建PSI4工作目录
            self.psi4_scratch_dir = os.path.join(os.getcwd(), "psi4_scratch")
            os.makedirs(self.psi4_scratch_dir, exist_ok=True)
            
            # 设置PSI4选项
            psi4.core.set_num_threads(4)  # 默认单线程
            psi4.set_memory('8GB')  # 默认内存
            
            # 设置输出级别（减少输出）
            psi4.core.set_output_file(os.path.join(self.psi4_scratch_dir, "psi4.out"), False)
            
        except Exception as e:
            print(f"PSI4环境设置警告: {e}")
    
    def generate_conformers(self, smiles: str, num_conformers: int = None) -> List[Chem.Mol]:
        """
        从SMILES生成分子构象
        
        参数:
            smiles: 分子的SMILES字符串
            num_conformers: 生成的构象数量，如果为None则自动确定
            
        返回:
            构象分子列表
        """
        print(f"正在处理SMILES: {smiles}")
        
        # 从SMILES创建分子
        mol = Chem.MolFromSmiles(smiles)
        if mol is None:
            raise ValueError(f"无效的SMILES字符串: {smiles}")
        
        # 添加氢原子
        mol = Chem.AddHs(mol)
        
        # 计算分子的旋转键数量来估计构象数
        num_rotatable_bonds = rdMolDescriptors.CalcNumRotatableBonds(mol)
        
        # 根据旋转键数量确定构象数量
        if num_conformers is None:
            if num_rotatable_bonds == 0:
                num_conformers = 1
            elif num_rotatable_bonds <= 6:
                num_conformers = num_rotatable_bonds**3
            elif num_rotatable_bonds <= 9:
                num_conformers = 1000
            else:
                num_conformers = 1500
        
        print(f"分子有 {num_rotatable_bonds} 个旋转键，将生成 {num_conformers} 个构象")
        
        # 生成构象
        conformer_ids = AllChem.EmbedMultipleConfs(
            mol, 
            numConfs=num_conformers,
            randomSeed=42,
            useExpTorsionAnglePrefs=True,
            useBasicKnowledge=True
        )
        
        if len(conformer_ids) == 0:
            raise RuntimeError("无法生成分子构象")
        
        print(f"成功生成 {len(conformer_ids)} 个构象")
        
        # 使用MMFF力场优化构象
        conformers = []
        energies = []
        
        for conf_id in conformer_ids:
            try:
                # 尝试使用MMFF力场
                ff_result = MMFFOptimizeMolecule(mol, confId=conf_id)
                if ff_result == 0:  # 优化成功
                    # 计算MMFF能量
                    try:
                        mmff_props = AllChem.MMFFGetMoleculeProperties(mol)
                        ff = AllChem.MMFFGetMoleculeForceField(mol, mmff_props, confId=conf_id)
                        if ff is not None:
                            energy = ff.CalcEnergy()
                            conformers.append((mol, conf_id, energy))
                            energies.append(energy)
                        else:
                            # 如果MMFF失败，尝试UFF
                            UFFOptimizeMolecule(mol, confId=conf_id)
                            ff = AllChem.UFFGetMoleculeForceField(mol, confId=conf_id)
                            if ff is not None:
                                energy = ff.CalcEnergy()
                                conformers.append((mol, conf_id, energy))
                                energies.append(energy)
                    except Exception:
                        # 如果MMFF失败，尝试UFF
                        UFFOptimizeMolecule(mol, confId=conf_id)
                        ff = AllChem.UFFGetMoleculeForceField(mol, confId=conf_id)
                        if ff is not None:
                            energy = ff.CalcEnergy()
                            conformers.append((mol, conf_id, energy))
                            energies.append(energy)
                else:
                    # MMFF优化失败，尝试UFF
                    uff_result = UFFOptimizeMolecule(mol, confId=conf_id)
                    if uff_result == 0:
                        ff = AllChem.UFFGetMoleculeForceField(mol, confId=conf_id)
                        if ff is not None:
                            energy = ff.CalcEnergy()
                            conformers.append((mol, conf_id, energy))
                            energies.append(energy)
            except Exception as e:
                print(f"构象 {conf_id} 优化失败: {e}")
                continue
        
        if not conformers:
            raise RuntimeError("所有构象优化都失败了")
        
        # 按能量排序
        conformers.sort(key=lambda x: x[2])
        
        print(f"成功优化 {len(conformers)} 个构象")
        print(f"能量范围: {min(energies):.2f} - {max(energies):.2f} kcal/mol")
        
        # 返回分子对象列表
        result_mols = []
        for mol_conf, conf_id, energy in conformers:
            # 创建新的分子对象，只包含该构象
            new_mol = Chem.Mol(mol_conf)
            new_mol.RemoveAllConformers()
            new_mol.AddConformer(mol_conf.GetConformer(conf_id), assignId=True)
            result_mols.append(new_mol)
        
        return result_mols
    
    def mol_to_xyz(self, mol: Chem.Mol, conf_id: int = 0) -> str:
        """
        将RDKit分子转换为XYZ格式字符串
        
        参数:
            mol: RDKit分子对象
            conf_id: 构象ID
            
        返回:
            XYZ格式字符串
        """
        conf = mol.GetConformer(conf_id)
        xyz_lines = []
        
        for i, atom in enumerate(mol.GetAtoms()):
            pos = conf.GetAtomPosition(i)
            symbol = atom.GetSymbol()
            xyz_lines.append(f"{symbol:2s} {pos.x:12.6f} {pos.y:12.6f} {pos.z:12.6f}")
        
        return "\n".join(xyz_lines)
    
    def optimize_with_psi4(self, mol: Chem.Mol, charge: int, multiplicity: int,
                          method: str, basis: str, memory: str, nthreads: int) -> Tuple[float, str]:
        """
        使用PSI4优化分子几何并计算能量
        
        参数:
            mol: RDKit分子对象
            charge: 电荷
            multiplicity: 自旋多重度
            method: 计算方法
            basis: 基组
            memory: 内存设置
            nthreads: 线程数
            
        返回:
            (能量, 优化后的XYZ坐标)
        """
        # 设置PSI4参数
        psi4.set_memory(memory)
        psi4.set_num_threads(nthreads)
        
        # 确保PSI4工作目录存在并设置
        if self.psi4_scratch_dir:
            os.makedirs(self.psi4_scratch_dir, exist_ok=True)
            try:
                psi4.core.IOManager.shared_object().set_default_path(self.psi4_scratch_dir)
            except Exception:
                pass  # 忽略设置错误
        
        # 获取分子的XYZ坐标
        xyz_coords = self.mol_to_xyz(mol)
        
        # 创建PSI4分子对象
        mol_string = f"{charge} {multiplicity}\n{xyz_coords}\nno_reorient\nno_com"
        psi4_mol = psi4.geometry(mol_string)
        
        # 进行几何优化
        try:
            # 根据多重度设置参考方法
            if multiplicity == 1:
                psi4.set_options({'reference': 'rhf'})
            else:
                psi4.set_options({'reference': 'uhf'})
            
            energy = psi4.optimize(f"{method}/{basis}", molecule=psi4_mol)
            
            # 获取优化后的坐标
            optimized_xyz = psi4_mol.save_string_xyz()
            
            return energy, optimized_xyz
            
        except Exception as e:
            raise RuntimeError(f"PSI4优化失败: {e}")
    
    def calculate_adiabatic_ionization_energy(self, 
                                            smiles: str,
                                            nthreads: int = 4,
                                            memory: str = "2GB",
                                            low_level_method: str = "b3lyp",
                                            low_level_basis: str = "6-31g(d)",
                                            high_level_method: str = "m062x", 
                                            high_level_basis: str = "aug-cc-pvtz",
                                            max_conformers: int = 10,
                                            skip_low_level_optimization: bool = False,
                                            scratch_dir: str = None) -> dict:
        """
        计算分子的绝热电离能
        
        参数:
            smiles: 分子SMILES字符串
            nthreads: CPU线程数
            memory: 内存大小
            low_level_method: 低精度计算方法
            low_level_basis: 低精度基组
            high_level_method: 高精度计算方法  
            high_level_basis: 高精度基组
            max_conformers: 最大构象数量
            skip_low_level_optimization: 是否跳过低精度构象优化，直接使用力场最优构象
            scratch_dir: PSI4计算缓存文件目录，None则使用默认目录
            
        返回:
            包含计算结果的字典
        """
        print("="*60)
        print("开始绝热电离能计算")
        print("="*60)
        
        results = {
            'smiles': smiles,
            'low_level_method': low_level_method,
            'low_level_basis': low_level_basis,
            'high_level_method': high_level_method,
            'high_level_basis': high_level_basis,
            'conformers_generated': 0,
            'conformers_optimized': 0,
            'neutral_energy': None,
            'cation_energy': None,
            'ionization_energy_hartree': None,
            'ionization_energy_ev': None,
            'ionization_energy_kcal_mol': None,
            'success': False,
            'error_message': None
        }
        
        try:
            # 设置PSI4缓存目录
            if scratch_dir is not None:
                # 使用用户指定的目录
                psi4_scratch_path = os.path.abspath(scratch_dir)
                os.makedirs(psi4_scratch_path, exist_ok=True)
                self.psi4_scratch_dir = psi4_scratch_path
                print(f"PSI4缓存目录设置为: {psi4_scratch_path}")
            else:
                # 默认使用程序目录下的psi4_scratch目录（而不是__init__中设置的）
                default_scratch_dir = os.path.join(os.getcwd(), "psi4_scratch")
                os.makedirs(default_scratch_dir, exist_ok=True)
                self.psi4_scratch_dir = default_scratch_dir
                print(f"使用默认PSI4缓存目录: {default_scratch_dir}")
                
                # 设置PSI4的工作目录和输出文件
            try:
                psi4.core.IOManager.shared_object().set_default_path(self.psi4_scratch_dir)
                psi4.core.set_output_file(os.path.join(self.psi4_scratch_dir, "psi4_calculation.out"), False)
            except Exception as e:
                print(f"PSI4缓存目录设置警告: {e}")
            
            # 步骤1: 生成构象
            print("\n步骤1: 生成分子构象...")
            conformers = self.generate_conformers(smiles)
            results['conformers_generated'] = len(conformers)
            
            # 限制构象数量
            conformers = conformers[:max_conformers]
            print(f"选择前 {len(conformers)} 个构象进行计算")
            
            if skip_low_level_optimization:
                # 跳过低精度优化，直接使用力场最优构象
                print(f"\n步骤2: 跳过低精度优化，直接使用力场最优构象...")
                best_mol = conformers[0]  # 力场能量最低的构象
                best_xyz = self.mol_to_xyz(best_mol)
                results['conformers_optimized'] = 1
                print(f"使用力场最优构象（构象1）进行高精度计算")
            else:
                # 步骤2: 低精度优化构象
                print(f"\n步骤2: 使用 {low_level_method}/{low_level_basis} 优化构象...")
                optimized_conformers = []
                
                for i, mol in enumerate(conformers):
                    try:
                        print(f"  优化构象 {i+1}/{len(conformers)}...")
                        energy, xyz = self.optimize_with_psi4(
                            mol, charge=0, multiplicity=1,
                            method=low_level_method, basis=low_level_basis,
                            memory=memory, nthreads=nthreads
                        )
                        optimized_conformers.append((energy, xyz, i))
                        print(f"    能量: {energy:.6f} Hartree")
                    except Exception as e:
                        print(f"    构象 {i+1} 优化失败: {e}")
                        continue
                
                if not optimized_conformers:
                    raise RuntimeError("所有构象的低精度优化都失败了")
                
                results['conformers_optimized'] = len(optimized_conformers)
                
                # 选择能量最低的构象
                optimized_conformers.sort(key=lambda x: x[0])
                best_energy, best_xyz, best_idx = optimized_conformers[0]
                
                print(f"\n选择能量最低的构象 {best_idx+1}，能量: {best_energy:.6f} Hartree")
            
            # 步骤3: 高精度计算中性分子
            print(f"\n步骤3: 使用 {high_level_method}/{high_level_basis} 计算中性分子...")
            
            # 从优化后的XYZ创建PSI4分子对象
            if skip_low_level_optimization:
                # 跳过低精度优化时，best_xyz是直接从RDKit生成的坐标，不包含电荷和多重度
                best_coords = best_xyz
            else:
                # 低精度优化后，best_xyz来自save_string_xyz()，包含电荷和多重度，需要提取坐标部分
                xyz_lines = best_xyz.strip().split('\n')
                if len(xyz_lines) > 1:
                    # 跳过第一行（电荷和多重度），只取坐标行
                    coord_lines = xyz_lines[1:]
                    best_coords = '\n'.join(coord_lines)
                else:
                    best_coords = best_xyz
            
            neutral_mol_string = f"0 1\n{best_coords}\nno_reorient\nno_com"
            neutral_psi4_mol = psi4.geometry(neutral_mol_string)
            
            # 确保中性分子使用RHF方法
            psi4.set_options({'reference': 'rhf'})
            neutral_energy = psi4.optimize(f"{high_level_method}/{high_level_basis}", 
                                         molecule=neutral_psi4_mol)
            results['neutral_energy'] = neutral_energy
            print(f"中性分子能量: {neutral_energy:.8f} Hartree")
            
            # 步骤4: 高精度计算阳离子
            print(f"\n步骤4: 使用 {high_level_method}/{high_level_basis} 计算阳离子...")
            
            # 获取中性分子优化后的坐标
            neutral_optimized_xyz = neutral_psi4_mol.save_string_xyz()
            # save_string_xyz()返回的格式是: "charge multiplicity\n坐标行..."
            # 需要提取坐标部分（跳过第一行的电荷和多重度）
            xyz_lines = neutral_optimized_xyz.strip().split('\n')
            if len(xyz_lines) > 1:
                # 跳过第一行（电荷和多重度），只取坐标行
                coord_lines = xyz_lines[1:]
                neutral_coords = '\n'.join(coord_lines)
            else:
                neutral_coords = neutral_optimized_xyz
            
            # 创建阳离子分子对象（电荷+1，自旋多重度2）
            cation_mol_string = f"1 2\n{neutral_coords}\nno_reorient\nno_com"
            cation_psi4_mol = psi4.geometry(cation_mol_string)
            
            # 对于阳离子（自旋多重度=2），需要使用UHF方法
            psi4.set_options({'reference': 'uhf'})
            cation_energy = psi4.optimize(f"{high_level_method}/{high_level_basis}",
                                        molecule=cation_psi4_mol)
            # 恢复为RHF方法（用于中性分子）
            psi4.set_options({'reference': 'rhf'})
            results['cation_energy'] = cation_energy
            print(f"阳离子能量: {cation_energy:.8f} Hartree")
            
            # 步骤5: 计算电离能
            print(f"\n步骤5: 计算绝热电离能...")
            
            ionization_energy_hartree = cation_energy - neutral_energy
            ionization_energy_ev = ionization_energy_hartree * 27.2114  # Hartree to eV
            ionization_energy_kcal_mol = ionization_energy_hartree * 627.509  # Hartree to kcal/mol
            
            results['ionization_energy_hartree'] = ionization_energy_hartree
            results['ionization_energy_ev'] = ionization_energy_ev
            results['ionization_energy_kcal_mol'] = ionization_energy_kcal_mol
            results['success'] = True
            
            print(f"\n绝热电离能计算结果:")
            print(f"  {ionization_energy_hartree:.6f} Hartree")
            print(f"  {ionization_energy_ev:.3f} eV")
            print(f"  {ionization_energy_kcal_mol:.2f} kcal/mol")
            
        except Exception as e:
            error_msg = f"计算过程中出现错误: {e}"
            print(f"\n错误: {error_msg}")
            results['error_message'] = error_msg
            results['success'] = False
        
        print("\n" + "="*60)
        print("计算完成")
        print("="*60)
        
        return results


def calculate_ionization_energy(smiles: str,
                               nthreads: int = 4,
                               memory: str = "2GB", 
                               low_level_method: str = "b3lyp",
                               low_level_basis: str = "6-31g(d)",
                               high_level_method: str = "m062x",
                               high_level_basis: str = "aug-cc-pvtz",
                               max_conformers: int = 10,
                               skip_low_level_optimization: bool = False,
                               scratch_dir: str = None) -> dict:
    """
    计算分子绝热电离能的便捷函数
    
    参数:
        smiles: 分子SMILES字符串
        nthreads: CPU线程数 (默认: 4)
        memory: 内存大小 (默认: "2GB")
        low_level_method: 低精度计算方法 (默认: "b3lyp")
        low_level_basis: 低精度基组 (默认: "6-31g(d)")
        high_level_method: 高精度计算方法 (默认: "m062x")
        high_level_basis: 高精度基组 (默认: "aug-cc-pvtz")
        max_conformers: 最大构象数量 (默认: 10)
        skip_low_level_optimization: 是否跳过低精度构象优化，直接使用力场最优构象 (默认: False)
        scratch_dir: PSI4计算缓存文件目录，None则使用默认目录 (默认: None)
        
    返回:
        包含计算结果的字典
    """
    with AdiabaticIonizationEnergyCalculator() as calculator:
        return calculator.calculate_adiabatic_ionization_energy(
            smiles=smiles,
            nthreads=nthreads,
            memory=memory,
            low_level_method=low_level_method,
            low_level_basis=low_level_basis,
            high_level_method=high_level_method,
            high_level_basis=high_level_basis,
            max_conformers=max_conformers,
            skip_low_level_optimization=skip_low_level_optimization,
            scratch_dir=scratch_dir
        )


if __name__ == "__main__":
    # 示例使用
    test_smiles = "CCO"  # 乙醇
    
    print("绝热电离能计算程序测试")
    print(f"测试分子: {test_smiles} (乙醇)")
    
    # 运行计算
    results = calculate_ionization_energy(
        smiles=test_smiles,
        nthreads=2,  # 使用较少线程进行测试
        memory="1GB",  # 使用较少内存进行测试
        max_conformers=5  # 使用较少构象进行快速测试
    )
    
    # 打印结果
    print("\n最终结果:")
    if results['success']:
        print(f"SMILES: {results['smiles']}")
        print(f"低精度方法: {results['low_level_method']}")
        print(f"高精度方法: {results['high_level_method']}")
        print(f"生成构象数: {results['conformers_generated']}")
        print(f"优化构象数: {results['conformers_optimized']}")
        print(f"中性分子能量: {results['neutral_energy']:.8f} Hartree")
        print(f"阳离子能量: {results['cation_energy']:.8f} Hartree")
        print(f"绝热电离能: {results['ionization_energy_ev']:.3f} eV")
    else:
        print(f"计算失败: {results['error_message']}")