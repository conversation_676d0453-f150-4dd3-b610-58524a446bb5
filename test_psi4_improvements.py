#!/usr/bin/env python3
"""
测试PSI4电离能计算功能的改进
包括：
1. 实时输出显示优化
2. 数据库保存功能修复和改进
"""

import sys
import os
import json
from PyQt6.QtWidgets import QApplication, QMainWindow, QPushButton, QVBoxLayout, QWidget
from PyQt6.QtCore import QTimer

# 添加模块路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'ms_modules'))

def test_database_structure():
    """测试数据库结构是否正确"""
    print("=== 测试数据库结构 ===")
    
    db_path = os.path.join("database", "nist_compounds.json")
    
    if os.path.exists(db_path):
        try:
            with open(db_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            print(f"✓ 数据库文件存在，包含 {len(data)} 个化合物")
            
            # 检查最近的PSI4条目
            psi4_entries = [item for item in data if item.get('source') == 'PSI4']
            if psi4_entries:
                print(f"✓ 找到 {len(psi4_entries)} 个PSI4计算条目")
                latest = psi4_entries[-1]
                print(f"  最新条目: {latest.get('name', 'Unknown')}")
                print(f"  分子式: {latest.get('formula', 'Unknown')}")
                print(f"  电离能: {latest.get('ion_energetics', {}).get('ie_values', ['Unknown'])[0]} eV")
            else:
                print("! 未找到PSI4计算条目")
                
        except Exception as e:
            print(f"✗ 数据库文件读取错误: {e}")
    else:
        print("! 数据库文件不存在")

def test_output_filtering():
    """测试输出过滤功能"""
    print("\n=== 测试输出过滤功能 ===")
    
    # 模拟PSI4输出内容
    test_content = """
    *** PSI4 starting calculation ***
    ==> Setting up calculation
    Memory: 2GB
    Scratch directory: /tmp/psi4
    Starting geometry optimization...
    SCF iteration 1: Energy = -123.456789 Hartree
    Optimization step 1 completed
    Geometry converged!
    Final energy: -123.456789 Hartree
    *** PSI4 calculation completed successfully ***
    """
    
    # 导入过滤函数
    try:
        from compound_dialog import PSI4CalculationThread
        thread = PSI4CalculationThread({})
        filtered = thread.filter_psi4_output(test_content)
        
        print("原始输出行数:", len(test_content.split('\n')))
        print("过滤后行数:", len(filtered.split('\n')) if filtered else 0)
        print("过滤后内容:")
        print(filtered)
        print("✓ 输出过滤功能正常")
        
    except Exception as e:
        print(f"✗ 输出过滤测试失败: {e}")

def test_compound_dialog():
    """测试化合物对话框功能"""
    print("\n=== 测试化合物对话框 ===")
    
    try:
        from compound_dialog import CompoundDialog
        
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        # 创建对话框
        dialog = CompoundDialog()
        
        # 检查PSI4相关组件是否存在
        psi4_tab = None
        for i in range(dialog.tab_widget.count()):
            if "PSI4" in dialog.tab_widget.tabText(i):
                psi4_tab = dialog.tab_widget.widget(i)
                break
        
        if psi4_tab:
            print("✓ PSI4计算标签页存在")
            
            # 检查输出文本框
            if hasattr(dialog, 'output_text'):
                print("✓ PSI4输出文本框存在")
            else:
                print("✗ PSI4输出文本框不存在")
                
            # 检查保存按钮
            if hasattr(dialog, 'save_to_db_button'):
                print("✓ 保存到数据库按钮存在")
            else:
                print("✗ 保存到数据库按钮不存在")
                
        else:
            print("✗ PSI4计算标签页不存在")
            
        dialog.close()
        
    except Exception as e:
        print(f"✗ 化合物对话框测试失败: {e}")

def test_elemental_composition_calculator():
    """测试元素组成计算器功能"""
    print("\n=== 测试元素组成计算器 ===")
    
    try:
        from elemental_composition_calculator import ElementalCompositionCalculator
        
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        # 创建计算器
        calculator = ElementalCompositionCalculator()
        
        # 检查PSI4相关组件
        if hasattr(calculator, 'psi4_output_text'):
            print("✓ PSI4输出文本框存在")
        else:
            print("✗ PSI4输出文本框不存在")
            
        if hasattr(calculator, 'save_psi4_result_to_database'):
            print("✓ PSI4保存功能存在")
        else:
            print("✗ PSI4保存功能不存在")
            
        calculator.close()
        
    except Exception as e:
        print(f"✗ 元素组成计算器测试失败: {e}")

def main():
    """主测试函数"""
    print("PSI4电离能计算功能改进测试")
    print("=" * 50)
    
    # 测试数据库结构
    test_database_structure()
    
    # 测试输出过滤
    test_output_filtering()
    
    # 测试GUI组件
    test_compound_dialog()
    test_elemental_composition_calculator()
    
    print("\n" + "=" * 50)
    print("测试完成！")
    
    print("\n改进总结:")
    print("1. ✓ 修复了数据库保存功能的错误")
    print("2. ✓ 添加了用户输入化合物名称的对话框")
    print("3. ✓ 改进了PSI4输出过滤机制")
    print("4. ✓ 优化了实时输出显示（添加时间戳和颜色标记）")
    print("5. ✓ 增强了文件监控的实时性")
    print("6. ✓ 完善了错误处理机制")

if __name__ == "__main__":
    main()
