"""
质谱数据处理程序
用于处理质谱数据文件，计算峰面积，生成图表和报告
"""
import os
import sys
import builtins  # 导入builtins模块，用于存储全局变量

from PyQt6.QtWidgets import (
    QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, QLabel,
    QPushButton, QFileDialog, QMessageBox, QGroupBox, QStatusBar, QTabWidget,
    QListWidget, QSplitter
)
from PyQt6.QtCore import Qt, QSize, QEvent

from ms_modules.data_processor import DataProcessor
from ms_modules.gui_components import (
    PeakEditorWidget, SettingsWidget, ProgressDialog, ResultsWidget
)
from ms_modules.project_manager import ProjectManager
from log_window import LogWindow

# 创建全局变量，用于存储主窗口实例
builtins.main_window = None

class MainWindow(QMainWindow):
    """主窗口"""

    def __init__(self):
        super().__init__()
        self.initUI()

        # 初始化数据处理器
        self.data_processor = DataProcessor()

        # 将数据处理器传递给峰编辑器
        self.peak_editor.setDataProcessor(self.data_processor)

        # 存储每个目录的处理结果
        self.batch_results = {}

        # 初始化同位素校正状态标志
        self.isotope_correction_applied = False

        # 初始化项目管理器
        self.project_manager = ProjectManager(self)

        # 导入化合物对话框模块
        from ms_modules.compound_dialog import compound_dialog_signals

        # 连接电离能选择信号
        compound_dialog_signals.ionization_energy_selected.connect(self.on_ionization_energy_selected)
        print("已连接电离能选择信号")

        # 将主窗口实例存储到全局变量中
        builtins.main_window = self
        print("已将主窗口实例存储到全局变量中")

    def initUI(self):
        """初始化UI"""
        self.setWindowTitle("质谱数据分析程序")
        self.setGeometry(100, 100, 1200, 800)

        # 创建中央部件
        central_widget = QWidget()

        # 创建主布局
        main_layout = QVBoxLayout()

        # 创建标签页组件
        self.tabs = QTabWidget()

        # 创建各个面板
        self.peak_editor = PeakEditorWidget()
        self.settings_widget = SettingsWidget()
        self.results_widget = ResultsWidget()

        # 添加标签页
        self.tabs.addTab(self.peak_editor, "峰编辑器")
        self.tabs.addTab(self.settings_widget, "设置")
        self.tabs.addTab(self.results_widget, "结果")

        # 连接ResultsWidget的isotopeCorrection信号到processAllData方法
        self.results_widget.isotopeCorrection.connect(lambda: self.processAllData(enable_isotope_correction=True))

        main_layout.addWidget(self.tabs, 1)  # 1为伸展系数，使标签页占据更多空间

        # 添加按钮布局
        process_layout = QHBoxLayout()

        # 添加项目管理按钮
        self.new_project_btn = QPushButton("新建项目")
        self.new_project_btn.clicked.connect(self.newProject)
        process_layout.addWidget(self.new_project_btn)

        self.save_project_btn = QPushButton("保存项目")
        self.save_project_btn.clicked.connect(self.saveProject)
        process_layout.addWidget(self.save_project_btn)

        self.load_project_btn = QPushButton("加载项目")
        self.load_project_btn.clicked.connect(self.loadProject)
        process_layout.addWidget(self.load_project_btn)

        process_layout.addStretch()

        # 创建批处理数据按钮和同位素校正指示器的布局
        process_btn_layout = QHBoxLayout()

        self.process_btn = QPushButton("批量处理数据")
        self.process_btn.clicked.connect(self.processAllData)
        process_btn_layout.addWidget(self.process_btn)

        # 添加同位素校正指示器
        self.isotope_indicator = QLabel("同位素校正: 未应用")
        self.isotope_indicator.setStyleSheet("color: gray;")
        process_btn_layout.addWidget(self.isotope_indicator)

        process_layout.addLayout(process_btn_layout)

        # 添加显示日志窗口按钮
        self.log_btn = QPushButton("显示日志")
        self.log_btn.clicked.connect(self.showLogWindow)
        process_layout.addWidget(self.log_btn)

        # 添加化合物查询按钮
        self.calculator_btn = QPushButton("化合物查询")
        self.calculator_btn.clicked.connect(self.showElementalCompositionCalculator)
        process_layout.addWidget(self.calculator_btn)



        main_layout.addLayout(process_layout)

        # 设置中央部件
        central_widget.setLayout(main_layout)
        self.setCentralWidget(central_widget)

        # 状态栏
        self.statusBar = QStatusBar()
        self.setStatusBar(self.statusBar)
        self.statusBar.showMessage("就绪")

        # 创建进度对话框
        self.progress_dialog = ProgressDialog()

        # 日志窗口引用，初始为None
        self.log_window = None

    def newProject(self):
        """新建项目"""
        # 检查是否有未保存的更改
        if self.batch_results and not self.project_manager.check_save_before_close():
            return

        # 清空当前数据
        self.batch_results = {}
        self.peak_editor.clearPeaks()
        self.settings_widget.dirList.clear()
        self.settings_widget.dataDirEdit.clear()
        self.results_widget.clearAll()

        # 重置项目路径
        self.project_manager.current_project_path = None

        self.statusBar.showMessage("新项目已创建")
        print("已创建新项目")

    def saveProject(self):
        """保存项目"""
        if self.project_manager.save_project():
            self.statusBar.showMessage(f"项目已保存至 {self.project_manager.current_project_path}")

    def loadProject(self):
        """加载项目"""
        # 检查是否有未保存的更改
        if self.batch_results and not self.project_manager.check_save_before_close():
            return

        if self.project_manager.load_project():
            self.statusBar.showMessage(f"项目已从 {self.project_manager.current_project_path} 加载")

    def showLogWindow(self):
        """显示日志窗口"""
        if self.log_window is None:
            self.log_window = LogWindow()

        self.log_window.show()
        self.log_window.activateWindow()  # 激活窗口（前置窗口）



    def on_ionization_energy_selected(self, ie_value, compound_name):
        """处理电离能选择信号

        参数:
            ie_value: 电离能值（eV）
            compound_name: 化合物名称
        """
        try:
            print(f"收到电离能选择信号: {ie_value} eV, 化合物: {compound_name}")

            # 将电离能值添加到结果图表中
            self.results_widget.add_ionization_energy_marker(ie_value, compound_name)

            # 切换到结果页面
            self.tabs.setCurrentIndex(2)  # 结果页面索引为2

            # 显示状态消息
            self.statusBar.showMessage(f"已添加电离能标记: {ie_value} eV ({compound_name})")
        except Exception as e:
            print(f"处理电离能选择时出错: {str(e)}")
            import traceback
            traceback.print_exc()

    def updateCalculatorMass(self, index=None):
        """更新化合物查询窗口的质量数"""
        # 检查计算器是否存在且可见
        if not hasattr(self, 'calculator') or not self.calculator.isVisible():
            return

        # 获取当前选中的峰
        selected_peak = self.results_widget.getSelectedPeak()
        if selected_peak:
            mass = selected_peak.get('mass')
            if mass is not None:
                # 更新计算器的质量数输入框（电离能查询页面）
                self.calculator.mass_input.setText(f"{mass:.6f}")

                # 始终更新PIE查询页面的质量数输入框，无论当前是哪个标签页
                self.calculator.pie_mass_input.setText(f"{mass:.6f}")
                print(f"已更新PIE查询窗口的质量数: {mass:.6f}")

                # 检查当前是否在PIE查询页面
                current_tab_index = self.calculator.main_tab_widget.currentIndex()
                if current_tab_index == 0:  # PIE查询页面
                    # 自动执行查找化合物操作
                    self.calculator.find_compounds_by_mass()
                    print(f"已自动查找符合质量数 {mass:.6f} 的化合物")
                else:
                    # 自动计算（仅在电离能查询页面）
                    self.calculator.calculate()
                    print(f"已更新化合物查询窗口的质量数并执行计算: {mass:.6f}")
                
                # 记录当前质量数的实验数据坐标范围
                self.record_axis_range_for_mass(mass)
                
                # 无论在哪个页面，都应用记录的坐标范围（如果有且开启了保持原始坐标轴范围选项）
                self.apply_recorded_axis_range_for_mass(mass)

    def record_axis_range_for_mass(self, mass):
        """记录指定质量数的实验数据坐标轴范围"""
        try:
            # 检查计算器是否存在且有mass_axis_ranges属性
            if (hasattr(self, 'calculator') and self.calculator and 
                hasattr(self.calculator, 'mass_axis_ranges')):
                
                # 获取当前实验数据的坐标范围
                if hasattr(self, 'results_widget') and self.results_widget:
                    try:
                        # 获取当前选中的峰
                        selected_peak = self.results_widget.getSelectedPeak()
                        if selected_peak and 'data' in selected_peak:
                            # 从峰数据中获取实验数据范围
                            peak_data = selected_peak['data']
                            if peak_data:
                                x_values = [point[0] for point in peak_data]
                                y_values = [point[1] for point in peak_data]
                                
                                if x_values and y_values:
                                    x_min, x_max = min(x_values), max(x_values)
                                    y_min, y_max = min(y_values), max(y_values)
                                    
                                    # 添加10%的边距
                                    x_margin = (x_max - x_min) * 0.1
                                    y_margin = (y_max - y_min) * 0.1
                                    
                                    x_range = [x_min - x_margin, x_max + x_margin]
                                    y_range = [y_min - y_margin, y_max + y_margin]
                                    
                                    # 记录到计算器的mass_axis_ranges中
                                    self.calculator.mass_axis_ranges[mass] = {
                                        'x_range': x_range,
                                        'y_range': y_range
                                    }
                                    
                                    print(f"已记录质量数 {mass:.4f} 的实验数据坐标范围: X={x_range}, Y={y_range}")
                    except Exception as e:
                        print(f"记录坐标范围时出错: {str(e)}")
        except Exception as e:
            print(f"记录坐标范围时出错: {str(e)}")

    def apply_recorded_axis_range_for_mass(self, mass):
        """为指定质量数应用记录的坐标轴范围"""
        try:
            # 检查计算器是否有保持原始坐标轴范围选项且已开启
            if (hasattr(self.calculator, 'keep_axis_range_checkbox') and 
                self.calculator.keep_axis_range_checkbox.isChecked() and
                hasattr(self.calculator, 'mass_axis_ranges') and
                mass in self.calculator.mass_axis_ranges):
                
                recorded_range = self.calculator.mass_axis_ranges[mass]
                x_range = recorded_range.get('x_range')
                y_range = recorded_range.get('y_range')
                
                if x_range and y_range:
                    # 应用记录的坐标范围到主窗口
                    if hasattr(self, 'results_widget'):
                        try:
                            view_box = self.results_widget.graphWidget.getViewBox()
                            view_box.setRange(xRange=x_range, yRange=y_range, padding=0)
                            print(f"已恢复质量数 {mass:.4f} 的记录坐标范围: X={x_range}, Y={y_range}")
                        except Exception as e:
                            print(f"应用记录的坐标范围时出错: {str(e)}")
        except Exception as e:
            print(f"应用记录坐标范围时出错: {str(e)}")

    def showElementalCompositionCalculator(self):
        """显示化合物查询窗口"""
        try:
            # 检查是否已经有打开的窗口
            if hasattr(self, 'calculator') and self.calculator.isVisible():
                # 如果已经有打开的窗口，则激活它并返回
                self.calculator.activateWindow()
                self.calculator.raise_()
                return

            # 导入化合物查询模块
            from ms_modules.elemental_composition_calculator import ElementalCompositionCalculator

            # 获取当前选中的峰的质量数（如果有）
            initial_mass = None

            # 如果在结果页面且有选中的峰
            if self.tabs.currentIndex() == 2:  # 结果页面
                selected_peak = self.results_widget.getSelectedPeak()
                if selected_peak:
                    initial_mass = selected_peak.get('mass')
                    print(f"使用选中峰的质量数: {initial_mass}")

            # 创建化合物查询窗口实例
            self.calculator = ElementalCompositionCalculator(initial_mass=initial_mass)

            # 设置窗口标志，使其保持在前台
            self.calculator.setWindowFlags(self.calculator.windowFlags() | Qt.WindowType.WindowStaysOnTopHint)

            # 连接结果页面中峰选择下拉框的信号
            self.results_widget.chartCombo.currentIndexChanged.connect(self.updateCalculatorMass)
            self.results_widget.peakCombo.currentIndexChanged.connect(self.updateCalculatorMass)

            # 信号已在__init__中连接，这里不需要重复连接

            # 计算主窗口的位置和大小
            main_geo = self.geometry()
            main_right = main_geo.x() + main_geo.width()
            main_top = main_geo.y()

            # 计算器窗口的大小
            calc_width = self.calculator.width()
            calc_height = self.calculator.height()

            # 设置计算器窗口的位置，使其出现在主窗口的右侧
            self.calculator.setGeometry(main_right + 10, main_top, calc_width, calc_height)

            # 显示计算器
            self.calculator.show()

            # 如果有初始质量数，自动计算
            if initial_mass is not None:
                self.calculator.calculate()

        except Exception as e:
            print(f"打开化合物查询窗口时出错: {str(e)}")
            import traceback
            traceback.print_exc()

    def processAllData(self, enable_isotope_correction=False):
        """批量处理所有数据目录

        参数:
            enable_isotope_correction: 是否启用同位素校正
        """
        # 获取设置中的批处理目录列表
        settings = self.settings_widget.getSettings()
        batch_dirs = settings.get('batch_dirs', [])

        # 如果启用同位素校正，设置标志
        if enable_isotope_correction:
            settings['isotope_subtraction'] = True
            # 设置同位素校正状态标志（用于项目保存）
            self.isotope_correction_applied = True
            print("\n已启用同位素校正")
            # 更新同位素校正指示器
            self.isotope_indicator.setText("同位素校正: 已应用")
            self.isotope_indicator.setStyleSheet("color: green; font-weight: bold;")
        else:
            # 检查设置中是否启用了同位素校正
            if settings.get('isotope_subtraction', False):
                # 保持同位素校正状态标志
                self.isotope_correction_applied = True
                self.isotope_indicator.setText("同位素校正: 已应用")
                self.isotope_indicator.setStyleSheet("color: green; font-weight: bold;")
            else:
                # 重置同位素校正状态标志
                self.isotope_correction_applied = False
                self.isotope_indicator.setText("同位素校正: 未应用")
                self.isotope_indicator.setStyleSheet("color: gray;")

        # 检查是否有添加目录
        if not batch_dirs:
            QMessageBox.warning(self, "错误", "请先在设置中添加至少一个数据目录")
            return

        # 检查峰设置
        peaks = self.peak_editor.getPeaks()
        if not peaks:
            QMessageBox.warning(self, "错误", "请添加至少一个峰")
            return

        # 获取同位素丰度信息
        # 如果启用了同位素校正，则计算并更新峰的同位素丰度
        if enable_isotope_correction:
            print("\n计算同位素丰度信息...")
            for peak in peaks:
                formula = peak.get('formula', '')
                if formula:
                    try:
                        # 优化公式解析，如果是m/z格式，提取实际分子式
                        if formula.startswith("m/z"):
                            parts = formula.split()
                            if len(parts) > 1 and not parts[1].replace('.', '', 1).isdigit():
                                formula = parts[1]
                                print(f"  从 '{peak['formula']}' 提取分子式: '{formula}'")

                        # 计算M+1和M+2的同位素丰度
                        m1_abundance = self.data_processor.calculate_isotope_abundance(formula, 1, settings)
                        m2_abundance = self.data_processor.calculate_isotope_abundance(formula, 2, settings)

                        # 更新峰的同位素丰度
                        peak['m1_abundance'] = m1_abundance
                        peak['m2_abundance'] = m2_abundance

                        print(f"  峰 '{formula}' 的同位素丰度: M+1={m1_abundance:.6f}, M+2={m2_abundance:.6f}")
                    except Exception as e:
                        print(f"  计算峰 '{formula}' 的同位素丰度时出错: {str(e)}")
                        # 设置默认值
                        peak['m1_abundance'] = peak.get('m1_abundance', 0.0)
                        peak['m2_abundance'] = peak.get('m2_abundance', 0.0)

        # 自动显示日志窗口
        self.showLogWindow()

        # 从峰编辑器获取校准参数
        calibration_params = self.peak_editor.getCalibrationParams()

        # 添加校准参数到设置字典
        settings['calibration'] = calibration_params

        print(f"批处理使用的校准参数: {calibration_params}")

        # 清空上一次的结果
        self.batch_results = {}

        try:
            total_dirs = len(batch_dirs)

            # 显示进度对话框
            self.progress_dialog.show()
            self.progress_dialog.statusLabel.setText("开始批处理...")
            QApplication.processEvents()

            processed_dirs = 0
            total_files = 0

            # 处理每个目录
            for i, data_dir in enumerate(batch_dirs):
                dir_name = os.path.basename(data_dir)

                print(f"\n=============================================")
                print(f"开始处理目录 {i+1}/{total_dirs}: {data_dir}")
                print(f"=============================================\n")

                self.progress_dialog.statusLabel.setText(f"处理目录 {i+1}/{total_dirs}: {dir_name}")
                QApplication.processEvents()

                try:
                    # 定义进度回调函数，将每个目录的进度映射到总进度
                    def dir_progress_callback(fraction, status):
                        overall_progress = (processed_dirs + fraction) / total_dirs
                        self.progress_dialog.updateProgress(overall_progress, status)
                        QApplication.processEvents()

                    # 处理数据
                    results, file_names, file_metadata = self.data_processor.process_batch_data(
                        data_dir, calibration_params, peaks, settings,
                        progress_callback=dir_progress_callback
                    )

                    total_files += len(file_names)

                    # 计算同位素信息
                    isotope_info = []
                    for peak in peaks:
                        try:
                            formula = peak['formula']
                            # 优化公式解析，如果是m/z格式，提取实际分子式
                            if formula.startswith("m/z"):
                                parts = formula.split()
                                if len(parts) > 1 and not parts[1].replace('.', '', 1).isdigit():
                                    formula = parts[1]
                                    print(f"从 '{peak['formula']}' 提取分子式: '{formula}'")

                            m1 = self.data_processor.calculate_isotope_abundance(formula, 1, settings)
                            m2 = self.data_processor.calculate_isotope_abundance(formula, 2, settings)

                            isotope_info.append({
                                'formula': peak['formula'],
                                'mass': peak['mass'],
                                'm1': m1,
                                'm2': m2
                            })
                        except Exception as e:
                            print(f"计算同位素信息时出错: {str(e)}")
                            import traceback
                            traceback.print_exc()
                            isotope_info.append({
                                'formula': peak['formula'],
                                'mass': peak['mass'],
                                'm1': 0.0,
                                'm2': 0.0
                            })

                    # 加载部分原始数据用于可视化
                    raw_data = {}
                    file_type = settings['file_type']

                    # 只加载前5个文件的原始数据，避免内存占用过大
                    max_files_to_load = min(5, len(file_names))

                    self.progress_dialog.statusLabel.setText(f"加载原始数据: {dir_name}")

                    for j, filename in enumerate(file_names[:max_files_to_load]):
                        file_path = os.path.join(data_dir, filename)
                        try:
                            self.progress_dialog.fileLabel.setText(f"加载原始数据: {filename}")
                            QApplication.processEvents()

                            data = self.data_processor.parse_data_file(file_path, file_type)
                            raw_data[filename] = data

                        except Exception as e:
                            print(f"加载原始数据文件 {file_path} 时出错: {str(e)}")

                    # 存储当前目录的处理结果
                    self.batch_results[data_dir] = {
                        'results': results,
                        'file_names': file_names,
                        'peaks': peaks,
                        'isotope_info': isotope_info,
                        'raw_data': raw_data,
                        'file_metadata': file_metadata
                    }

                    print(f"目录 {data_dir} 处理完成，共处理 {len(file_names)} 个文件")
                    processed_dirs += 1

                except Exception as e:
                    print(f"处理目录 {data_dir} 时出错: {str(e)}")
                    import traceback
                    traceback.print_exc()
                    processed_dirs += 1  # 即使出错也计入进度

            # 批处理完成
            self.progress_dialog.hide()

            # 更新结果窗口，使其使用下拉菜单来选择显示哪个目录的结果
            self.results_widget.updateBatchResults(self.batch_results, peaks)

            # 更新状态
            status_msg = f"批处理完成，共处理 {processed_dirs} 个目录，{total_files} 个文件"
            self.statusBar.showMessage(status_msg)

            QMessageBox.information(self, "成功", status_msg)

        except Exception as e:
            self.progress_dialog.hide()
            error_msg = f"批处理数据时出错: {str(e)}"
            QMessageBox.critical(self, "错误", error_msg)
            self.statusBar.showMessage(f"批处理失败: {str(e)}")
            print(error_msg)
            import traceback
            traceback.print_exc()

    def closeEvent(self, event):
        """应用关闭前的处理"""
        # 检查是否需要保存
        if self.project_manager.check_save_before_close():
            # 确保日志窗口也关闭
            if self.log_window:
                self.log_window.close()

            # 关闭元素组成计算器窗口（如果存在）
            if hasattr(self, 'calculator') and self.calculator:
                self.calculator.close()

            event.accept()
        else:
            event.ignore()


if __name__ == "__main__":
    app = QApplication(sys.argv)
    main_window = MainWindow()
    main_window.show()

    # 创建并显示日志窗口
    log_window = LogWindow()
    log_window.setGeometry(100, 700, 1200, 300)  # 设置位置在主窗口下方
    log_window.show()

    # 将日志窗口的引用保存到主窗口中
    main_window.log_window = log_window

    sys.exit(app.exec())