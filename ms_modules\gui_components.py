"""
GUI组件模块
包含各种界面组件类，如峰编辑器、设置面板、进度对话框等
"""

import os
import re
import numpy as np
import pandas as pd
import pyqtgraph as pg
from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel, QLineEdit, QPushButton,
    QFileDialog, QTableWidget, QTableWidgetItem, QHeaderView, QMessageBox,
    QGroupBox, QComboBox, QCheckBox, QTabWidget, QProgressBar, QSplitter,
    QRadioButton, QButtonGroup, QSpinBox, QDoubleSpinBox, QApplication, QTextEdit,
    QStatusBar, QListWidget, QDialog
)
from PyQt6.QtCore import Qt, pyqtSignal, QThread, QObject
from PyQt6.QtGui import QColor, QDoubleValidator
import sys
import io
import datetime

# 导入元素组分计算器
from ms_modules.elemental_composition_calculator import ElementalCompositionCalculator

# 前向声明MainWindow类型，避免循环导入
class MainWindow(QWidget):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("质谱数据处理程序")
        self.resize(1200, 800)

        # 创建主布局
        main_layout = QVBoxLayout()

        # 创建上半部分布局（顶部区域）
        top_layout = QVBoxLayout()

        # 添加数据文件目录输入区域
        data_dir_layout = QHBoxLayout()
        data_dir_layout.addWidget(QLabel("数据文件目录:"))

        self.data_dir_edit = QLineEdit()
        self.data_dir_edit.setReadOnly(True)  # 只读
        data_dir_layout.addWidget(self.data_dir_edit, 1)

        browse_btn = QPushButton("浏览...")
        browse_btn.clicked.connect(self.browse_data_dir)
        data_dir_layout.addWidget(browse_btn)

        top_layout.addLayout(data_dir_layout)

        # 创建标签页容器
        self.tabs = QTabWidget()

        # 创建各个面板
        self.peak_editor = PeakEditorWidget(self)
        self.settings_widget = SettingsWidget(self)
        self.results_widget = ResultsWidget(self)

        # 添加标签页
        self.tabs.addTab(self.peak_editor, "峰编辑器")
        self.tabs.addTab(self.settings_widget, "设置")
        self.tabs.addTab(self.results_widget, "结果")

        # 将标签页添加到顶部布局
        top_layout.addWidget(self.tabs, 1)

        # 添加处理按钮
        self.process_btn = QPushButton("处理数据")
        self.process_btn.setMinimumHeight(30)  # 设置按钮高度
        self.process_btn.setStyleSheet("font-weight: bold;")
        self.process_btn.clicked.connect(self.processData)
        top_layout.addWidget(self.process_btn)

        # 添加顶部布局到主布局
        main_layout.addLayout(top_layout, 3)  # 占比3

        # 创建底部区域 - 日志显示区域
        log_group = QGroupBox("处理日志")
        log_group.setStyleSheet("QGroupBox { border: 2px solid #999; border-radius: 5px; margin-top: 1ex; } "
                                "QGroupBox::title { subcontrol-origin: margin; subcontrol-position: top center; padding: 0 5px; }")
        log_layout = QVBoxLayout()

        # 创建一个用于显示日志的文本区域
        self.log_area = QTextEdit()
        self.log_area.setReadOnly(True)
        self.log_area.setMinimumHeight(200)  # 增加最小高度到200
        self.log_area.setStyleSheet("font-family: Consolas, Courier New, monospace; font-size: 10pt; background-color: #f5f5f5; border: 1px solid #ddd;")
        self.log_area.setContextMenuPolicy(Qt.ContextMenuPolicy.CustomContextMenu)
        self.log_area.customContextMenuRequested.connect(self.showLogContextMenu)
        log_layout.addWidget(self.log_area)

        # 创建按钮行
        log_buttons = QHBoxLayout()

        # 创建一个清除日志的按钮
        self.clear_log_btn = QPushButton("清除日志")
        self.clear_log_btn.clicked.connect(self.clearLog)

        # 创建一个复制日志的按钮
        self.copy_log_btn = QPushButton("复制日志")
        self.copy_log_btn.clicked.connect(self.copyLog)

        # 创建一个保存日志的按钮
        self.save_log_btn = QPushButton("保存日志")
        self.save_log_btn.clicked.connect(self.saveLog)

        # 添加按钮到布局
        log_buttons.addWidget(self.clear_log_btn)
        log_buttons.addWidget(self.copy_log_btn)
        log_buttons.addWidget(self.save_log_btn)
        log_buttons.addStretch()

        log_layout.addLayout(log_buttons)
        log_group.setLayout(log_layout)

        # 添加日志区域到主布局 - 确保它有足够的空间
        main_layout.addWidget(log_group, 1)  # 占比1

        # 创建状态栏
        self.status_bar = QStatusBar()
        self.status_bar.setSizeGripEnabled(False)  # 不显示右下角的大小调整控件
        self.status_bar.setStyleSheet("QStatusBar { border-top: 1px solid #ccc; padding: 3px; }")
        self.status_label = QLabel("就绪")
        self.status_bar.addWidget(self.status_label, 1)
        main_layout.addWidget(self.status_bar)

        # 设置布局
        self.setLayout(main_layout)

        # 重定向stdout到日志区域
        self.redirector = StdoutRedirector(self.log_area)
        sys.stdout = self.redirector

        # 创建数据处理器
        from .data_processor import DataProcessor
        self.data_processor = DataProcessor()
        self.peak_editor.setDataProcessor(self.data_processor)

        # 同步设置面板和主窗口的数据目录
        self.settings_widget.dataDirEdit.textChanged.connect(self.updateDataDir)

        print("程序已启动，日志系统已初始化")

    def browse_data_dir(self):
        """浏览选择数据目录"""
        dir_path = QFileDialog.getExistingDirectory(self, "选择数据目录")
        if dir_path:
            self.data_dir_edit.setText(dir_path)
            self.settings_widget.dataDirEdit.setText(dir_path)

    def updateDataDir(self, text):
        """同步数据目录"""
        self.data_dir_edit.setText(text)

    def showLogContextMenu(self, pos):
        """显示日志区域的右键菜单"""
        menu = self.log_area.createStandardContextMenu()

        # 添加自定义菜单项
        clear_action = menu.addAction("清除日志")
        clear_action.triggered.connect(self.clearLog)

        copy_action = menu.addAction("复制所有内容")
        copy_action.triggered.connect(self.copyLog)

        save_action = menu.addAction("保存日志到文件...")
        save_action.triggered.connect(self.saveLog)

        # 显示菜单
        menu.exec(self.log_area.mapToGlobal(pos))

    def clearLog(self):
        """清除日志区域内容"""
        self.log_area.clear()
        print("日志已清除")

    def copyLog(self):
        """复制日志内容到剪贴板"""
        self.log_area.selectAll()
        self.log_area.copy()
        self.log_area.moveCursor(self.log_area.textCursor().End)
        print("日志已复制到剪贴板")

    def saveLog(self):
        """保存日志到文件"""
        file_path, _ = QFileDialog.getSaveFileName(self, "保存日志", "", "文本文件 (*.txt);;所有文件 (*)")
        if file_path:
            try:
                with open(file_path, 'w', encoding='utf-8') as file:
                    file.write(self.log_area.toPlainText())
                print(f"日志已保存到 {file_path}")
            except Exception as e:
                print(f"保存日志时出错: {str(e)}")

    def processData(self):
        """处理数据"""
        # 从设置面板获取设置
        settings = self.settings_widget.getSettings()
        print(f"处理设置: {settings}")

        # 检查数据目录
        data_dir = settings.get('data_dir')
        if not data_dir or not os.path.isdir(data_dir):
            QMessageBox.warning(self, "警告", "请选择有效的数据目录")
            print(f"无效的数据目录: {data_dir}")
            return

        print(f"数据目录: {data_dir}")

        # 从峰编辑面板获取峰列表
        peaks = self.peak_editor.getPeaks()
        if not peaks:
            QMessageBox.warning(self, "警告", "请添加至少一个峰")
            print("未添加峰信息")
            return

        print(f"峰列表: {[peak['formula'] for peak in peaks]}")
        print(f"峰详情: {[(p.get('formula', ''), p.get('mass', 0), p.get('left_bound', 0), p.get('right_bound', 0)) for p in peaks]}")

        # 获取校准参数并添加到设置中
        calibration_params = self.peak_editor.getCalibrationParams()
        settings['calibration'] = calibration_params
        print(f"校准参数: {calibration_params}")

        # 显示进度对话框
        self.progress_dialog = ProgressDialog(self)
        self.progress_dialog.show()

        # 使用一个线程处理数据，避免UI卡死
        self.process_thread = QThread()
        self.process_worker = ProcessWorker(
            self.data_processor,
            data_dir,  # 确保传递正确的数据目录
            peaks,
            settings
        )
        self.process_worker.moveToThread(self.process_thread)

        # 连接信号
        self.process_thread.started.connect(self.process_worker.run)
        self.process_worker.progress.connect(self.progress_dialog.updateProgress)
        self.process_worker.finished.connect(self.processFinished)
        self.process_worker.error.connect(self.processError)

        # 启动线程
        self.process_thread.start()
        print("已启动数据处理线程")

    def processFinished(self, results, file_names, peaks, isotope_info, raw_data, file_metadata):
        """处理完成后的回调"""
        # 关闭进度对话框
        if hasattr(self, 'progress_dialog'):
            self.progress_dialog.hide()
            self.progress_dialog.deleteLater()

        # 清理线程
        if hasattr(self, 'process_thread'):
            self.process_thread.quit()
            self.process_thread.wait()

        # 更新状态栏
        self.status_label.setText(f"处理完成，共处理 {len(file_names)} 个文件")

        # 如果没有结果，显示警告
        if results.size == 0 or len(file_names) == 0:
            QMessageBox.warning(self, "警告", "未能处理任何数据文件")
            print("警告: 未能处理任何数据文件")
            return

        print(f"处理完成: {results.shape[0]} 个文件, {results.shape[1]} 个峰")

        # 将结果传递给结果显示面板
        self.results_widget.updateResults(results, file_names, peaks, isotope_info, raw_data, file_metadata)

        # 切换到结果标签页，并选择积分结果与绘图子标签页
        self.tabs.setCurrentIndex(2)  # 切换到"结果"标签页
        self.results_widget.mainTabs.setCurrentIndex(0)  # 选择第一个子标签页（积分结果与绘图）

    def processError(self, error_msg):
        """处理出错后的回调"""
        # 关闭进度对话框
        if hasattr(self, 'progress_dialog'):
            self.progress_dialog.hide()
            self.progress_dialog.deleteLater()

        # 清理线程
        if hasattr(self, 'process_thread'):
            self.process_thread.quit()
            self.process_thread.wait()

        # 更新状态栏
        self.status_label.setText(f"处理出错")

        # 显示错误信息
        QMessageBox.critical(self, "错误", f"数据处理出错: {error_msg}")
        print(f"错误: {error_msg}")

# 自定义的stdout重定向类，用于捕获print输出
class StdoutRedirector(io.StringIO):
    def __init__(self, text_widget):
        super().__init__()
        self.text_widget = text_widget
        self.old_stdout = sys.stdout

    def write(self, message):
        self.old_stdout.write(message)  # 同时保持原有的控制台输出
        if message.strip():  # 忽略空消息
            # 添加时间戳
            timestamp = datetime.datetime.now().strftime("[%H:%M:%S] ")
            self.text_widget.append(timestamp + message.rstrip())

            # 确保滚动到底部 - 两种方法结合使用，确保在所有Qt版本中都能正常工作
            scrollbar = self.text_widget.verticalScrollBar()
            scrollbar.setValue(scrollbar.maximum())

            # 直接使用文本光标移动到末尾也是一种保证滚动到底部的方法
            cursor = self.text_widget.textCursor()
            cursor.movePosition(cursor.MoveOperation.End)
            self.text_widget.setTextCursor(cursor)

            # 强制更新UI，确保实时显示
            QApplication.processEvents()

    def flush(self):
        self.old_stdout.flush()

class PeakEditorWidget(QWidget):
    """峰编辑器组件"""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.data_processor = None  # 将在MainWindow中设置
        self.calibration_params = {'a': '0.0', 'b': '1.0', 'c': '0.0'}  # 默认参数
        self.initUI()

    def initUI(self):
        layout = QVBoxLayout()

        # 校准参数输入区域
        cal_group = QGroupBox("质量校准")
        cal_layout = QVBoxLayout()

        # 校准公式说明
        cal_layout.addWidget(QLabel("校准公式: M = A + B*x + C*x²  (x为峰位置)"))

        # 参数输入
        param_layout = QHBoxLayout()

        self.aInput = QLineEdit("0.0")  # 常数项
        self.bInput = QLineEdit("1.0")  # 一次项系数
        self.cInput = QLineEdit("0.0")  # 二次项系数

        param_layout.addWidget(QLabel("A(常数):"))
        param_layout.addWidget(self.aInput)
        param_layout.addWidget(QLabel("B(一次):"))
        param_layout.addWidget(self.bInput)
        param_layout.addWidget(QLabel("C(二次):"))
        param_layout.addWidget(self.cInput)

        cal_layout.addLayout(param_layout)

        # 添加背景扣除说明
        bg_info = QLabel("本底扣除区域说明：\n空白 - 峰前后区域\na - 只使用峰后区域\nb - 只使用峰前区域\nf - 不扣除背景\nd - 或输入自定义位置(位置1,位置2)")
        bg_info.setStyleSheet("font-size: 10pt; color: #555;")
        cal_layout.addWidget(bg_info)

        # 应用按钮
        apply_btn = QPushButton("应用校准参数")
        apply_btn.clicked.connect(self.applyCalibrationParams)
        cal_layout.addWidget(apply_btn)

        cal_group.setLayout(cal_layout)
        layout.addWidget(cal_group)

        # 创建表格
        self.table = QTableWidget(0, 7)  # 增加一列存放自定义背景范围
        self.table.setHorizontalHeaderLabels(["峰位置", "m/z", "分子式", "左边界", "右边界", "本底扣除区域", "自定义背景范围"])
        self.table.horizontalHeader().setSectionResizeMode(QHeaderView.ResizeMode.Stretch)

        # 连接编辑完成信号
        self.table.cellChanged.connect(self.onCellChanged)

        layout.addWidget(self.table)

        # 添加按钮
        btnLayout = QHBoxLayout()

        self.addBtn = QPushButton("添加峰")
        self.addBtn.clicked.connect(self.addPeak)
        btnLayout.addWidget(self.addBtn)

        self.deleteBtn = QPushButton("删除峰")
        self.deleteBtn.clicked.connect(self.deletePeak)
        btnLayout.addWidget(self.deleteBtn)

        self.clearBtn = QPushButton("清空")
        self.clearBtn.clicked.connect(self.clearPeaks)
        btnLayout.addWidget(self.clearBtn)

        # 导入导出按钮
        self.importBtn = QPushButton("导入峰列表")
        self.importBtn.clicked.connect(self.importPeaks)
        btnLayout.addWidget(self.importBtn)

        self.exportBtn = QPushButton("导出峰列表")
        self.exportBtn.clicked.connect(self.exportPeaks)
        btnLayout.addWidget(self.exportBtn)

        layout.addLayout(btnLayout)

        # 设置布局
        self.setLayout(layout)

    def setDataProcessor(self, processor):
        """设置数据处理器"""
        self.data_processor = processor

    def onCellChanged(self, row, column):
        """当单元格内容改变时更新相关数据"""
        # 断开信号，避免循环
        self.table.blockSignals(True)

        # 如果峰位置列改变，则更新质量数
        if column == 0:
            self.updateMassDisplay(row)

        # 如果分子式列改变，则同步到同位素信息表格
        elif column == 2:
            self.syncFormulaToIsotopeInfo(row)

        # 重新连接信号
        self.table.blockSignals(False)

    def syncFormulaToIsotopeInfo(self, row):
        """将峰编辑器中的分子式同步到同位素信息表格"""
        try:
            formula_item = self.table.item(row, 2)
            if formula_item and formula_item.text():
                new_formula = formula_item.text()

                # 获取主窗口实例
                main_window = None
                for widget in QApplication.topLevelWidgets():
                    if hasattr(widget, 'windowTitle') and "质谱数据分析" in widget.windowTitle():
                        if hasattr(widget, 'results_widget'):
                            main_window = widget
                            break

                if main_window and hasattr(main_window, 'results_widget'):
                    # 更新同位素信息表格中的分子式
                    results_widget = main_window.results_widget

                    # 检查同位素信息表格是否有足够的行
                    if row < results_widget.isoInfoTable.rowCount():
                        # 更新分子式
                        results_widget.isoInfoTable.blockSignals(True)
                        results_widget.isoInfoTable.setItem(row, 0, QTableWidgetItem(new_formula))

                        # 重新计算同位素丰度
                        from .isotope_database import calculate_isotope_abundance
                        m1_abundance = calculate_isotope_abundance(new_formula, 1)
                        m2_abundance = calculate_isotope_abundance(new_formula, 2)

                        # 更新同位素信息表格
                        results_widget.isoInfoTable.setItem(row, 2, QTableWidgetItem(f"{m1_abundance:.6f}"))
                        results_widget.isoInfoTable.setItem(row, 3, QTableWidgetItem(f"{m2_abundance:.6f}"))
                        results_widget.isoInfoTable.blockSignals(False)
        except Exception as e:
            print(f"同步分子式到同位素信息表格时出错: {str(e)}")
            import traceback
            traceback.print_exc()

    def updateMassDisplay(self, row):
        """根据峰位置更新质量数显示"""
        try:
            position_item = self.table.item(row, 0)
            if position_item and position_item.text():
                position = float(position_item.text())

                # 使用数据处理器计算质量数
                if self.data_processor:
                    # 使用position_to_mass函数计算质量数
                    mass = self.data_processor.position_to_mass(position, self.calibration_params)
                else:
                    # 如果数据处理器未设置，使用简单公式
                    a = float(self.calibration_params.get('a', 0))
                    b = float(self.calibration_params.get('b', 1))
                    c = float(self.calibration_params.get('c', 0))
                    mass = a + b * position + c * position * position

                # 更新质量数显示
                mass_str = f"{mass:.6f}"
                self.table.setItem(row, 1, QTableWidgetItem(mass_str))
        except (ValueError, IndexError) as e:
            print(f"更新质量数时出错: {str(e)}")

    def applyCalibrationParams(self):
        """应用校准参数并更新所有峰的质量数"""
        try:
            # 获取参数
            self.calibration_params = {
                'a': self.aInput.text().strip(),
                'b': self.bInput.text().strip(),
                'c': self.cInput.text().strip()
            }

            # 更新所有峰的质量数
            self.updateAllPeaksMass()

            QMessageBox.information(self, "成功", "校准参数已应用并更新所有峰的质量数")
        except Exception as e:
            QMessageBox.warning(self, "错误", f"应用校准参数时出错: {str(e)}")

    def updateAllPeaksMass(self):
        """更新所有峰的质量数"""
        self.table.blockSignals(True)

        for row in range(self.table.rowCount()):
            self.updateMassDisplay(row)

        self.table.blockSignals(False)

    def addPeak(self):
        """添加一个新峰"""
        # 获取行数
        row = self.table.rowCount()
        self.table.insertRow(row)

        # 添加默认值
        self.table.setItem(row, 0, QTableWidgetItem("0"))  # 峰位置
        self.table.setItem(row, 1, QTableWidgetItem("0"))  # m/z
        self.table.setItem(row, 2, QTableWidgetItem(f"m/z {row+1}"))  # 分子式
        self.table.setItem(row, 3, QTableWidgetItem("0"))  # 左边界
        self.table.setItem(row, 4, QTableWidgetItem("0"))  # 右边界
        self.table.setItem(row, 5, QTableWidgetItem(""))  # 背景区域 - 默认为空，表示使用峰前后区域
        self.table.setItem(row, 6, QTableWidgetItem(""))  # 背景右边界

        # 选择新行
        self.table.selectRow(row)

    def deletePeak(self):
        """删除选中的峰"""
        selected = self.table.selectedIndexes()
        if not selected:
            return

        # 获取选中的行
        rows = set(index.row() for index in selected)

        # 从最后一行开始删除，避免索引变化
        for row in sorted(rows, reverse=True):
            self.table.removeRow(row)

    def clearPeaks(self):
        """清空所有峰"""
        # 检查是否有峰数据
        if self.table.rowCount() == 0:
            return

        # 显示确认对话框
        reply = QMessageBox.question(
            self,
            "确认清空",
            "确定要清空所有峰数据吗？此操作无法撤销。",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
            QMessageBox.StandardButton.No  # 默认选择"否"
        )

        # 如果用户确认，则清空表格
        if reply == QMessageBox.StandardButton.Yes:
            self.table.setRowCount(0)
            print("已清空所有峰数据")

    def getPeaks(self):
        """获取峰列表"""
        peaks = []

        for row in range(self.table.rowCount()):
            try:
                position = float(self.table.item(row, 0).text())
                mass = float(self.table.item(row, 1).text())
                formula = self.table.item(row, 2).text()
                left_bound = float(self.table.item(row, 3).text())
                right_bound = float(self.table.item(row, 4).text())

                # 本底扣除区域可能是字母(a, b, f)或空
                bg_text = self.table.item(row, 5).text().strip()
                # 自定义背景范围可能是逗号分隔的两个值
                bg_range_text = self.table.item(row, 6).text().strip()

                bg_mode = ""

                # 根据本底扣除区域的输入决定背景扣除模式
                if bg_text.lower() == "a":
                    # 只使用峰后区域
                    bg_mode = "a"
                elif bg_text.lower() == "b":
                    # 只使用峰前区域
                    bg_mode = "b"
                elif bg_text.lower() == "f":
                    # 不扣除背景
                    bg_mode = "f"
                elif bg_text.lower() == "d":
                    # 使用自定义背景区域
                    bg_mode = "d"
                else:
                    # 默认使用峰前后区域
                    bg_mode = ""

                peak_info = {
                    'position': position,
                    'mass': mass,
                    'formula': formula,
                    'left_bound': left_bound,
                    'right_bound': right_bound,
                    'bg_mode': bg_mode
                }

                # 如果有自定义背景范围，则添加到peak_info中
                if bg_range_text:
                    peak_info['bg_range'] = bg_range_text

                peaks.append(peak_info)

            except (ValueError, AttributeError) as e:
                print(f"获取峰 {row+1} 信息时出错: {str(e)}，忽略此峰")

        return peaks

    def importPeaks(self):
        """从CSV文件导入峰列表"""
        file_path, _ = QFileDialog.getOpenFileName(self, "导入峰列表", "", "CSV文件 (*.csv)")
        if not file_path:
            return

        try:
            # 读取CSV文件
            df = pd.read_csv(file_path)

            # 检查必要的列是否存在
            if 'position' not in df.columns:
                QMessageBox.warning(self, "错误", "CSV文件缺少必要的列：position (峰位置)")
                return

            # 断开信号
            self.table.blockSignals(True)

            # 清空当前表格
            self.clearPeaks()

            # 添加峰
            for _, row_data in df.iterrows():
                row = self.table.rowCount()
                self.table.insertRow(row)

                # 设置值
                self.table.setItem(row, 0, QTableWidgetItem(str(row_data['position'])))

                # 设置m/z (如果存在)
                if 'mass' in df.columns and pd.notna(row_data['mass']):
                    self.table.setItem(row, 1, QTableWidgetItem(str(row_data['mass'])))

                # 设置分子式 (如果存在)
                if 'formula' in df.columns and pd.notna(row_data['formula']):
                    self.table.setItem(row, 2, QTableWidgetItem(str(row_data['formula'])))

                # 设置边界
                if 'left_bound' in df.columns and pd.notna(row_data['left_bound']):
                    self.table.setItem(row, 3, QTableWidgetItem(str(row_data['left_bound'])))
                else:
                    self.table.setItem(row, 3, QTableWidgetItem("0.0"))

                if 'right_bound' in df.columns and pd.notna(row_data['right_bound']):
                    self.table.setItem(row, 4, QTableWidgetItem(str(row_data['right_bound'])))
                else:
                    self.table.setItem(row, 4, QTableWidgetItem("0.0"))

                # 设置本底扣除区域
                if 'bg_mode' in df.columns and pd.notna(row_data['bg_mode']):
                    self.table.setItem(row, 5, QTableWidgetItem(str(row_data['bg_mode'])))

                # 设置自定义背景范围
                if 'bg_range' in df.columns and pd.notna(row_data['bg_range']):
                    self.table.setItem(row, 6, QTableWidgetItem(str(row_data['bg_range'])))
                # 兼容旧版本
                elif 'bg_left' in df.columns and 'bg_right' in df.columns:
                    bg_left = row_data.get('bg_left')
                    bg_right = row_data.get('bg_right')

                    if pd.notna(bg_left) and pd.notna(bg_right):
                        bg_range = f"{bg_left},{bg_right}"
                        self.table.setItem(row, 6, QTableWidgetItem(bg_range))

            # 重新连接信号
            self.table.blockSignals(False)

            # 更新所有峰的质量数
            self.updateAllPeaksMass()

            QMessageBox.information(self, "成功", f"已从 {file_path} 导入 {self.table.rowCount()} 个峰")

        except Exception as e:
            QMessageBox.critical(self, "错误", f"导入峰列表时出错: {str(e)}")

    def exportPeaks(self):
        """导出峰列表到CSV文件"""
        peaks = self.getPeaks()
        if not peaks:
            QMessageBox.warning(self, "错误", "没有可导出的峰")
            return

        file_path, _ = QFileDialog.getSaveFileName(self, "导出峰列表", "", "CSV文件 (*.csv)")
        if not file_path:
            return

        try:
            # 创建DataFrame
            data = {
                'position': [],
                'mass': [],
                'formula': [],
                'left_bound': [],
                'right_bound': [],
                'bg_mode': [],
                'bg_range': []
            }

            for peak in peaks:
                data['position'].append(peak.get('position'))
                data['mass'].append(peak.get('mass'))
                data['formula'].append(peak.get('formula'))
                data['left_bound'].append(peak.get('left_bound'))
                data['right_bound'].append(peak.get('right_bound'))
                data['bg_mode'].append(peak.get('bg_mode'))
                data['bg_range'].append(peak.get('bg_range'))

            df = pd.DataFrame(data)

            # 导出CSV
            df.to_csv(file_path, index=False)

            QMessageBox.information(self, "成功", f"已将 {len(peaks)} 个峰导出到 {file_path}")

        except Exception as e:
            QMessageBox.critical(self, "错误", f"导出峰列表时出错: {str(e)}")

    def getCalibrationParams(self):
        """获取当前的校准参数"""
        return {
            'a': self.aInput.text(),
            'b': self.bInput.text(),
            'c': self.cInput.text()
        }

    def setCalibrationParams(self, params):
        """设置校准参数

        参数:
            params: 包含校准参数的字典，格式为 {'a': '0.0', 'b': '1.0', 'c': '0.0'}
        """
        self.table.blockSignals(True)

        if 'a' in params:
            self.aInput.setText(str(params['a']))
        if 'b' in params:
            self.bInput.setText(str(params['b']))
        if 'c' in params:
            self.cInput.setText(str(params['c']))

        # 更新校准参数
        self.calibration_params = {
            'a': self.aInput.text(),
            'b': self.bInput.text(),
            'c': self.cInput.text()
        }

        # 更新所有峰的质量数
        self.updateAllPeaksMass()

        self.table.blockSignals(False)

    def setPeaks(self, peaks):
        """设置峰列表

        参数:
            peaks: 峰信息列表，每个元素为字典，包含position, mass, formula等字段
        """
        # 断开信号
        self.table.blockSignals(True)

        # 清空当前表格
        self.clearPeaks()

        # 添加峰
        for peak in peaks:
            row = self.table.rowCount()
            self.table.insertRow(row)

            # 设置峰位置
            self.table.setItem(row, 0, QTableWidgetItem(str(peak.get('position', 0))))

            # 设置质量数
            self.table.setItem(row, 1, QTableWidgetItem(str(peak.get('mass', 0))))

            # 设置分子式
            self.table.setItem(row, 2, QTableWidgetItem(str(peak.get('formula', f"m/z {row+1}"))))

            # 设置边界
            self.table.setItem(row, 3, QTableWidgetItem(str(peak.get('left_bound', 0))))
            self.table.setItem(row, 4, QTableWidgetItem(str(peak.get('right_bound', 0))))

            # 设置背景区域
            bg_mode = peak.get('bg_mode', '')
            bg_text = ""
            bg_right_text = ""

            if bg_mode == 'a' or bg_mode == 'b' or bg_mode == 'f':
                bg_text = bg_mode
            elif bg_mode == 'custom':
                bg_text = str(peak.get('bg_left', 0))
                bg_right_text = str(peak.get('bg_right', 0))

            self.table.setItem(row, 5, QTableWidgetItem(bg_text))
            self.table.setItem(row, 6, QTableWidgetItem(bg_right_text))

        # 重新连接信号
        self.table.blockSignals(False)


class SettingsWidget(QWidget):
    """设置面板组件"""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.initUI()

    def initUI(self):
        layout = QVBoxLayout()

        # 数据库设置
        dbGroup = QGroupBox("数据库设置")
        dbLayout = QVBoxLayout()

        # 添加自定义数据库数据按钮
        self.custom_db_btn = QPushButton("自定义数据库数据")
        self.custom_db_btn.setToolTip("手动添加电离能和光电离截面数据")
        self.custom_db_btn.clicked.connect(self.openCustomDatabaseDialog)
        dbLayout.addWidget(self.custom_db_btn)
        
        # 添加数据库管理按钮
        self.manage_db_btn = QPushButton("数据库管理")
        self.manage_db_btn.setToolTip("查看、修改或删除数据库中的化合物信息")
        self.manage_db_btn.clicked.connect(self.openDatabaseManagerDialog)
        dbLayout.addWidget(self.manage_db_btn)

        dbGroup.setLayout(dbLayout)
        layout.addWidget(dbGroup)

        # 文件类型设置
        fileTypeGroup = QGroupBox("文件类型设置")
        fileTypeLayout = QVBoxLayout()

        self.fileTypeCombo = QComboBox()
        self.fileTypeCombo.addItems(["txt", "csv", "dat"])

        # 添加LOG显示等级设置
        logLevelLayout = QHBoxLayout()
        logLevelLayout.addWidget(QLabel("LOG显示等级:"))

        self.logLevelCombo = QComboBox()
        self.logLevelCombo.addItems(["不显示", "简略显示", "详细显示"])
        self.logLevelCombo.setCurrentIndex(2)  # 默认设置为详细显示
        logLevelLayout.addWidget(self.logLevelCombo)

        fileTypeLayout.addLayout(logLevelLayout)

        # 数据目录
        dirLayout = QHBoxLayout()
        self.dataDirEdit = QLineEdit()
        self.dataDirEdit.setPlaceholderText("请选择数据目录")

        browseBtn = QPushButton("浏览...")
        browseBtn.clicked.connect(self.browseDataDir)

        dirLayout.addWidget(QLabel("数据目录:"))
        dirLayout.addWidget(self.dataDirEdit, 1)
        dirLayout.addWidget(browseBtn)

        fileTypeLayout.addLayout(dirLayout)

        fileTypeGroup.setLayout(fileTypeLayout)
        layout.addWidget(fileTypeGroup)

        # 光强归一化设置
        normGroup = QGroupBox("信号归一化")
        normLayout = QVBoxLayout()

        # 光强归一化选项
        self.normalizationCheck = QCheckBox("信号除以光强归一化")
        self.normalizationCheck.setChecked(True)  # 默认开启
        self.normalizationCheck.stateChanged.connect(self.updateNormalizationUI)
        normLayout.addWidget(self.normalizationCheck)

        # 光子强度校正
        self.photonCorrectionCheck = QCheckBox("启用光子强度校正")
        self.photonCorrectionCheck.setChecked(True)  # 默认开启
        self.photonCorrectionCheck.stateChanged.connect(self.updateNormalizationUI)
        normLayout.addWidget(self.photonCorrectionCheck)

        # 光强背景值
        piBGLayout = QHBoxLayout()
        piBGLayout.addWidget(QLabel("光强背景值:"))
        self.piBGInput = QLineEdit("0.0")  # 默认值为0
        self.piBGInput.setValidator(QDoubleValidator())
        piBGLayout.addWidget(self.piBGInput)
        normLayout.addLayout(piBGLayout)

        # 光电二极管类型选择
        pdTypeLayout = QHBoxLayout()
        pdTypeLayout.addWidget(QLabel("光电二极管类型:"))

        self.pdTypeGroup = QButtonGroup(self)
        self.pdSXUVRadio = QRadioButton("SXUV")
        self.pdAXUVRadio = QRadioButton("AXUV")
        self.pdSXUVRadio.setChecked(True)  # 默认SXUV

        self.pdTypeGroup.addButton(self.pdSXUVRadio)
        self.pdTypeGroup.addButton(self.pdAXUVRadio)

        pdTypeLayout.addWidget(self.pdSXUVRadio)
        pdTypeLayout.addWidget(self.pdAXUVRadio)
        pdTypeLayout.addStretch()

        normLayout.addLayout(pdTypeLayout)

        normGroup.setLayout(normLayout)
        layout.addWidget(normGroup)

        # 批处理设置
        batchGroup = QGroupBox("批处理设置")
        batchLayout = QVBoxLayout()

        # 按钮和列表
        buttonLayout = QHBoxLayout()
        self.addDirBtn = QPushButton("添加目录")
        self.addDirBtn.clicked.connect(self.addDataDir)
        self.removeDirBtn = QPushButton("移除目录")
        self.removeDirBtn.clicked.connect(self.removeDataDir)
        self.clearDirsBtn = QPushButton("清空目录")
        self.clearDirsBtn.clicked.connect(self.clearDataDirs)

        buttonLayout.addWidget(self.addDirBtn)
        buttonLayout.addWidget(self.removeDirBtn)
        buttonLayout.addWidget(self.clearDirsBtn)

        self.dirList = QListWidget()

        batchLayout.addLayout(buttonLayout)
        batchLayout.addWidget(self.dirList)

        batchGroup.setLayout(batchLayout)
        layout.addWidget(batchGroup)

        # 设置布局
        self.setLayout(layout)

        # 初始化UI状态
        self.updateNormalizationUI()

    def updateNormalizationUI(self):
        """更新光强归一化相关UI状态"""
        # 光强校正只在开启归一化时可用
        normEnabled = self.normalizationCheck.isChecked()
        self.photonCorrectionCheck.setEnabled(normEnabled)
        self.piBGInput.setEnabled(normEnabled)  # 光强背景值只在归一化开启时可用

        # 光电二极管类型选择只在开启归一化和校正时可用
        pdEnabled = normEnabled and self.photonCorrectionCheck.isChecked()
        self.pdSXUVRadio.setEnabled(pdEnabled)
        self.pdAXUVRadio.setEnabled(pdEnabled)

    def browseDataDir(self):
        """浏览数据目录"""
        directory = QFileDialog.getExistingDirectory(self, "选择数据目录")
        if directory:
            self.dataDirEdit.setText(directory)

    def addDataDir(self):
        """添加批处理目录"""
        directory = QFileDialog.getExistingDirectory(self, "选择批处理目录")
        if directory:
            # 检查是否已经添加
            items = [self.dirList.item(i).text() for i in range(self.dirList.count())]
            if directory not in items:
                self.dirList.addItem(directory)

    def removeDataDir(self):
        """移除选中的批处理目录"""
        selected = self.dirList.selectedItems()
        if not selected:
            return

        for item in selected:
            self.dirList.takeItem(self.dirList.row(item))

    def clearDataDirs(self):
        """清空批处理目录列表"""
        self.dirList.clear()

    def getSettings(self):
        """获取设置"""
        # 收集批处理目录
        batch_dirs = []
        for i in range(self.dirList.count()):
            batch_dirs.append(self.dirList.item(i).text())

        settings = {
            'file_type': self.fileTypeCombo.currentText(),
            'normalization': self.normalizationCheck.isChecked(),
            'photon_intensity_correction': self.photonCorrectionCheck.isChecked(),
            'pi_bg': float(self.piBGInput.text() or 0.0),  # 光强背景值
            'photodiode_type': 'SXUV' if self.pdSXUVRadio.isChecked() else 'AXUV',
            'data_dir': self.dataDirEdit.text(),
            'batch_dirs': batch_dirs,
            'log_level': self.logLevelCombo.currentIndex() + 1  # 将索引转换为1-3的等级
        }

        return settings

    def openCustomDatabaseDialog(self):
        """打开自定义数据库数据对话框"""
        from .custom_database_dialog import CustomDatabaseDialog
        dialog = CustomDatabaseDialog(self)
        dialog.exec()
    
    def openDatabaseManagerDialog(self):
        """打开数据库管理对话框"""
        from .database_manager_dialog import DatabaseManagerDialog
        dialog = DatabaseManagerDialog(self)
        dialog.exec()

    def setSettings(self, settings):
        """设置设置值

        参数:
            settings: 设置字典，包含要应用的设置值
        """
        # 设置文件类型
        file_type = settings.get('file_type', 'txt')
        index = self.fileTypeCombo.findText(file_type)
        if index >= 0:
            self.fileTypeCombo.setCurrentIndex(index)

        # 设置数据目录
        if 'data_dir' in settings:
            self.dataDirEdit.setText(settings['data_dir'])

        # 设置光强归一化
        if 'normalization' in settings:
            self.normalizationCheck.setChecked(settings['normalization'])
        else:
            self.normalizationCheck.setChecked(True)  # 默认开启

        # 设置光子强度校正
        if 'photon_intensity_correction' in settings:
            self.photonCorrectionCheck.setChecked(settings['photon_intensity_correction'])
        else:
            self.photonCorrectionCheck.setChecked(True)  # 默认开启

        # 设置光强背景值
        if 'pi_bg' in settings:
            self.piBGInput.setText(str(settings['pi_bg']))

        # 设置光电二极管类型
        if 'photodiode_type' in settings:
            photodiode_type = settings['photodiode_type']
            if photodiode_type == 'SXUV':
                self.pdSXUVRadio.setChecked(True)
            else:
                self.pdAXUVRadio.setChecked(True)

        # 设置LOG显示等级
        if 'log_level' in settings:
            log_level = settings['log_level']
            # 将等级转换为索引（0-2）
            index = min(max(log_level - 1, 0), 2)
            self.logLevelCombo.setCurrentIndex(index)
        else:
            # 默认为详细显示
            self.logLevelCombo.setCurrentIndex(2)

        # 设置批处理目录
        self.dirList.clear()
        for dir_path in settings.get('batch_dirs', []):
            self.dirList.addItem(dir_path)


class ProgressDialog(QDialog):
    """进度对话框"""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.initUI()

    def initUI(self):
        layout = QVBoxLayout()

        # 标题
        layout.addWidget(QLabel("正在处理数据..."))

        # 状态标签
        self.statusLabel = QLabel("初始化...")
        layout.addWidget(self.statusLabel)

        # 文件标签
        self.fileLabel = QLabel("")
        layout.addWidget(self.fileLabel)

        # 进度条
        self.progressBar = QProgressBar()
        self.progressBar.setMinimum(0)
        self.progressBar.setMaximum(100)
        layout.addWidget(self.progressBar)

        self.setLayout(layout)
        self.setWindowTitle("进度")
        self.setFixedSize(400, 150)

        # 设置窗口标志
        self.setWindowFlags(Qt.WindowType.Window | Qt.WindowType.WindowStaysOnTopHint)

        # 设置窗口模态，防止用户操作主窗口
        self.setWindowModality(Qt.WindowModality.ApplicationModal)

    def updateProgress(self, fraction, status=None):
        """更新进度

        参数:
            fraction: 进度比例（0-1）
            status: 状态信息
        """
        # 限制进度范围
        fraction = max(0.0, min(1.0, fraction))

        # 更新进度条，使用更精确的计算
        value = int(round(fraction * 100))
        self.progressBar.setValue(value)

        # 更新进度百分比文本
        self.progressBar.setFormat(f"{value}%")

        # 更新状态标签
        if status:
            self.statusLabel.setText(status)

        # 处理事件，确保UI更新
        QApplication.processEvents()


class ResultsWidget(QWidget):
    """结果显示组件"""

    # 定义信号
    isotopeCorrection = pyqtSignal()

    def __init__(self, parent=None):
        super().__init__(parent)
        self.initUI()

        # 电离能标记列表
        self.ionization_energy_markers = []

    def initUI(self):
        layout = QVBoxLayout()

        # 添加目录选择控件
        dir_selector_layout = QHBoxLayout()
        dir_selector_layout.addWidget(QLabel("选择数据目录:"))
        self.dir_combo = QComboBox()
        self.dir_combo.currentIndexChanged.connect(self.onDirSelected)
        dir_selector_layout.addWidget(self.dir_combo, 1)  # 1为伸展因子，使下拉框占据更多空间
        layout.addLayout(dir_selector_layout)

        # 创建主标签页
        self.mainTabs = QTabWidget()

        # 第一个标签页：积分结果和绘图
        integrationResultsTab = QWidget()
        integrationLayout = QVBoxLayout(integrationResultsTab)

        # 创建子标签页容器
        integrationSubTabs = QTabWidget()

        # 创建积分结果子标签页
        integrationSubTab = QWidget()
        integrationSubLayout = QVBoxLayout(integrationSubTab)

        # 创建分割器用于调整上下面板
        integrationSplitter = QSplitter(Qt.Orientation.Vertical)

        # 上部面板 - 积分结果表格
        upperPanel = QWidget()
        upperLayout = QVBoxLayout(upperPanel)

        # 积分结果表格
        self.resultsTable = QTableWidget(0, 0)
        self.resultsTable.horizontalHeader().setSectionResizeMode(QHeaderView.ResizeMode.Stretch)
        self.resultsTable.setContextMenuPolicy(Qt.ContextMenuPolicy.CustomContextMenu)
        self.resultsTable.customContextMenuRequested.connect(self.showResultsContextMenu)
        self.resultsTable.cellDoubleClicked.connect(self.onResultsCellDoubleClicked)
        upperLayout.addWidget(self.resultsTable)

        # 添加导出积分结果按钮
        exportResultsButton = QPushButton("导出积分结果")
        exportResultsButton.clicked.connect(self.exportResults)
        upperLayout.addWidget(exportResultsButton)

        # 下部面板 - 积分结果图表
        lowerPanel = QWidget()
        lowerLayout = QVBoxLayout(lowerPanel)

        # 峰选择和坐标轴控制面板
        controlLayout = QHBoxLayout()

        # 峰选择下拉框
        self.chartCombo = QComboBox()
        self.chartCombo.currentIndexChanged.connect(self.updateChart)

        controlLayout.addWidget(QLabel("选择峰:"))
        controlLayout.addWidget(self.chartCombo)

        # 横坐标选择
        xAxisLayout = QHBoxLayout()
        xAxisLabel = QLabel("横坐标类型:")
        self.xAxisCombo = QComboBox()
        self.xAxisCombo.addItems(["文件索引", "能量(eV)", "温度(K)"])
        self.xAxisCombo.currentIndexChanged.connect(self.updateChart)
        xAxisLayout.addWidget(xAxisLabel)
        xAxisLayout.addWidget(self.xAxisCombo)

        controlLayout.addLayout(xAxisLayout)

        lowerLayout.addLayout(controlLayout)

        # 绘图区域
        self.graphWidget = pg.PlotWidget()
        self.graphWidget.setBackground('w')
        lowerLayout.addWidget(self.graphWidget)

        # 添加面板到分割器
        integrationSplitter.addWidget(upperPanel)
        integrationSplitter.addWidget(lowerPanel)

        # 设置分割器比例
        integrationSplitter.setSizes([300, 500])

        integrationSubLayout.addWidget(integrationSplitter)

        # 添加积分结果子标签页
        integrationSubTabs.addTab(integrationSubTab, "积分结果")

        # 创建同位素信息子标签页
        isoInfoTab = QWidget()
        isoInfoLayout = QVBoxLayout(isoInfoTab)

        # 同位素信息表格
        self.isoInfoTable = QTableWidget(0, 4)
        self.isoInfoTable.setHorizontalHeaderLabels(["分子式", "m/z", "M+1同位素丰度", "M+2同位素丰度"])
        self.isoInfoTable.horizontalHeader().setSectionResizeMode(QHeaderView.ResizeMode.Stretch)
        self.isoInfoTable.cellChanged.connect(self.onIsoInfoTableCellChanged)
        # 移除双击打开化合物查询窗口的功能
        # self.isoInfoTable.cellDoubleClicked.connect(self.onIsoInfoTableCellDoubleClicked)
        isoInfoLayout.addWidget(self.isoInfoTable)

        # 添加重新校正同位素按钮
        recalcIsoButton = QPushButton("重新校正同位素")
        # 连接到信号
        recalcIsoButton.clicked.connect(self.isotopeCorrection.emit)
        isoInfoLayout.addWidget(recalcIsoButton)

        # 添加同位素信息子标签页
        integrationSubTabs.addTab(isoInfoTab, "同位素信息")

        # 将子标签页容器添加到积分结果标签页
        integrationLayout.addWidget(integrationSubTabs)

        # 添加到主标签页
        self.mainTabs.addTab(integrationResultsTab, "积分结果与绘图")

        # 第二个标签页：文件元数据和原始数据
        fileDataTab = QWidget()
        fileDataLayout = QVBoxLayout(fileDataTab)

        # 创建分割器用于调整上下面板
        fileDataSplitter = QSplitter(Qt.Orientation.Vertical)

        # 上部面板 - 包含文件元数据和同位素信息
        upperDataPanel = QWidget()
        upperDataLayout = QVBoxLayout(upperDataPanel)

        # 使用标签页显示文件元数据
        dataTabs = QTabWidget()

        # 文件元数据表格
        metadataTab = QWidget()
        metadataLayout = QVBoxLayout(metadataTab)

        self.metadataTable = QTableWidget(0, 3)
        self.metadataTable.setHorizontalHeaderLabels(["文件名", "能量(eV)", "温度(K)"])
        self.metadataTable.horizontalHeader().setSectionResizeMode(QHeaderView.ResizeMode.Stretch)
        metadataLayout.addWidget(self.metadataTable)

        # 添加导出元数据按钮
        exportMetadataButton = QPushButton("导出文件元数据")
        exportMetadataButton.clicked.connect(self.exportMetadata)
        metadataLayout.addWidget(exportMetadataButton)

        dataTabs.addTab(metadataTab, "文件元数据")

        upperDataLayout.addWidget(dataTabs)

        # 下部面板 - 原始数据图表
        lowerDataPanel = QWidget()
        lowerDataLayout = QVBoxLayout(lowerDataPanel)

        # 控制面板
        dataControlLayout = QHBoxLayout()

        # 文件选择下拉框
        self.fileCombo = QComboBox()
        self.fileCombo.currentIndexChanged.connect(self.updateDataView)
        dataControlLayout.addWidget(QLabel("选择文件:"))
        dataControlLayout.addWidget(self.fileCombo)

        # 峰选择下拉框（用于原始数据视图）
        self.peakCombo = QComboBox()
        self.peakCombo.currentIndexChanged.connect(self.updateDataView)
        dataControlLayout.addWidget(QLabel("选择峰:"))
        dataControlLayout.addWidget(self.peakCombo)

        lowerDataLayout.addLayout(dataControlLayout)

        # 原始数据视图
        self.dataViewWidget = pg.PlotWidget()
        self.dataViewWidget.setBackground('w')
        lowerDataLayout.addWidget(self.dataViewWidget)

        # 添加面板到分割器
        fileDataSplitter.addWidget(upperDataPanel)
        fileDataSplitter.addWidget(lowerDataPanel)

        # 设置分割器比例
        fileDataSplitter.setSizes([300, 500])

        fileDataLayout.addWidget(fileDataSplitter)

        # 添加到主标签页
        self.mainTabs.addTab(fileDataTab, "文件源数据与原始数据")

        # 添加主标签页到布局
        layout.addWidget(self.mainTabs)

        # 设置布局
        self.setLayout(layout)

        # 数据存储
        self.results = None
        self.file_names = None
        self.peaks = None
        self.raw_data = {}  # 存储原始数据
        self.file_metadata = {}  # 存储每个文件的元数据（能量、温度等）
        self.batch_results = {}  # 存储批处理结果

    def updateBatchResults(self, batch_results, peaks):
        """更新批处理结果，准备显示多个目录的数据

        参数:
            batch_results: 包含每个目录处理结果的字典
            peaks: 峰列表
        """
        print(f"接收到批处理结果，共 {len(batch_results)} 个目录")

        # 保存批处理结果和峰信息
        self.batch_results = batch_results
        self.peaks = peaks

        # 清空并更新目录选择下拉框
        self.dir_combo.clear()

        # 添加所有目录路径到下拉框
        for dir_path in batch_results.keys():
            dir_name = os.path.basename(dir_path) or dir_path  # 如果basename为空，使用完整路径
            self.dir_combo.addItem(dir_name, dir_path)  # 显示目录名，但存储完整路径作为用户数据

        # 如果有结果，选择第一个目录并显示数据
        if self.dir_combo.count() > 0:
            self.dir_combo.setCurrentIndex(0)
            # onDirSelected会通过信号自动调用
        else:
            print("没有找到批处理结果")

    def onDirSelected(self, index):
        """当用户选择不同的目录时更新显示的结果"""
        if index < 0 or not self.batch_results:
            return

        # 获取所选目录的完整路径
        dir_path = self.dir_combo.itemData(index)
        if not dir_path or dir_path not in self.batch_results:
            print(f"找不到目录 {dir_path} 的处理结果")
            return

        print(f"显示目录 {dir_path} 的处理结果")

        # 获取该目录的处理结果
        dir_data = self.batch_results[dir_path]

        # 更新结果显示
        self.updateResults(
            dir_data['results'],
            dir_data['file_names'],
            dir_data['peaks'],
            dir_data['isotope_info'],
            dir_data['raw_data'],
            dir_data['file_metadata']
        )

    def updateResults(self, results, file_names, peaks, isotope_info=None, raw_data=None, file_metadata=None):
        """更新结果表格和图表"""
        self.results = results
        self.file_names = file_names
        self.peaks = peaks

        if raw_data:
            self.raw_data = raw_data

        if file_metadata:
            self.file_metadata = file_metadata
        else:
            # 如果没有提供元数据，尝试从文件名中提取
            self.file_metadata = {}
            for filename in file_names:
                self.file_metadata[filename] = self.extract_metadata_from_filename(filename)

        # 输出元数据信息便于调试
        print("文件元数据信息:")
        for filename, metadata in self.file_metadata.items():
            print(f"文件: {filename}, 能量: {metadata.get('energy')}, 温度: {metadata.get('temperature')}")

        # 输出结果数据便于调试
        print("积分结果矩阵:")
        for i in range(min(5, len(file_names))):
            row_str = f"文件 {file_names[i]}: "
            for j in range(len(peaks)):
                row_str += f"{results[i, j]:.6f} "
            print(row_str)

        # 更新结果表格
        self.resultsTable.setRowCount(len(file_names))
        self.resultsTable.setColumnCount(len(peaks))

        # 设置列标题
        column_headers = [f"{peak['formula']} ({peak['mass']})" for peak in peaks]
        self.resultsTable.setHorizontalHeaderLabels(column_headers)

        # 设置行标题
        self.resultsTable.setVerticalHeaderLabels(file_names)

        # 填充数据
        for i in range(len(file_names)):
            for j in range(len(peaks)):
                try:
                    # 确保值不小于0
                    value = max(0.0, float(results[i, j]))
                    self.resultsTable.setItem(i, j, QTableWidgetItem(f"{value:.6f}"))
                    # 同时更新结果数组
                    self.results[i, j] = value
                except (ValueError, TypeError) as e:
                    print(f"处理结果值出错: {results[i, j]}, 错误: {e}")
                    self.resultsTable.setItem(i, j, QTableWidgetItem("0.000000"))
                    self.results[i, j] = 0.0

        # 更新元数据表格
        self.metadataTable.setRowCount(len(file_names))
        for i, file_name in enumerate(file_names):
            self.metadataTable.setItem(i, 0, QTableWidgetItem(file_name))

            # 能量和温度
            metadata = self.file_metadata.get(file_name, {})
            energy = metadata.get('energy', None)
            temperature = metadata.get('temperature', None)

            self.metadataTable.setItem(i, 1, QTableWidgetItem(f"{energy:.2f}" if energy is not None else ""))
            self.metadataTable.setItem(i, 2, QTableWidgetItem(f"{temperature:.2f}" if temperature is not None else ""))

        # 更新同位素信息表格
        self.isoInfoTable.setRowCount(len(peaks))

        # 填充同位素信息
        from .isotope_database import calculate_isotope_abundance
        for i, peak in enumerate(peaks):
            formula = peak.get('formula', '')
            mass = peak.get('mass', 0.0)

            # 如果提供了同位素信息，使用提供的数据
            if isotope_info and i < len(isotope_info):
                m1_abundance = isotope_info[i].get('m1', 0.0)
                m2_abundance = isotope_info[i].get('m2', 0.0)
            else:
                # 否则重新计算
                m1_abundance = calculate_isotope_abundance(formula, 1)
                m2_abundance = calculate_isotope_abundance(formula, 2)

            # 设置表格内容
            self.isoInfoTable.setItem(i, 0, QTableWidgetItem(formula))
            self.isoInfoTable.setItem(i, 1, QTableWidgetItem(f"{mass:.6f}"))
            self.isoInfoTable.setItem(i, 2, QTableWidgetItem(f"{m1_abundance:.6f}"))
            self.isoInfoTable.setItem(i, 3, QTableWidgetItem(f"{m2_abundance:.6f}"))

        # 更新峰选择下拉框
        self.chartCombo.clear()
        for i, peak in enumerate(peaks):
            # 使用m/z值作为峰的显示名称
            mass = peak.get('mass', 0.0)
            formula = peak.get('formula', '')
            peak_name = f"m/z {mass:.4f}" + (f" ({formula})" if formula else "")
            self.chartCombo.addItem(peak_name)

        # 更新图表
        if self.chartCombo.count() > 0:
            self.updateChart()

        # 更新峰选择框（用于原始数据视图）
        self.peakCombo.clear()
        self.peakCombo.addItems(column_headers)

        # 更新文件选择框
        self.fileCombo.clear()
        self.fileCombo.addItems(file_names)

        # 更新图表
        if len(column_headers) > 0:
            self.updateChart()
            self.updateDataView()

    def showResultsContextMenu(self, pos):
        """显示积分结果表格的右键菜单"""
        menu = self.resultsTable.createStandardContextMenu()

        # 获取选中的单元格
        selected_items = self.resultsTable.selectedItems()
        if selected_items:
            # 获取选中单元格的列索引（对应峰索引）
            column = selected_items[0].column()

            # 添加打开元素组分计算器的菜单项
            if column >= 0 and column < len(self.peaks):
                peak = self.peaks[column]
                mass = peak.get('mass', 0.0)
                formula = peak.get('formula', '')

                # 添加菜单项
                open_calculator_action = menu.addAction(f"打开元素组分计算器 (m/z={mass:.6f})")
                open_calculator_action.triggered.connect(lambda: self.openElementalCompositionCalculator(mass))

        # 显示菜单
        menu.exec(self.resultsTable.mapToGlobal(pos))

    def onResultsCellDoubleClicked(self, row, column):
        """当用户双击积分结果表格中的单元格时打开元素组分计算器"""
        # 检查列索引是否有效
        if column >= 0 and column < len(self.peaks):
            peak = self.peaks[column]
            mass = peak.get('mass', 0.0)
            # 打开元素组分计算器
            self.openElementalCompositionCalculator(mass)

    def onIsoInfoTableCellDoubleClicked(self, row, column):
        """当用户双击同位素信息表格中的单元格时打开元素组分计算器"""
        # 检查行索引是否有效
        if row >= 0 and row < len(self.peaks):
            # 获取质量数
            mass_item = self.isoInfoTable.item(row, 1)  # 第二列是m/z
            if mass_item and mass_item.text():
                try:
                    mass = float(mass_item.text())
                    # 打开元素组分计算器
                    self.openElementalCompositionCalculator(mass)
                except ValueError:
                    print(f"无法解析质量数: {mass_item.text()}")

    def getSelectedPeak(self):
        """获取当前选中的峰"""
        # 如果没有峰数据，返回None
        if not self.peaks:
            return None

        # 从图表选择下拉框获取当前选中的峰索引
        peak_index = self.chartCombo.currentIndex()
        if peak_index >= 0 and peak_index < len(self.peaks):
            return self.peaks[peak_index]

        # 从原始数据视图的峰选择下拉框获取当前选中的峰索引
        peak_index = self.peakCombo.currentIndex()
        if peak_index >= 0 and peak_index < len(self.peaks):
            return self.peaks[peak_index]

        # 如果没有选中的峰，返回第一个峰
        if len(self.peaks) > 0:
            return self.peaks[0]

        return None

    def openElementalCompositionCalculator(self, mass):
        """打开元素组分计算器"""
        try:
            # 创建元素组分计算器实例，传入初始质量数
            calculator = ElementalCompositionCalculator(initial_mass=mass)

            # 设置窗口标志，使其保持在前台
            calculator.setWindowFlags(calculator.windowFlags() | Qt.WindowType.WindowStaysOnTopHint)

            # 显示计算器
            calculator.show()

            # 自动计算
            calculator.calculate()

            print(f"已打开元素组分计算器，质量数={mass:.6f}")
        except Exception as e:
            print(f"打开元素组分计算器时出错: {str(e)}")
            import traceback
            traceback.print_exc()

    def extract_metadata_from_filename(self, filename):
        """从文件名中提取元数据（能量、温度等）"""
        metadata = {
            'energy': None,
            'temperature': None
        }

        # 尝试从文件名中提取能量
        energy_match = re.search(r'(\d+(?:\.\d+)?)eV', filename)
        if energy_match:
            metadata['energy'] = float(energy_match.group(1))

        # 尝试从文件名中提取温度
        temp_match = re.search(r'(\d+(?:\.\d+)?)K', filename)
        if temp_match:
            metadata['temperature'] = float(temp_match.group(1))

        return metadata

    def add_ionization_energy_marker(self, ie_value, compound_name):
        """添加电离能标记

        参数:
            ie_value: 电离能值（eV），如果为-1则清除所有标记
            compound_name: 化合物名称
        """
        # 如果电离能值为-1，则清除所有标记
        if ie_value == -1 and compound_name == "clear_all":
            print("清除所有电离能标记")
            # 清除所有标记
            for marker in self.ionization_energy_markers:
                vLine, text = marker
                self.graphWidget.removeItem(vLine)
                self.graphWidget.removeItem(text)
            # 清空标记列表
            self.ionization_energy_markers = []
            return

        print(f"尝试添加电离能标记: {ie_value} eV, 化合物: {compound_name}")

        # 检查是否已经有这个电离能值的标记
        for i, marker in enumerate(self.ionization_energy_markers):
            vLine, text = marker
            # 获取标记的电离能值
            marker_ie_value = vLine.value()
            # 如果已经有这个电离能值的标记，则删除它
            if abs(marker_ie_value - ie_value) < 0.01:  # 允许小误差
                print(f"删除现有电离能标记: {marker_ie_value:.2f} eV")
                self.graphWidget.removeItem(vLine)
                self.graphWidget.removeItem(text)
                self.ionization_energy_markers.pop(i)
                return

        # 强制设置横坐标类型为能量
        current_x_axis = self.xAxisCombo.currentText()
        if current_x_axis != "能量(eV)":
            print(f"当前横坐标类型为 {current_x_axis}, 将切换为能量(eV)")
            # 找到能量选项的索引
            energy_index = self.xAxisCombo.findText("能量(eV)")
            if energy_index >= 0:
                # 阻止信号触发updateChart
                self.xAxisCombo.blockSignals(True)
                self.xAxisCombo.setCurrentIndex(energy_index)
                self.xAxisCombo.blockSignals(False)
                # 手动更新图表，但不清除标记
                self.updateChartWithoutClearingMarkers()
            else:
                print(f"无法找到能量(eV)选项")
                return

        # 检查当前图表是否有效
        peak_index = self.chartCombo.currentIndex()
        if peak_index < 0:
            print(f"无法获取当前选中的峰")
            return

        # 添加电离能标记
        try:
            # 选择不同的颜色用于标记
            # 定义一组颜色
            colors = ['r', 'g', 'b', 'c', 'm', 'y', (255, 165, 0), (128, 0, 128), (0, 128, 0), (165, 42, 42)]
            # 根据当前标记数量选择颜色
            color_index = len(self.ionization_energy_markers) % len(colors)
            marker_color = colors[color_index]

            # 如果颜色是元组，转换为字符串表示
            if isinstance(marker_color, tuple):
                color_str = f"rgb{marker_color}"
            else:
                color_str = marker_color

            # 创建垂直线
            vLine = pg.InfiniteLine(pos=ie_value, angle=90, pen=pg.mkPen(marker_color, width=2, style=Qt.PenStyle.DashLine))
            self.graphWidget.addItem(vLine)

            # 获取当前图表的Y轴范围
            y_range = self.graphWidget.getViewBox().viewRange()[1]
            y_max = y_range[1]
            y_min = y_range[0]
            y_pos = y_min + (y_max - y_min) * 0.9  # 将文本放在Y轴的高处

            # 处理化合物名称，移除CAS相关信息
            display_name = compound_name
            if "[CAS:" in display_name:
                display_name = display_name.split("[CAS:")[0].strip()

            # 创建文本标签
            text = pg.TextItem(text=f"IE: {ie_value:.2f} eV\n{display_name}", color=marker_color, anchor=(0, 1))
            text.setPos(ie_value, y_pos)  # 将文本放在线的顶部
            self.graphWidget.addItem(text)

            # 保存标记以便于清除
            self.ionization_energy_markers.append((vLine, text))

            print(f"添加电离能标记: {ie_value:.2f} eV, {compound_name}")
        except Exception as e:
            print(f"添加电离能标记时出错: {str(e)}")

    def updateChartWithoutClearingMarkers(self):
        """更新图表但不清除电离能标记"""
        if self.results is None or self.file_names is None or self.peaks is None:
            return

        # 保存当前的电离能标记
        saved_markers = self.ionization_energy_markers.copy()

        # 清除当前图表
        self.graphWidget.clear()

        # 清除电离能标记列表，但保存其内容
        self.ionization_energy_markers = []

        # 正常绘制图表
        self._drawChart()

        # 重新添加保存的标记
        for marker in saved_markers:
            vLine, text = marker
            # 重新创建标记
            pos = vLine.value()
            text_content = text.textItem.toPlainText()

            # 获取原始标记的颜色
            # 尝试从线的颜色中获取
            try:
                original_color = vLine.pen.color().name()
            except:
                # 如果无法获取原始颜色，使用默认颜色
                # 选择不同的颜色用于标记
                colors = ['r', 'g', 'b', 'c', 'm', 'y', (255, 165, 0), (128, 0, 128), (0, 128, 0), (165, 42, 42)]
                # 根据当前标记数量选择颜色
                color_index = len(self.ionization_energy_markers) % len(colors)
                original_color = colors[color_index]

            # 创建新的垂直线
            new_vLine = pg.InfiniteLine(pos=pos, angle=90, pen=pg.mkPen(original_color, width=2, style=Qt.PenStyle.DashLine))
            self.graphWidget.addItem(new_vLine)

            # 获取当前图表的Y轴范围
            y_range = self.graphWidget.getViewBox().viewRange()[1]
            y_max = y_range[1]
            y_min = y_range[0]
            y_pos = y_min + (y_max - y_min) * 0.9  # 将文本放在Y轴的高处

            # 创建新的文本标签
            new_text = pg.TextItem(text=text_content, color=original_color, anchor=(0, 1))
            new_text.setPos(pos, y_pos)
            self.graphWidget.addItem(new_text)

            # 将新标记添加到列表
            self.ionization_energy_markers.append((new_vLine, new_text))

    def updateChart(self):
        """更新积分结果图表"""
        if self.results is None or self.file_names is None or self.peaks is None:
            return

        # 清除当前图表
        self.graphWidget.clear()

        # 清除电离能标记列表
        self.ionization_energy_markers = []

        # 调用共享的绘图函数
        self._drawChart()

    def _drawChart(self):
        """内部函数，实际绘制图表"""
        if self.results is None or self.file_names is None or self.peaks is None:
            return

        # 获取选中的峰索引
        peak_index = self.chartCombo.currentIndex()
        if peak_index < 0:
            return

        # 发送信号通知主窗口更新元素组成计算器
        # 找到主窗口
        main_window = None
        for widget in QApplication.topLevelWidgets():
            if hasattr(widget, 'windowTitle') and "质谱数据分析" in widget.windowTitle():
                if hasattr(widget, 'updateCalculatorMass'):
                    main_window = widget
                    break

        # 如果找到主窗口，调用更新方法
        if main_window:
            main_window.updateCalculatorMass()

        # 获取选中的横坐标类型
        x_axis_type = self.xAxisCombo.currentText()

        # 创建X轴和Y轴数据
        # 检查结果的格式
        if isinstance(self.results, dict):
            # 字典格式的结果
            y_values = []
            for file_name in self.file_names:
                if file_name in self.results:
                    result_data = self.results[file_name]
                    if peak_index < result_data.shape[0]:
                        y_values.append(float(result_data[peak_index, 0]))
                    else:
                        y_values.append(0.0)
                else:
                    y_values.append(0.0)
            y = np.array(y_values)
        else:
            # 数组格式的结果
            y = self.results[:, peak_index]

        # 根据选择的横坐标类型设置X轴数据
        if x_axis_type == "能量(eV)":
            # 从文件元数据中获取能量值
            x_values = []
            for filename in self.file_names:
                energy = self.file_metadata.get(filename, {}).get('energy')
                x_values.append(energy if energy is not None else float('nan'))

            x = np.array(x_values)

            # 移除NaN值
            mask = ~np.isnan(x)
            if not np.any(mask):
                # 如果所有值都是NaN，则使用文件索引
                x = np.arange(len(self.file_names))
                QMessageBox.warning(self, "警告", "无法从文件名中提取能量值，使用文件索引作为横坐标")
                self.xAxisCombo.setCurrentText("文件索引")
                x_axis_label = "文件索引"
            else:
                x_axis_label = "能量 (eV)"

        elif x_axis_type == "温度(K)":
            # 从文件元数据中获取温度值
            x_values = []
            for filename in self.file_names:
                temperature = self.file_metadata.get(filename, {}).get('temperature')
                x_values.append(temperature if temperature is not None else float('nan'))

            x = np.array(x_values)

            # 移除NaN值
            mask = ~np.isnan(x)
            if not np.any(mask):
                # 如果所有值都是NaN，则使用文件索引
                x = np.arange(len(self.file_names))
                QMessageBox.warning(self, "警告", "无法从文件名中提取温度值，使用文件索引作为横坐标")
                self.xAxisCombo.setCurrentText("文件索引")
                x_axis_label = "文件索引"
            else:
                x_axis_label = "温度 (K)"

        else:  # 默认使用文件索引
            x = np.arange(len(self.file_names))
            x_axis_label = "文件索引"

        # 设置图表标题和轴标签
        peak_formula = self.peaks[peak_index]['formula']
        peak_mass = self.peaks[peak_index]['mass']
        # 使用m/z值作为图表标题
        chart_title = f"m/z {peak_mass:.4f}" + (f" ({peak_formula})" if peak_formula else "")
        self.graphWidget.setTitle(chart_title)
        self.graphWidget.setLabel('left', '积分强度')
        self.graphWidget.setLabel('bottom', x_axis_label)

        # 绘制曲线
        pen = pg.mkPen(color='b', width=2)
        self.graphWidget.plot(x, y, pen=pen, symbol='o', symbolSize=8, symbolBrush='b')

    def updateDataView(self):
        """更新原始数据和积分区域显示"""
        if not self.peaks or not self.file_names:
            return

        # 清除当前图表
        self.dataViewWidget.clear()

        # 获取选中的文件索引和峰索引
        file_index = self.fileCombo.currentIndex()
        peak_index = self.peakCombo.currentIndex()

        if file_index < 0 or peak_index < 0:
            return

        # 发送信号通知主窗口更新元素组成计算器
        # 找到主窗口
        main_window = None
        for widget in QApplication.topLevelWidgets():
            if hasattr(widget, 'windowTitle') and "质谱数据分析" in widget.windowTitle():
                if hasattr(widget, 'updateCalculatorMass'):
                    main_window = widget
                    break

        # 如果找到主窗口，调用更新方法
        if main_window:
            main_window.updateCalculatorMass()

        # 获取选中的文件名
        file_name = self.file_names[file_index]

        # 检查是否有原始数据，如果没有则尝试懒加载
        if file_name not in self.raw_data:
            self.dataViewWidget.setTitle(f"加载中... {file_name}")
            QApplication.processEvents()

            try:
                # 获取主窗口实例 - 改进获取主窗口实例的方法
                main_window = None
                for widget in QApplication.topLevelWidgets():
                    # 检查是否是QMainWindow类型且窗口标题相符
                    if hasattr(widget, 'windowTitle') and "质谱数据分析" in widget.windowTitle():
                        if hasattr(widget, 'data_processor') and hasattr(widget, 'batch_results'):
                            main_window = widget
                            break

                if main_window:
                    # 获取文件路径和设置
                    data_dir = None

                    # 如果是批处理模式，尝试从当前选中的目录获取
                    if hasattr(self, 'batch_results') and self.batch_results:
                        dir_index = self.dir_combo.currentIndex()
                        if dir_index >= 0:
                            data_dir = self.dir_combo.itemData(dir_index)

                    # 如果还没有数据目录，尝试从主窗口获取
                    if not data_dir:
                        # 尝试从dir_list中获取目录（如果有多个目录）
                        if hasattr(main_window, 'dir_list') and main_window.dir_list.count() > 0:
                            data_dir = main_window.dir_list.item(0).text()

                    # 如果还没有数据目录，尝试从设置组件获取
                    if not data_dir and hasattr(main_window, 'settings_widget'):
                        settings = main_window.settings_widget.getSettings()
                        data_dir = settings.get('data_dir', '')

                    if not data_dir:
                        raise ValueError("无法确定数据目录")

                    # 获取设置
                    settings = main_window.settings_widget.getSettings()
                    file_type = settings.get('file_type', 'txt')

                    print(f"尝试加载文件: {file_name}，数据目录: {data_dir}")

                    # 尝试多种可能的路径
                    file_paths_to_try = [
                        os.path.join(data_dir, file_name),  # 直接在数据目录
                    ]

                    # 添加各种可能的子目录路径
                    for subfolder in ['PIE-1120', 'PIE-1123']:
                        file_paths_to_try.append(os.path.join(data_dir, subfolder, file_name))
                        if os.name == 'nt':  # Windows系统
                            file_paths_to_try.append(os.path.join(data_dir, f"{subfolder}\\", file_name))
                            file_paths_to_try.append(data_dir + f"\\{subfolder}\\" + file_name)

                    # 遍历所有可能的子目录
                    for root, dirs, files in os.walk(data_dir):
                        if file_name in files:
                            file_paths_to_try.append(os.path.join(root, file_name))

                    # 尝试加载文件
                    file_loaded = False
                    actual_file_path = None

                    for file_path in file_paths_to_try:
                        print(f"尝试加载文件路径: {file_path}")
                        if os.path.exists(file_path):
                            actual_file_path = file_path
                            file_loaded = True
                            break

                    if not file_loaded:
                        print(f"找不到文件: {file_name}，尝试过以下路径:")
                        for path in file_paths_to_try:
                            print(f"  - {path}")
                        self.dataViewWidget.setTitle(f"文件不存在: {file_name}")
                        return

                    print(f"成功找到文件: {actual_file_path}")

                    # 加载数据 - 保存原始数据，不应用校准
                    data_and_metadata = main_window.data_processor.parse_data_file(actual_file_path, file_type)

                    # 处理返回结果
                    if isinstance(data_and_metadata, tuple) and len(data_and_metadata) == 2:
                        data_array, metadata = data_and_metadata
                        self.raw_data[file_name] = data_array
                        # 更新元数据
                        if file_name not in self.file_metadata or not any(self.file_metadata[file_name].values()):
                            self.file_metadata[file_name] = metadata
                    else:
                        # 兼容旧版本的返回值
                        self.raw_data[file_name] = data_and_metadata
                        # 提取元数据（如果尚未有）
                        if file_name not in self.file_metadata or not any(self.file_metadata[file_name].values()):
                            self.file_metadata[file_name] = main_window.data_processor.extract_metadata_from_file(actual_file_path, file_type)

                    print(f"成功加载文件: {file_name}，数据形状: {self.raw_data[file_name].shape}")
                    print(f"元数据: {self.file_metadata[file_name]}")
                else:
                    self.dataViewWidget.setTitle(f"无法获取主窗口 - {file_name}")
                    print("找不到MainWindow实例")
                    return
            except Exception as e:
                self.dataViewWidget.setTitle(f"加载数据失败 - {file_name}: {str(e)}")
                print(f"加载数据时出错: {str(e)}")
                import traceback
                traceback.print_exc()
                return

        # 获取原始数据和选中的峰
        raw_data_item = self.raw_data[file_name]

        # 处理raw_data可能是元组(data, metadata)的情况
        if isinstance(raw_data_item, tuple) and len(raw_data_item) == 2:
            original_data = raw_data_item[0]  # 取元组的第一个元素（数据数组）
        else:
            original_data = raw_data_item

        peak = self.peaks[peak_index]

        # 确保数据至少有两列
        if original_data.shape[1] < 2:
            self.dataViewWidget.setTitle(f"数据格式错误 - {file_name}，需要至少两列数据")
            return

        # 获取质量数阈值和校准参数
        mass_threshold = 14.0  # 默认值
        calibration_params = None

        # 尝试从设置中获取质量数阈值和校准参数
        main_window = None
        for widget in QApplication.topLevelWidgets():
            # 检查是否是适当的窗口类型且标题相符
            if hasattr(widget, 'windowTitle') and "质谱数据分析" in widget.windowTitle():
                # 确保是主窗口
                if hasattr(widget, 'data_processor'):
                    main_window = widget
                    break

        if main_window:
            if hasattr(main_window, 'settings_widget'):
                settings = main_window.settings_widget.getSettings()
                mass_threshold = settings.get('mass_threshold', 14.0)

            # 直接从峰编辑器获取校准参数
            if hasattr(main_window, 'peak_editor'):
                calibration_params = main_window.peak_editor.getCalibrationParams()
                # 确保参数是浮点数
                try:
                    calibration_params = {
                        'a': float(calibration_params.get('a', '0.0')),
                        'b': float(calibration_params.get('b', '1.0')),
                        'c': float(calibration_params.get('c', '0.0'))
                    }
                    print(f"成功获取校准参数: a={calibration_params['a']}, b={calibration_params['b']}, c={calibration_params['c']}")
                except (ValueError, TypeError) as e:
                    print(f"转换校准参数为浮点数时出错: {str(e)}")
                    calibration_params = None

        # 设置图表标题
        peak_formula = peak['formula']
        peak_mass = peak['mass']
        self.dataViewWidget.setTitle(f"{peak_formula} (m/z = {peak_mass}) - {file_name}")
        self.dataViewWidget.setLabel('left', '信号强度')
        self.dataViewWidget.setLabel('bottom', '数据位置')

        try:
            # 获取原始数据
            position_data = original_data[:, 0]
            intensity_data = original_data[:, 1]

            # 计算校准后的质量数
            if calibration_params and all(k in calibration_params for k in ['a', 'b', 'c']):
                a = calibration_params['a']  # 已经确保是浮点数
                b = calibration_params['b']
                c = calibration_params['c']
                mass_data = a + b * position_data + c * position_data * position_data

                # 打印校准参数和映射示例供调试
                print(f"应用校准参数: a={a}, b={b}, c={c}")
                print(f"示例质量映射: 位置 {position_data[0]} -> 质量 {mass_data[0]}")

                # 应用质量数阈值过滤
                threshold_mask = mass_data >= mass_threshold
                position_data = position_data[threshold_mask]
                intensity_data = intensity_data[threshold_mask]
                mass_data = mass_data[threshold_mask]

                print(f"应用质量数阈值 {mass_threshold}，筛选后数据点数: {len(position_data)}")
            else:
                # 如果没有校准参数，使用原始位置
                print("未找到有效的校准参数，使用原始位置数据")
                mass_data = position_data

                # 仍应用阈值过滤（基于位置）
                threshold_mask = position_data >= mass_threshold
                position_data = position_data[threshold_mask]
                intensity_data = intensity_data[threshold_mask]
                mass_data = mass_data[threshold_mask]

            # 如果过滤后没有数据，返回提示
            if len(position_data) == 0:
                self.dataViewWidget.setTitle(f"数据中没有大于{mass_threshold}的质量数/位置 - {file_name}")
                return

            # 创建双X轴绘图区域
            self.dataViewWidget.showAxis('top', True)
            self.dataViewWidget.getAxis('top').setLabel('质量数 (m/z)')
            self.dataViewWidget.getAxis('top').setStyle(showValues=True)

            # 定义位置到质量数的映射函数
            def map_position_to_mass(position):
                if calibration_params:
                    a = calibration_params['a']  # 已经是浮点数
                    b = calibration_params['b']
                    c = calibration_params['c']
                    return a + b * position + c * position * position
                return position

            # 定义质量数到位置的反向映射函数
            def map_mass_to_position(mass):
                if calibration_params:
                    a = calibration_params['a']  # 已经是浮点数
                    b = calibration_params['b']
                    c = calibration_params['c']

                    # 对于二次方程，需要求解方程: a + b*x + c*x^2 = mass
                    if abs(c) < 1e-10:  # 如果二次项系数接近零，使用线性关系
                        return (mass - a) / b if abs(b) > 1e-10 else mass
                    else:
                        # 使用求根公式: x = (-b ± sqrt(b^2 - 4*a*c)) / (2*a)
                        # 这里 a=c, b=b, c=a-mass
                        discriminant = b*b - 4*c*(a-mass)
                        if discriminant < 0:
                            # 无实数解，返回近似值
                            return (mass - a) / b if abs(b) > 1e-10 else mass

                        # 选择较小的解（通常更合理）
                        x1 = (-b + np.sqrt(discriminant)) / (2*c)
                        x2 = (-b - np.sqrt(discriminant)) / (2*c)
                        return min(x1, x2) if x1 > 0 and x2 > 0 else max(x1, x2)
                return mass

            # 建立位置和质量数的对应关系
            mass_range = np.linspace(min(mass_data), max(mass_data), 10)
            position_range = np.array([map_mass_to_position(m) for m in mass_range])

            # 绘制主数据曲线（位置为X轴）
            pen = pg.mkPen(color='b', width=1)
            self.dataViewWidget.plot(position_data, intensity_data, pen=pen)

            # 设置底部轴范围
            bottom_min, bottom_max = min(position_data), max(position_data)
            self.dataViewWidget.setXRange(bottom_min, bottom_max)

            # 设置顶部轴的刻度
            top_min, top_max = map_position_to_mass(bottom_min), map_position_to_mass(bottom_max)
            top_axis = self.dataViewWidget.getAxis('top')
            top_axis.setRange(top_min, top_max)

            # 生成顶部轴的刻度标签
            ticks = []
            for pos in position_range:
                mass = map_position_to_mass(pos)
                ticks.append((pos, f"{mass:.2f}"))
            top_axis.setTicks([ticks])

            # 获取积分边界
            left_bound = peak.get('left_bound', 0.0)
            right_bound = peak.get('right_bound', 0.0)

            # 判断边界值是质量数还是位置
            if calibration_params and abs(left_bound - peak_mass) < abs(left_bound - peak.get('position', 0.0)):
                # 如果边界值更接近质量数，则转换为位置
                left_position = map_mass_to_position(left_bound)
                right_position = map_mass_to_position(right_bound)
                print(f"边界值似乎是质量值，需要转换为位置值")
                print(f"转换积分边界: {left_bound:.2f}-{right_bound:.2f} -> {left_position:.2f}-{right_position:.2f}")
            else:
                # 边界值已经是位置
                left_position = left_bound
                right_position = right_bound
                print(f"边界值似乎是原始位置值，需要转换为质量值")
                print(f"转换积分边界: {left_bound:.2f}-{right_bound:.2f} -> {map_position_to_mass(left_bound):.2f}-{map_position_to_mass(right_bound):.2f}")

            # 映射回质量数用于显示
            left_mass = map_position_to_mass(left_position)
            right_mass = map_position_to_mass(right_position)

            # 获取积分区域内的数据
            mask = (position_data >= left_position) & (position_data <= right_position)
            integration_data_position = position_data[mask]
            integration_data_intensity = intensity_data[mask]

            # 如果有积分数据，显示积分区域
            if len(integration_data_position) > 0:
                # 绘制积分区域
                pen = pg.mkPen(color='r', width=2)
                self.dataViewWidget.plot(integration_data_position, integration_data_intensity, pen=pen)

                # 添加垂直线标记边界
                vLine1 = pg.InfiniteLine(pos=left_position, angle=90, pen=pg.mkPen('g', width=1, style=Qt.PenStyle.DashLine))
                vLine2 = pg.InfiniteLine(pos=right_position, angle=90, pen=pg.mkPen('g', width=1, style=Qt.PenStyle.DashLine))
                self.dataViewWidget.addItem(vLine1)
                self.dataViewWidget.addItem(vLine2)

                # 显示积分区域信息
                peak_sum = np.sum(integration_data_intensity)
                info_text = f"积分区域: {left_position:.2f}-{right_position:.2f} (质量: {left_mass:.2f}-{right_mass:.2f}), 点数: {len(integration_data_position)}, 总和: {peak_sum:.2f}"
                # 使用m/z值作为标题
                title = f"m/z {peak_mass:.4f}" + (f" ({peak_formula})" if peak_formula else "")
                self.dataViewWidget.setTitle(f"{title} - {file_name} - {info_text}")

            # 处理背景区域（如果有）
            bg_left = peak.get('bg_left')
            bg_right = peak.get('bg_right')

            if bg_left is not None and bg_right is not None:
                # 判断背景边界值是质量数还是位置
                if calibration_params and abs(bg_left - peak_mass) < abs(bg_left - peak.get('position', 0.0)):
                    # 如果背景边界值更接近质量数，则转换为位置
                    bg_left_position = map_mass_to_position(bg_left)
                    bg_right_position = map_mass_to_position(bg_right)
                else:
                    # 背景边界值已经是位置
                    bg_left_position = bg_left
                    bg_right_position = bg_right

                # 映射回质量数用于显示
                bg_left_mass = map_position_to_mass(bg_left_position)
                bg_right_mass = map_position_to_mass(bg_right_position)

                # 添加背景区域标记
                vLine3 = pg.InfiniteLine(pos=bg_left_position, angle=90, pen=pg.mkPen('y', width=1, style=Qt.PenStyle.DashLine))
                vLine4 = pg.InfiniteLine(pos=bg_right_position, angle=90, pen=pg.mkPen('y', width=1, style=Qt.PenStyle.DashLine))
                self.dataViewWidget.addItem(vLine3)
                self.dataViewWidget.addItem(vLine4)

                # 获取背景区域内的数据
                bg_mask = (position_data >= bg_left_position) & (position_data <= bg_right_position)
                bg_data_position = position_data[bg_mask]
                bg_data_intensity = intensity_data[bg_mask]

                # 如果有背景数据，显示背景区域
                if len(bg_data_position) > 0:
                    # 绘制背景区域
                    pen = pg.mkPen(color='m', width=2)
                    self.dataViewWidget.plot(bg_data_position, bg_data_intensity, pen=pen)

                    # 显示背景区域信息
                    bg_height = np.mean(bg_data_intensity)
                    bg_contribution = bg_height * len(integration_data_position)
                    net_area = max(0, peak_sum - bg_contribution)

                    bg_info = f"背景: {bg_height:.2f} × {len(integration_data_position)} = {bg_contribution:.2f}, 净面积: {net_area:.2f}"
                    bg_pos_info = f"(背景位置: {bg_left_position:.2f}-{bg_right_position:.2f}, 质量: {bg_left_mass:.2f}-{bg_right_mass:.2f})"

                    title = self.dataViewWidget.titleLabel.text()
                    self.dataViewWidget.setTitle(f"{title} - {bg_info} {bg_pos_info}")

        except Exception as e:
            self.dataViewWidget.setTitle(f"绘图错误: {str(e)}")
            print(f"绘制文件 {file_name} 数据时出错: {str(e)}")
            import traceback
            traceback.print_exc()

    def exportResults(self):
        """导出积分结果到CSV文件"""
        if self.results is None or self.file_names is None or self.peaks is None:
            QMessageBox.warning(self, "错误", "没有可导出的结果")
            return

        # 创建DataFrame
        column_headers = [f"{peak['formula']} ({peak['mass']})" for peak in self.peaks]
        df = pd.DataFrame(self.results, index=self.file_names, columns=column_headers)

        # 选择保存路径
        file_path, _ = QFileDialog.getSaveFileName(self, "导出积分结果", "", "CSV文件 (*.csv)")
        if not file_path:
            return

        # 导出CSV
        df.to_csv(file_path)
        QMessageBox.information(self, "成功", f"已导出积分结果到 {file_path}")

    def exportMetadata(self):
        """导出文件元数据到CSV文件"""
        if not self.file_names or not self.file_metadata:
            QMessageBox.warning(self, "错误", "没有可导出的元数据")
            return

        # 创建DataFrame
        data = {
            'filename': [],
            'energy': [],
            'temperature': []
        }

        for filename in self.file_names:
            metadata = self.file_metadata.get(filename, {})
            data['filename'].append(filename)
            data['energy'].append(metadata.get('energy', None))
            data['temperature'].append(metadata.get('temperature', None))

        df = pd.DataFrame(data)

        # 选择保存路径
        file_path, _ = QFileDialog.getSaveFileName(self, "导出文件元数据", "", "CSV文件 (*.csv)")
        if not file_path:
            return

        # 导出CSV
        df.to_csv(file_path, index=False)
        QMessageBox.information(self, "成功", f"已导出文件元数据到 {file_path}")

    def onIsoInfoTableCellChanged(self, row, column):
        """当同位素信息表格单元格内容改变时更新相关数据"""
        # 断开信号，避免循环
        self.isoInfoTable.blockSignals(True)

        # 如果是分子式列改变，则同步到峰编辑器
        if column == 0 and self.peaks is not None:
            try:
                formula_item = self.isoInfoTable.item(row, column)
                if formula_item and formula_item.text():
                    new_formula = formula_item.text()

                    # 获取主窗口实例
                    main_window = None
                    for widget in QApplication.topLevelWidgets():
                        if hasattr(widget, 'windowTitle') and "质谱数据分析" in widget.windowTitle():
                            if hasattr(widget, 'peak_editor'):
                                main_window = widget
                                break

                    if main_window and hasattr(main_window, 'peak_editor'):
                        # 更新峰编辑器中的分子式
                        peak_editor = main_window.peak_editor
                        peak_editor.table.blockSignals(True)
                        peak_editor.table.setItem(row, 2, QTableWidgetItem(new_formula))
                        peak_editor.table.blockSignals(False)

                        # 更新peaks数组
                        if row < len(self.peaks):
                            self.peaks[row]['formula'] = new_formula

                            # 重新计算同位素丰度
                            from .isotope_database import calculate_isotope_abundance
                            m1_abundance = calculate_isotope_abundance(new_formula, 1)
                            m2_abundance = calculate_isotope_abundance(new_formula, 2)

                            # 更新同位素信息表格
                            self.isoInfoTable.setItem(row, 2, QTableWidgetItem(f"{m1_abundance:.6f}"))
                            self.isoInfoTable.setItem(row, 3, QTableWidgetItem(f"{m2_abundance:.6f}"))
            except Exception as e:
                print(f"更新分子式时出错: {str(e)}")
                import traceback
                traceback.print_exc()

        # 重新连接信号
        self.isoInfoTable.blockSignals(False)

    # 已删除connectToMainProcessAllData方法，改为使用信号和槽机制



    def updateResultsTable(self):
        """更新结果表格显示"""
        if self.results is None or self.file_names is None or self.peaks is None:
            return

        # 检查结果的格式
        if isinstance(self.results, dict):
            # 字典格式的结果
            for i, file_name in enumerate(self.file_names):
                if file_name in self.results:
                    result_data = self.results[file_name]
                    for j in range(len(self.peaks)):
                        try:
                            # 确保值不小于0
                            if j < len(result_data):
                                value = max(0.0, float(result_data[j, 0]))
                                self.resultsTable.setItem(i, j, QTableWidgetItem(f"{value:.6f}"))
                            else:
                                self.resultsTable.setItem(i, j, QTableWidgetItem("0.000000"))
                        except (ValueError, TypeError, IndexError) as e:
                            print(f"处理结果值出错: {file_name}, 峰 {j}, 错误: {e}")
                            self.resultsTable.setItem(i, j, QTableWidgetItem("0.000000"))
                else:
                    # 文件不在结果中
                    for j in range(len(self.peaks)):
                        self.resultsTable.setItem(i, j, QTableWidgetItem("0.000000"))
        else:
            # 数组格式的结果
            for i in range(len(self.file_names)):
                for j in range(len(self.peaks)):
                    try:
                        # 确保值不小于0
                        value = max(0.0, float(self.results[i, j]))
                        self.resultsTable.setItem(i, j, QTableWidgetItem(f"{value:.6f}"))
                    except (ValueError, TypeError) as e:
                        print(f"处理结果值出错: {self.results[i, j]}, 错误: {e}")
                        self.resultsTable.setItem(i, j, QTableWidgetItem("0.000000"))

    def clearAll(self):
        """清空所有结果数据和图表"""
        # 清空数据
        self.results = None
        self.file_names = None
        self.peaks = None
        self.raw_data = {}
        self.file_metadata = {}
        self.batch_results = {}

        # 清空目录选择下拉框
        self.dir_combo.clear()

        # 清空积分结果表格
        self.resultsTable.setRowCount(0)
        self.resultsTable.setColumnCount(0)

        # 清空元数据表格
        self.metadataTable.setRowCount(0)

        # 清空同位素信息表格
        self.isoInfoTable.setRowCount(0)

        # 清空峰选择下拉框
        self.chartCombo.clear()

        # 清空图表
        self.graphWidget.clear()
        self.dataViewWidget.clear()

        print("已清空所有结果数据和图表")

class ProcessWorker(QObject):
    """数据处理工作线程"""
    progress = pyqtSignal(float, str)
    finished = pyqtSignal(np.ndarray, list, list, list, dict, dict)
    error = pyqtSignal(str)

    def __init__(self, data_processor, data_dir, peaks, settings):
        super().__init__()
        self.data_processor = data_processor
        self.data_dir = data_dir
        self.peaks = peaks
        self.settings = settings

    def run(self):
        try:
            # 定义进度回调函数
            def progress_callback(fraction, status):
                self.progress.emit(fraction, status)

            # 使用改进后的文件搜索机制处理数据
            results = None
            file_names = []
            file_metadata = {}

            # 获取数据目录中的所有文件
            all_files = []
            file_type = self.settings.get('file_type', 'txt')

            # 递归搜索文件
            for root, dirs, files in os.walk(self.data_dir):
                for file in files:
                    if file.endswith(f".{file_type}"):
                        file_path = os.path.join(root, file)
                        file_names.append(file)
                        all_files.append(file_path)

            # 如果没有找到文件，尝试在指定目录下搜索
            if not all_files and os.path.exists(os.path.join(self.data_dir, "PIE-1120")):
                pie_dir = os.path.join(self.data_dir, "PIE-1120")
                for root, dirs, files in os.walk(pie_dir):
                    for file in files:
                        if file.endswith(f".{file_type}"):
                            file_path = os.path.join(root, file)
                            file_names.append(file)
                            all_files.append(file_path)

            # 确保找到了文件
            if not all_files:
                raise ValueError(f"在数据目录 {self.data_dir} 及其子目录中找不到{file_type}文件")

            # 排序文件名
            file_names = sorted(file_names)
            all_files = sorted(all_files, key=lambda x: os.path.basename(x))

            # 设置总文件数
            total_files = len(all_files)
            print(f"共找到 {total_files} 个{file_type}文件")

            # 为结果矩阵分配内存
            results = np.zeros((total_files, len(self.peaks)))

            # 处理每个文件
            for i, (file_name, file_path) in enumerate(zip(file_names, all_files)):
                # 更新进度
                progress = i / total_files
                progress_callback(progress, f"处理文件 {i+1}/{total_files}: {file_name}")
                print(f"开始处理文件 {i+1}/{total_files}: {file_name}")

                try:
                    # 解析数据文件
                    data = self.data_processor.parse_data_file(file_path, file_type)

                    # 提取元数据
                    metadata = self.data_processor.extract_metadata_from_file(file_path, file_type)
                    file_metadata[file_name] = metadata

                    # 处理校准参数
                    if 'calibration' in self.settings:
                        calibration_params = self.settings['calibration']
                    else:
                        calibration_params = {'a': '0.0', 'b': '1.0', 'c': '0.0'}

                    # 对数据应用校准
                    calibrated_data = self.data_processor.apply_calibration(data, calibration_params)

                    # 如果设置了质量数阈值，过滤数据
                    if 'mass_threshold' in self.settings:
                        threshold = self.settings['mass_threshold']
                        calibrated_data = self.data_processor.apply_mass_threshold(calibrated_data, threshold)

                    # 计算每个峰的积分
                    for j, peak in enumerate(self.peaks):
                        # 获取积分边界和背景区域
                        left_bound = peak.get('left_bound')
                        right_bound = peak.get('right_bound')
                        bg_left = peak.get('bg_left')
                        bg_right = peak.get('bg_right')

                        # 计算积分
                        area = self.data_processor.integrate_peak(
                            calibrated_data,
                            left_bound,
                            right_bound,
                            bg_left,
                            bg_right
                        )

                        # 存储结果
                        results[i, j] = area

                except Exception as e:
                    print(f"处理文件 {file_name} 时出错: {str(e)}")
                    # 继续处理下一个文件

            # 更新进度为完成
            progress_callback(1.0, "处理完成")

            # 计算同位素信息
            isotope_info = []
            for peak in self.peaks:
                formula = peak.get('formula', '')
                mass = peak.get('mass', 0.0)

                # 计算同位素丰度
                m1_abundance = self.data_processor.calculate_isotope_abundance(formula, 1, self.settings)
                m2_abundance = self.data_processor.calculate_isotope_abundance(formula, 2, self.settings)

                isotope_info.append({
                    'formula': formula,
                    'mass': mass,
                    'm1': m1_abundance,
                    'm2': m2_abundance
                })

            # 初始化原始数据字典（用于延迟加载）
            raw_data = {}

            # 输出结果矩阵
            print("结果矩阵:")
            for i in range(min(5, len(file_names))):
                row_str = f"文件 {file_names[i]}: "
                for j in range(len(self.peaks)):
                    row_str += f"{results[i, j]:.2f} "
                print(row_str)

            # 发送完成信号
            self.finished.emit(results, file_names, self.peaks, isotope_info, raw_data, file_metadata)

        except Exception as e:
            # 发送错误信号
            import traceback
            traceback_str = traceback.format_exc()
            error_message = f"处理数据时出错: {str(e)}\n{traceback_str}"
            self.error.emit(error_message)