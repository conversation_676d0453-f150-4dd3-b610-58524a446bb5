"""
数据库管理对话框
允许用户查看、修改和删除数据库中的化合物信息
"""

import json
import os
import io
import re
from PyQt6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QTabWidget, QWidget, QLabel, 
    QLineEdit, QTextEdit, QPushButton, QGroupBox, QFormLayout,
    QMessageBox, QTableWidget, QTableWidgetItem, QSplitter,
    QHeaderView, QAbstractItemView, QComboBox,
    QCheckBox, QScrollArea, QSpinBox, QDoubleSpinBox, QApplication
)
from PyQt6.QtGui import QDoubleValidator, QPixmap, QAction
from PyQt6.QtCore import Qt, pyqtSignal

# 尝试导入RDKit用于分子结构绘制
try:
    from rdkit import Chem
    from rdkit.Chem import Draw
    from PIL import Image, ImageQt
    RDKIT_AVAILABLE = True
except ImportError:
    RDKIT_AVAILABLE = False
    print("Warning: RDKit not available. Molecular structure display will be disabled.")


class DatabaseManagerDialog(QDialog):
    """数据库管理对话框"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("数据库管理")
        self.setGeometry(150, 150, 1200, 800)
        self.setModal(True)
        
        # 数据缓存
        self.ie_data = []  # 电离能数据
        self.cs_data = {}  # 光电离截面数据
        self.current_compound = None  # 当前选中的化合物
        
        self.initUI()
        self.loadData()
        
    def initUI(self):
        """初始化用户界面"""
        layout = QVBoxLayout()
        
        # 创建选项卡控件
        self.tab_widget = QTabWidget()
        
        # 创建电离能数据库管理选项卡
        self.ie_tab = self.createIonizationEnergyTab()
        self.tab_widget.addTab(self.ie_tab, "电离能数据库")
        
        # 创建光电离截面数据库管理选项卡
        self.cs_tab = self.createCrossSectionTab()
        self.tab_widget.addTab(self.cs_tab, "光电离截面数据库")
        
        layout.addWidget(self.tab_widget)
        
        # 按钮布局
        button_layout = QHBoxLayout()
        
        # 刷新数据按钮
        self.refresh_btn = QPushButton("刷新数据")
        self.refresh_btn.clicked.connect(self.loadData)
        button_layout.addWidget(self.refresh_btn)
        
        button_layout.addStretch()
        
        # 关闭按钮
        self.close_btn = QPushButton("关闭")
        self.close_btn.clicked.connect(self.accept)
        button_layout.addWidget(self.close_btn)
        
        layout.addLayout(button_layout)
        
        self.setLayout(layout)
        
    def createIonizationEnergyTab(self):
        """创建电离能数据库管理选项卡"""
        tab = QWidget()
        layout = QHBoxLayout(tab)
        
        # 创建分割器
        splitter = QSplitter(Qt.Orientation.Horizontal)
        
        # 左侧：化合物列表
        left_widget = QWidget()
        left_layout = QVBoxLayout(left_widget)
        
        # 搜索框
        search_layout = QVBoxLayout()
        
        # 文本搜索
        text_search_layout = QHBoxLayout()
        text_search_layout.addWidget(QLabel("文本搜索:"))
        self.ie_search = QLineEdit()
        self.ie_search.setPlaceholderText("输入化合物名称、分子式或SMILES...")
        self.ie_search.textChanged.connect(self.filterIEData)
        text_search_layout.addWidget(self.ie_search)
        search_layout.addLayout(text_search_layout)
        
        # 质量范围搜索
        mass_search_layout = QHBoxLayout()
        mass_search_layout.addWidget(QLabel("分子量范围:"))
        self.ie_mass_min = QDoubleSpinBox()
        self.ie_mass_min.setMinimum(0)
        self.ie_mass_min.setMaximum(1000)
        self.ie_mass_min.setValue(0)
        self.ie_mass_min.setSuffix(" Da")
        self.ie_mass_min.valueChanged.connect(self.filterIEData)
        mass_search_layout.addWidget(self.ie_mass_min)
        
        mass_search_layout.addWidget(QLabel("到"))
        self.ie_mass_max = QDoubleSpinBox()
        self.ie_mass_max.setMinimum(0)
        self.ie_mass_max.setMaximum(1000)
        self.ie_mass_max.setValue(1000)
        self.ie_mass_max.setSuffix(" Da")
        self.ie_mass_max.valueChanged.connect(self.filterIEData)
        mass_search_layout.addWidget(self.ie_mass_max)
        
        self.ie_reset_filter_btn = QPushButton("重置筛选")
        self.ie_reset_filter_btn.clicked.connect(self.resetIEFilter)
        mass_search_layout.addWidget(self.ie_reset_filter_btn)
        
        mass_search_layout.addStretch()
        search_layout.addLayout(mass_search_layout)
        
        left_layout.addLayout(search_layout)
        
        # 化合物列表表格
        self.ie_table = QTableWidget()
        self.ie_table.setColumnCount(4)
        self.ie_table.setHorizontalHeaderLabels(["名称", "分子式", "分子量", "ID"])
        self.ie_table.setSelectionBehavior(QAbstractItemView.SelectionBehavior.SelectRows)
        self.ie_table.setAlternatingRowColors(True)
        
        # 调整列宽
        header = self.ie_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.ResizeMode.Stretch)
        header.setSectionResizeMode(1, QHeaderView.ResizeMode.ResizeToContents)
        header.setSectionResizeMode(2, QHeaderView.ResizeMode.ResizeToContents)
        header.setSectionResizeMode(3, QHeaderView.ResizeMode.ResizeToContents)
        
        self.ie_table.selectionModel().currentRowChanged.connect(self.onIECompoundSelected)
        left_layout.addWidget(self.ie_table)
        
        # 操作按钮
        ie_button_layout = QHBoxLayout()
        self.ie_edit_btn = QPushButton("编辑")
        self.ie_edit_btn.clicked.connect(self.editIECompound)
        self.ie_edit_btn.setEnabled(False)
        ie_button_layout.addWidget(self.ie_edit_btn)
        
        self.ie_delete_btn = QPushButton("删除")
        self.ie_delete_btn.clicked.connect(self.deleteIECompound)
        self.ie_delete_btn.setEnabled(False)
        ie_button_layout.addWidget(self.ie_delete_btn)
        
        ie_button_layout.addStretch()
        left_layout.addLayout(ie_button_layout)
        
        splitter.addWidget(left_widget)
        
        # 右侧：化合物详细信息
        right_widget = QWidget()
        right_layout = QVBoxLayout(right_widget)
        
        right_layout.addWidget(QLabel("化合物详细信息"))
        
        # 创建滚动区域
        scroll = QScrollArea()
        scroll_widget = QWidget()
        self.ie_detail_layout = QVBoxLayout(scroll_widget)
        
        # 初始化详细信息显示区域
        initial_label = QLabel("请选择一个化合物查看详细信息")
        initial_label.setWordWrap(True)
        initial_label.setAlignment(Qt.AlignmentFlag.AlignTop)
        self.ie_detail_layout.addWidget(initial_label)
        
        scroll.setWidget(scroll_widget)
        scroll.setWidgetResizable(True)
        right_layout.addWidget(scroll)
        
        splitter.addWidget(right_widget)
        
        # 设置分割器比例
        splitter.setSizes([600, 400])
        
        layout.addWidget(splitter)
        
        return tab
        
    def createCrossSectionTab(self):
        """创建光电离截面数据库管理选项卡"""
        tab = QWidget()
        layout = QHBoxLayout(tab)
        
        # 创建分割器
        splitter = QSplitter(Qt.Orientation.Horizontal)
        
        # 左侧：化合物列表
        left_widget = QWidget()
        left_layout = QVBoxLayout(left_widget)
        
        # 搜索框
        search_layout = QVBoxLayout()
        
        # 文本搜索
        text_search_layout = QHBoxLayout()
        text_search_layout.addWidget(QLabel("文本搜索:"))
        self.cs_search = QLineEdit()
        self.cs_search.setPlaceholderText("输入化合物名称、分子式或来源...")
        self.cs_search.textChanged.connect(self.filterCSData)
        text_search_layout.addWidget(self.cs_search)
        search_layout.addLayout(text_search_layout)
        
        # 质量数范围搜索
        mass_search_layout = QHBoxLayout()
        mass_search_layout.addWidget(QLabel("质量数范围:"))
        self.cs_mass_min = QDoubleSpinBox()
        self.cs_mass_min.setMinimum(0)
        self.cs_mass_min.setMaximum(1000)
        self.cs_mass_min.setValue(0)
        self.cs_mass_min.setSuffix(" Da")
        self.cs_mass_min.valueChanged.connect(self.filterCSData)
        mass_search_layout.addWidget(self.cs_mass_min)
        
        mass_search_layout.addWidget(QLabel("到"))
        self.cs_mass_max = QDoubleSpinBox()
        self.cs_mass_max.setMinimum(0)
        self.cs_mass_max.setMaximum(1000)
        self.cs_mass_max.setValue(1000)
        self.cs_mass_max.setSuffix(" Da")
        self.cs_mass_max.valueChanged.connect(self.filterCSData)
        mass_search_layout.addWidget(self.cs_mass_max)
        
        self.cs_reset_filter_btn = QPushButton("重置筛选")
        self.cs_reset_filter_btn.clicked.connect(self.resetCSFilter)
        mass_search_layout.addWidget(self.cs_reset_filter_btn)
        
        mass_search_layout.addStretch()
        search_layout.addLayout(mass_search_layout)
        
        left_layout.addLayout(search_layout)
        
        # 化合物列表表格
        self.cs_table = QTableWidget()
        self.cs_table.setColumnCount(4)
        self.cs_table.setHorizontalHeaderLabels(["名称", "分子式", "质量数", "数据来源"])
        self.cs_table.setSelectionBehavior(QAbstractItemView.SelectionBehavior.SelectRows)
        self.cs_table.setAlternatingRowColors(True)
        
        # 调整列宽
        header = self.cs_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.ResizeMode.Stretch)
        header.setSectionResizeMode(1, QHeaderView.ResizeMode.ResizeToContents)
        header.setSectionResizeMode(2, QHeaderView.ResizeMode.ResizeToContents)
        header.setSectionResizeMode(3, QHeaderView.ResizeMode.Stretch)
        
        self.cs_table.selectionModel().currentRowChanged.connect(self.onCSCompoundSelected)
        left_layout.addWidget(self.cs_table)
        
        # 操作按钮
        cs_button_layout = QHBoxLayout()
        self.cs_edit_btn = QPushButton("编辑")
        self.cs_edit_btn.clicked.connect(self.editCSCompound)
        self.cs_edit_btn.setEnabled(False)
        cs_button_layout.addWidget(self.cs_edit_btn)
        
        self.cs_delete_btn = QPushButton("删除")
        self.cs_delete_btn.clicked.connect(self.deleteCSCompound)
        self.cs_delete_btn.setEnabled(False)
        cs_button_layout.addWidget(self.cs_delete_btn)
        
        cs_button_layout.addStretch()
        left_layout.addLayout(cs_button_layout)
        
        splitter.addWidget(left_widget)
        
        # 右侧：化合物详细信息
        right_widget = QWidget()
        right_layout = QVBoxLayout(right_widget)
        
        right_layout.addWidget(QLabel("化合物详细信息"))
        
        # 创建滚动区域
        scroll = QScrollArea()
        scroll_widget = QWidget()
        self.cs_detail_layout = QVBoxLayout(scroll_widget)
        
        # 初始化详细信息显示区域
        initial_label = QLabel("请选择一个化合物查看详细信息")
        initial_label.setWordWrap(True)
        initial_label.setAlignment(Qt.AlignmentFlag.AlignTop)
        self.cs_detail_layout.addWidget(initial_label)
        
        scroll.setWidget(scroll_widget)
        scroll.setWidgetResizable(True)
        right_layout.addWidget(scroll)
        
        splitter.addWidget(right_widget)
        
        # 设置分割器比例
        splitter.setSizes([600, 400])
        
        layout.addWidget(splitter)
        
        return tab
        
    def loadData(self):
        """加载数据库数据"""
        self.loadIEData()
        self.loadCSData()
        
    def loadIEData(self):
        """加载电离能数据"""
        try:
            ie_path = "database/nist_compounds.json"
            backup_paths = [
                "database/nist_compounds_0430.json",
                "database/nist_compounds0413.json", 
                "database/nist_compounds1.json"
            ]
            
            # 尝试加载主文件
            if os.path.exists(ie_path):
                try:
                    with open(ie_path, 'r', encoding='utf-8') as f:
                        self.ie_data = json.load(f)
                    self.populateIETable()
                    print(f"已加载 {len(self.ie_data)} 个电离能化合物")
                    return
                except json.JSONDecodeError as e:
                    print(f"主数据库文件损坏: {str(e)}")
                    
                    # 尝试使用备份文件
                    for backup_path in backup_paths:
                        if os.path.exists(backup_path):
                            try:
                                print(f"尝试使用备份文件: {backup_path}")
                                with open(backup_path, 'r', encoding='utf-8') as f:
                                    self.ie_data = json.load(f)
                                self.populateIETable()
                                print(f"已从备份文件加载 {len(self.ie_data)} 个电离能化合物")
                                
                                # 询问是否要恢复主文件
                                reply = QMessageBox.question(self, "数据库修复",
                                                           f"主数据库文件已损坏，但成功从备份文件加载了数据。\n\n"
                                                           f"是否要将备份数据复制到主文件以修复数据库？",
                                                           QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No)
                                
                                if reply == QMessageBox.StandardButton.Yes:
                                    self.saveIEData()
                                    QMessageBox.information(self, "修复完成", "数据库已成功修复！")
                                
                                return
                            except Exception as backup_e:
                                print(f"备份文件 {backup_path} 也有问题: {str(backup_e)}")
                                continue
                    
                    # 所有文件都有问题
                    raise Exception(f"主文件和所有备份文件都无法加载")
            else:
                self.ie_data = []
                QMessageBox.warning(self, "警告", f"电离能数据库文件不存在: {ie_path}")
                
        except Exception as e:
            error_msg = f"加载电离能数据时出错: {str(e)}"
            print(error_msg)
            import traceback
            traceback.print_exc()
            QMessageBox.critical(self, "错误", error_msg)
            self.ie_data = []
            
    def loadCSData(self):
        """加载光电离截面数据"""
        try:
            cs_path = "database/compounds_data.json"
            if os.path.exists(cs_path):
                with open(cs_path, 'r', encoding='utf-8') as f:
                    self.cs_data = json.load(f)
                    
                self.populateCSTable()
                print(f"已加载 {len(self.cs_data)} 个光电离截面化合物")
            else:
                self.cs_data = {}
                QMessageBox.warning(self, "警告", f"光电离截面数据库文件不存在: {cs_path}")
                
        except Exception as e:
            error_msg = f"加载光电离截面数据时出错: {str(e)}"
            print(error_msg)
            import traceback
            traceback.print_exc()
            QMessageBox.critical(self, "错误", error_msg)
            self.cs_data = {}
            
    def populateIETable(self):
        """填充电离能数据表格"""
        self.ie_table.setRowCount(len(self.ie_data))
        
        for row, compound in enumerate(self.ie_data):
            # 名称
            name_item = QTableWidgetItem(compound.get('name', ''))
            name_item.setData(Qt.ItemDataRole.UserRole, row)  # 存储原始索引
            self.ie_table.setItem(row, 0, name_item)
            
            # 分子式
            self.ie_table.setItem(row, 1, QTableWidgetItem(compound.get('formula', '')))
            
            # 分子量
            mol_weight = compound.get('mol_weight', 0)
            mol_weight_str = ''
            if mol_weight:
                try:
                    mol_weight_float = float(mol_weight)
                    mol_weight_str = f"{mol_weight_float:.4f}"
                except (ValueError, TypeError):
                    mol_weight_str = str(mol_weight)
            self.ie_table.setItem(row, 2, QTableWidgetItem(mol_weight_str))
            
            # ID
            self.ie_table.setItem(row, 3, QTableWidgetItem(compound.get('id', '')))
            
    def populateCSTable(self):
        """填充光电离截面数据表格"""
        self.cs_table.setRowCount(len(self.cs_data))
        
        for row, (key, compound) in enumerate(self.cs_data.items()):
            # 名称
            name_item = QTableWidgetItem(compound.get('name', ''))
            name_item.setData(Qt.ItemDataRole.UserRole, key)  # 存储键名
            self.cs_table.setItem(row, 0, name_item)
            
            # 分子式
            self.cs_table.setItem(row, 1, QTableWidgetItem(compound.get('formula', '')))
            
            # 质量数
            mass_number = compound.get('mass_number', 0)
            mass_number_str = ''
            if mass_number:
                try:
                    mass_number_float = float(mass_number)
                    mass_number_str = f"{mass_number_float:.1f}"
                except (ValueError, TypeError):
                    mass_number_str = str(mass_number)
            self.cs_table.setItem(row, 2, QTableWidgetItem(mass_number_str))
            
            # 数据来源
            source = compound.get('source', '')
            # 截断过长的来源信息
            if len(source) > 50:
                source = source[:47] + "..."
            self.cs_table.setItem(row, 3, QTableWidgetItem(source))
            
    def filterIEData(self):
        """筛选电离能数据"""
        search_text = self.ie_search.text().lower()
        mass_min = self.ie_mass_min.value()
        mass_max = self.ie_mass_max.value()
        
        for row in range(self.ie_table.rowCount()):
            should_show = True
            
            # 获取原始数据索引
            original_index = self.ie_table.item(row, 0).data(Qt.ItemDataRole.UserRole)
            if 0 <= original_index < len(self.ie_data):
                compound = self.ie_data[original_index]
                
                # 文本搜索
                if search_text:
                    name = self.ie_table.item(row, 0).text().lower()
                    formula = self.ie_table.item(row, 1).text().lower()
                    compound_id = self.ie_table.item(row, 3).text().lower()
                    smiles = compound.get('smiles', '').lower()
                    
                    text_match = (search_text in name or 
                                search_text in formula or 
                                search_text in compound_id or
                                search_text in smiles)
                    if not text_match:
                        should_show = False
                
                # 质量范围搜索
                if should_show and (mass_min > 0 or mass_max < 1000):
                    mol_weight = compound.get('mol_weight', 0)
                    try:
                        mol_weight_float = float(mol_weight) if mol_weight else 0
                        if not (mass_min <= mol_weight_float <= mass_max):
                            should_show = False
                    except (ValueError, TypeError):
                        # 如果分子量不是有效数字，按质量范围筛选时隐藏
                        if mass_min > 0 or mass_max < 1000:
                            should_show = False
                                 
            self.ie_table.setRowHidden(row, not should_show)
            
    def filterCSData(self):
        """筛选光电离截面数据"""
        search_text = self.cs_search.text().lower()
        mass_min = self.cs_mass_min.value()
        mass_max = self.cs_mass_max.value()
        
        for row in range(self.cs_table.rowCount()):
            should_show = True
            
            # 获取原始数据key
            key = self.cs_table.item(row, 0).data(Qt.ItemDataRole.UserRole)
            if key in self.cs_data:
                compound = self.cs_data[key]
                
                # 文本搜索
                if search_text:
                    name = self.cs_table.item(row, 0).text().lower()
                    formula = self.cs_table.item(row, 1).text().lower()
                    source = self.cs_table.item(row, 3).text().lower()
                    
                    text_match = (search_text in name or 
                                search_text in formula or 
                                search_text in source)
                    if not text_match:
                        should_show = False
                
                # 质量数范围搜索
                if should_show and (mass_min > 0 or mass_max < 1000):
                    mass_number = compound.get('mass_number', 0)
                    try:
                        mass_number_float = float(mass_number) if mass_number else 0
                        if not (mass_min <= mass_number_float <= mass_max):
                            should_show = False
                    except (ValueError, TypeError):
                        # 如果质量数不是有效数字，按质量范围筛选时隐藏
                        if mass_min > 0 or mass_max < 1000:
                            should_show = False
                             
            self.cs_table.setRowHidden(row, not should_show)
            
    def onIECompoundSelected(self, current, previous):
        """当选择电离能化合物时"""
        if current.isValid():
            row = current.row()
            if not self.ie_table.isRowHidden(row):
                original_index = self.ie_table.item(row, 0).data(Qt.ItemDataRole.UserRole)
                if 0 <= original_index < len(self.ie_data):
                    self.displayIECompoundInfo(self.ie_data[original_index], original_index)
                    self.ie_edit_btn.setEnabled(True)
                    self.ie_delete_btn.setEnabled(True)
                    return
                    
        # 清空显示
        self.clearIECompoundInfo()
        self.ie_edit_btn.setEnabled(False)
        self.ie_delete_btn.setEnabled(False)
        
    def clearIECompoundInfo(self):
        """清空电离能化合物详细信息显示"""
        # 清除之前的详细信息布局
        for i in reversed(range(self.ie_detail_layout.count())):
            child = self.ie_detail_layout.takeAt(i)
            if child.widget():
                child.widget().deleteLater()
        
        # 添加默认信息标签
        info_label = QLabel("请选择一个化合物查看详细信息")
        info_label.setWordWrap(True)
        info_label.setAlignment(Qt.AlignmentFlag.AlignTop)
        self.ie_detail_layout.addWidget(info_label)
        
    def onCSCompoundSelected(self, current, previous):
        """当选择光电离截面化合物时"""
        if current.isValid():
            row = current.row()
            if not self.cs_table.isRowHidden(row):
                key = self.cs_table.item(row, 0).data(Qt.ItemDataRole.UserRole)
                if key in self.cs_data:
                    self.displayCSCompoundInfo(self.cs_data[key], key)
                    self.cs_edit_btn.setEnabled(True)
                    self.cs_delete_btn.setEnabled(True)
                    return
                    
        # 清空显示
        self.clearCSCompoundInfo()
        self.cs_edit_btn.setEnabled(False)
        self.cs_delete_btn.setEnabled(False)
        
    def clearCSCompoundInfo(self):
        """清空光电离截面化合物详细信息显示"""
        # 清除之前的详细信息布局
        for i in reversed(range(self.cs_detail_layout.count())):
            child = self.cs_detail_layout.takeAt(i)
            if child.widget():
                child.widget().deleteLater()
        
        # 添加默认信息标签
        info_label = QLabel("请选择一个化合物查看详细信息")
        info_label.setWordWrap(True)
        info_label.setAlignment(Qt.AlignmentFlag.AlignTop)
        self.cs_detail_layout.addWidget(info_label)
        
    def displayIECompoundInfo(self, compound, index):
        """显示电离能化合物详细信息"""
        # 清除之前的详细信息布局
        for i in reversed(range(self.ie_detail_layout.count())):
            child = self.ie_detail_layout.takeAt(i)
            if child.widget():
                child.widget().deleteLater()
        
        info_text = f"<h3>{compound.get('name', 'Unknown')}</h3>"
        info_text += f"<p><b>ID:</b> {compound.get('id', 'N/A')}</p>"
        info_text += f"<p><b>分子式:</b> {compound.get('formula', 'N/A')}</p>"
        info_text += f"<p><b>分子量:</b> {compound.get('mol_weight', 'N/A')}</p>"
        info_text += f"<p><b>SMILES:</b> {compound.get('smiles', 'N/A')}</p>"
        info_text += f"<p><b>CAS号:</b> {compound.get('cas_rn', 'N/A')}</p>"
        info_text += f"<p><b>InChI:</b> {compound.get('inchi', 'N/A')}</p>"
        info_text += f"<p><b>InChI Key:</b> {compound.get('inchi_key', 'N/A')}</p>"
        
        # 创建信息标签
        info_label = QLabel(info_text)
        info_label.setWordWrap(True)
        info_label.setAlignment(Qt.AlignmentFlag.AlignTop)
        self.ie_detail_layout.addWidget(info_label)
        
        # 如果有SMILES，尝试绘制分子结构
        smiles = compound.get('smiles', '')
        if smiles and RDKIT_AVAILABLE:
            pixmap = self.drawMoleculeFromSMILES(smiles)
            if pixmap:
                structure_label = QLabel()
                structure_label.setPixmap(pixmap)
                structure_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
                
                structure_frame = QGroupBox("分子结构")
                structure_layout = QVBoxLayout(structure_frame)
                structure_layout.addWidget(structure_label)
                self.ie_detail_layout.addWidget(structure_frame)
        
        # 同义词
        synonyms = compound.get('synonyms', [])
        if synonyms:
            synonym_text = f"<p><b>同义词:</b></p><ul>"
            for synonym in synonyms[:5]:  # 只显示前5个
                synonym_text += f"<li>{synonym}</li>"
            if len(synonyms) > 5:
                synonym_text += f"<li>... 还有 {len(synonyms) - 5} 个</li>"
            synonym_text += "</ul>"
            
            synonym_label = QLabel(synonym_text)
            synonym_label.setWordWrap(True)
            self.ie_detail_layout.addWidget(synonym_label)
            
        # 电离能数据
        ion_energetics = compound.get('ion_energetics', {})
        if ion_energetics:
            ie_values = ion_energetics.get('ie_values', [])
            methods = ion_energetics.get('methods', [])
            references = ion_energetics.get('references', [])
            comments = ion_energetics.get('comments', [])
            
            if ie_values:
                ie_frame = QGroupBox("电离能数据")
                ie_layout = QVBoxLayout(ie_frame)
                
                ie_table = QTableWidget()
                ie_table.setColumnCount(4)
                ie_table.setHorizontalHeaderLabels(["电离能", "方法", "参考文献", "备注"])
                ie_table.setRowCount(len(ie_values))
                
                for i, ie_val in enumerate(ie_values):
                    ie_table.setItem(i, 0, QTableWidgetItem(str(ie_val)))
                    ie_table.setItem(i, 1, QTableWidgetItem(methods[i] if i < len(methods) else ""))
                    ie_table.setItem(i, 2, QTableWidgetItem(references[i] if i < len(references) else ""))
                    ie_table.setItem(i, 3, QTableWidgetItem(comments[i] if i < len(comments) else ""))
                
                ie_table.resizeColumnsToContents()
                ie_table.setMaximumHeight(200)
                ie_layout.addWidget(ie_table)
                self.ie_detail_layout.addWidget(ie_frame)
                
        self.ie_detail_layout.addStretch()
        self.current_compound = ('ie', index, compound)
        
    def displayCSCompoundInfo(self, compound, key):
        """显示光电离截面化合物详细信息"""
        # 清除之前的详细信息布局
        for i in reversed(range(self.cs_detail_layout.count())):
            child = self.cs_detail_layout.takeAt(i)
            if child.widget():
                child.widget().deleteLater()
        
        info_text = f"<h3>{compound.get('name', 'Unknown')}</h3>"
        info_text += f"<p><b>分子式:</b> {compound.get('formula', 'N/A')}</p>"
        info_text += f"<p><b>质量数:</b> {compound.get('mass_number', 'N/A')}</p>"
        info_text += f"<p><b>电离能:</b> {compound.get('ionization_energy', 'N/A')} eV</p>"
        info_text += f"<p><b>数据来源:</b> {compound.get('source', 'N/A')}</p>"
        
        # 创建信息标签
        info_label = QLabel(info_text)
        info_label.setWordWrap(True)
        info_label.setAlignment(Qt.AlignmentFlag.AlignTop)
        self.cs_detail_layout.addWidget(info_label)
        
        # 光电离截面数据
        energy = compound.get('energy', [])
        cross_section = compound.get('cross_section', [])
        
        if energy and cross_section:
            data_frame = QGroupBox(f"光电离截面数据 ({len(energy)} 个数据点)")
            data_layout = QVBoxLayout(data_frame)
            
            # 数据统计信息
            stats_text = f"<p><b>能量范围:</b> {min(energy):.2f} - {max(energy):.2f} eV</p>"
            stats_text += f"<p><b>截面范围:</b> {min(cross_section):.2f} - {max(cross_section):.2f} Mb</p>"
            stats_label = QLabel(stats_text)
            data_layout.addWidget(stats_label)
            
            # 创建数据表格
            data_table = QTableWidget()
            data_table.setColumnCount(2)
            data_table.setHorizontalHeaderLabels(["能量 (eV)", "截面 (Mb)"])
            data_table.setRowCount(len(energy))
            
            for i, (e, cs) in enumerate(zip(energy, cross_section)):
                data_table.setItem(i, 0, QTableWidgetItem(f"{e:.4f}"))
                data_table.setItem(i, 1, QTableWidgetItem(f"{cs:.4f}"))
            
            data_table.resizeColumnsToContents()
            data_table.setMaximumHeight(300)
            data_table.setSelectionBehavior(QAbstractItemView.SelectionBehavior.SelectRows)
            data_layout.addWidget(data_table)
            
            # 添加复制按钮
            button_layout = QHBoxLayout()
            
            copy_selected_btn = QPushButton("复制选中行")
            copy_selected_btn.clicked.connect(lambda: self.copySelectedCSData(data_table))
            button_layout.addWidget(copy_selected_btn)
            
            copy_all_btn = QPushButton("复制全部数据")
            copy_all_btn.clicked.connect(lambda: self.copyAllCSData(energy, cross_section))
            button_layout.addWidget(copy_all_btn)
            
            export_btn = QPushButton("导出为文本")
            export_btn.clicked.connect(lambda: self.exportCSData(compound, energy, cross_section))
            button_layout.addWidget(export_btn)
            
            button_layout.addStretch()
            data_layout.addLayout(button_layout)
            
            self.cs_detail_layout.addWidget(data_frame)
                
        self.cs_detail_layout.addStretch()
        self.current_compound = ('cs', key, compound)
        
    def copySelectedCSData(self, table):
        """复制选中的光电离截面数据"""
        selected_ranges = table.selectionModel().selectedRows()
        if not selected_ranges:
            QMessageBox.information(self, "提示", "请先选择要复制的行")
            return
            
        clipboard_text = "能量(eV)\t截面(Mb)\n"
        for index in selected_ranges:
            row = index.row()
            energy_item = table.item(row, 0)
            cs_item = table.item(row, 1)
            if energy_item and cs_item:
                clipboard_text += f"{energy_item.text()}\t{cs_item.text()}\n"
        
        clipboard = QApplication.clipboard()
        clipboard.setText(clipboard_text)
        QMessageBox.information(self, "复制成功", f"已复制 {len(selected_ranges)} 行数据到剪贴板")
        
    def copyAllCSData(self, energy, cross_section):
        """复制全部光电离截面数据"""
        clipboard_text = "能量(eV)\t截面(Mb)\n"
        for e, cs in zip(energy, cross_section):
            clipboard_text += f"{e:.4f}\t{cs:.4f}\n"
        
        clipboard = QApplication.clipboard()
        clipboard.setText(clipboard_text)
        QMessageBox.information(self, "复制成功", f"已复制 {len(energy)} 个数据点到剪贴板")
        
    def exportCSData(self, compound, energy, cross_section):
        """导出光电离截面数据为文本文件"""
        try:
            from PyQt6.QtWidgets import QFileDialog
            
            filename, _ = QFileDialog.getSaveFileName(
                self, "导出光电离截面数据", 
                f"{compound.get('name', 'compound')}_photoionization_data.txt",
                "文本文件 (*.txt);;所有文件 (*.*)"
            )
            
            if filename:
                with open(filename, 'w', encoding='utf-8') as f:
                    f.write(f"# 光电离截面数据\n")
                    f.write(f"# 化合物: {compound.get('name', 'Unknown')}\n")
                    f.write(f"# 分子式: {compound.get('formula', 'N/A')}\n")
                    f.write(f"# 质量数: {compound.get('mass_number', 'N/A')}\n")
                    f.write(f"# 电离能: {compound.get('ionization_energy', 'N/A')} eV\n")
                    f.write(f"# 数据来源: {compound.get('source', 'N/A')}\n")
                    f.write(f"# 数据点数: {len(energy)}\n")
                    f.write("#\n")
                    f.write("# 能量(eV)\t截面(Mb)\n")
                    
                    for e, cs in zip(energy, cross_section):
                        f.write(f"{e:.4f}\t{cs:.4f}\n")
                
                QMessageBox.information(self, "导出成功", f"数据已导出到: {filename}")
                
        except Exception as e:
            QMessageBox.critical(self, "导出失败", f"导出数据时出错: {str(e)}")
    
    def resetIEFilter(self):
        """重置电离能筛选条件"""
        self.ie_search.clear()
        self.ie_mass_min.setValue(0)
        self.ie_mass_max.setValue(1000)
        self.filterIEData()
        
    def resetCSFilter(self):
        """重置光电离截面筛选条件"""
        self.cs_search.clear()
        self.cs_mass_min.setValue(0)
        self.cs_mass_max.setValue(1000)
        self.filterCSData()
        
    def drawMoleculeFromSMILES(self, smiles):
        """从SMILES绘制分子结构"""
        if not RDKIT_AVAILABLE or not smiles:
            return None
            
        try:
            mol = Chem.MolFromSmiles(smiles)
            if mol is None:
                return None
                
            # 生成分子图像
            img = Draw.MolToImage(mol, size=(300, 200))
            
            # 转换为Qt可用的格式
            qt_img = ImageQt.ImageQt(img)
            pixmap = QPixmap.fromImage(qt_img)
            
            return pixmap
        except Exception as e:
            print(f"绘制分子结构时出错: {str(e)}")
            return None
        
    def editIECompound(self):
        """编辑电离能化合物"""
        if not self.current_compound or self.current_compound[0] != 'ie':
            return
            
        index = self.current_compound[1]
        compound = self.current_compound[2]
        
        # 创建简单的编辑对话框
        dialog = SimpleEditDialog(compound, 'ie', self)
        
        if dialog.exec() == QDialog.DialogCode.Accepted:
            # 更新数据
            updated_compound = dialog.getCompoundData()
            self.ie_data[index] = updated_compound
            
            # 保存到文件
            self.saveIEData()
            
            # 刷新显示
            self.populateIETable()
            self.displayIECompoundInfo(updated_compound, index)
            
    def editCSCompound(self):
        """编辑光电离截面化合物"""
        if not self.current_compound or self.current_compound[0] != 'cs':
            return
            
        key = self.current_compound[1]
        compound = self.current_compound[2]
        
        # 创建简单的编辑对话框
        dialog = SimpleEditDialog(compound, 'cs', self)
        
        if dialog.exec() == QDialog.DialogCode.Accepted:
            # 更新数据
            updated_compound = dialog.getCompoundData()
            self.cs_data[key] = updated_compound
            
            # 保存到文件
            self.saveCSData()
            
            # 刷新显示
            self.populateCSTable()
            self.displayCSCompoundInfo(updated_compound, key)
            
    def deleteIECompound(self):
        """删除电离能化合物"""
        if not self.current_compound or self.current_compound[0] != 'ie':
            return
            
        index = self.current_compound[1]
        compound = self.current_compound[2]
        
        # 确认删除
        reply = QMessageBox.question(self, "确认删除",
                                   f"您确定要删除化合物 '{compound.get('name', 'Unknown')}' 吗？\n\n"
                                   "此操作不可撤销。",
                                   QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No)
        
        if reply == QMessageBox.StandardButton.Yes:
            # 删除数据
            del self.ie_data[index]
            
            # 保存到文件
            self.saveIEData()
            
            # 刷新显示
            self.populateIETable()
            self.ie_info_label.setText("化合物已删除")
            self.current_compound = None
            self.ie_edit_btn.setEnabled(False)
            self.ie_delete_btn.setEnabled(False)
            
    def deleteCSCompound(self):
        """删除光电离截面化合物"""
        if not self.current_compound or self.current_compound[0] != 'cs':
            return
            
        key = self.current_compound[1]
        compound = self.current_compound[2]
        
        # 确认删除
        reply = QMessageBox.question(self, "确认删除",
                                   f"您确定要删除化合物 '{compound.get('name', 'Unknown')}' 吗？\n\n"
                                   "此操作不可撤销。",
                                   QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No)
        
        if reply == QMessageBox.StandardButton.Yes:
            # 删除数据
            del self.cs_data[key]
            
            # 保存到文件
            self.saveCSData()
            
            # 刷新显示
            self.populateCSTable()
            self.cs_info_label.setText("化合物已删除")
            self.current_compound = None
            self.cs_edit_btn.setEnabled(False)
            self.cs_delete_btn.setEnabled(False)
            
    def saveIEData(self):
        """保存电离能数据到文件"""
        try:
            ie_path = "database/nist_compounds.json"
            with open(ie_path, 'w', encoding='utf-8') as f:
                json.dump(self.ie_data, f, indent=2, ensure_ascii=False)
            print(f"电离能数据已保存到 {ie_path}")
        except Exception as e:
            QMessageBox.critical(self, "保存错误", f"保存电离能数据时出错: {str(e)}")
            
    def saveCSData(self):
        """保存光电离截面数据到文件"""
        try:
            cs_path = "database/compounds_data.json"
            with open(cs_path, 'w', encoding='utf-8') as f:
                json.dump(self.cs_data, f, indent=2, ensure_ascii=False)
            print(f"光电离截面数据已保存到 {cs_path}")
        except Exception as e:
            QMessageBox.critical(self, "保存错误", f"保存光电离截面数据时出错: {str(e)}")


class SimpleEditDialog(QDialog):
    """简单的编辑对话框"""
    
    def __init__(self, compound, compound_type, parent=None):
        super().__init__(parent)
        self.compound = compound.copy()
        self.compound_type = compound_type
        
        self.setWindowTitle("编辑化合物")
        self.setGeometry(200, 200, 600, 500)
        self.setModal(True)
        
        self.initUI()
        
    def initUI(self):
        """初始化用户界面"""
        layout = QVBoxLayout()
        
        # 创建滚动区域
        scroll = QScrollArea()
        scroll_widget = QWidget()
        form_layout = QFormLayout(scroll_widget)
        
        self.fields = {}
        
        if self.compound_type == 'ie':
            # 电离能化合物字段
            self.fields['name'] = QLineEdit(self.compound.get('name', ''))
            form_layout.addRow("名称 *:", self.fields['name'])
            
            self.fields['formula'] = QLineEdit(self.compound.get('formula', ''))
            form_layout.addRow("分子式:", self.fields['formula'])
            
            self.fields['mol_weight'] = QLineEdit(str(self.compound.get('mol_weight', '')))
            self.fields['mol_weight'].setValidator(QDoubleValidator())
            form_layout.addRow("分子量 *:", self.fields['mol_weight'])
            
            self.fields['smiles'] = QLineEdit(self.compound.get('smiles', ''))
            form_layout.addRow("SMILES *:", self.fields['smiles'])
            
            self.fields['cas_rn'] = QLineEdit(self.compound.get('cas_rn', ''))
            form_layout.addRow("CAS号:", self.fields['cas_rn'])
            
        else:  # cs
            # 光电离截面化合物字段
            self.fields['name'] = QLineEdit(self.compound.get('name', ''))
            form_layout.addRow("名称 *:", self.fields['name'])
            
            self.fields['formula'] = QLineEdit(self.compound.get('formula', ''))
            form_layout.addRow("分子式:", self.fields['formula'])
            
            self.fields['mass_number'] = QLineEdit(str(self.compound.get('mass_number', '')))
            self.fields['mass_number'].setValidator(QDoubleValidator())
            form_layout.addRow("质量数 *:", self.fields['mass_number'])
            
            self.fields['source'] = QLineEdit(self.compound.get('source', ''))
            form_layout.addRow("数据来源 *:", self.fields['source'])
            
            self.fields['ionization_energy'] = QLineEdit(str(self.compound.get('ionization_energy', '')))
            self.fields['ionization_energy'].setValidator(QDoubleValidator())
            form_layout.addRow("电离能 (eV):", self.fields['ionization_energy'])
            
        scroll.setWidget(scroll_widget)
        scroll.setWidgetResizable(True)
        layout.addWidget(scroll)
        
        # 按钮布局
        button_layout = QHBoxLayout()
        
        save_btn = QPushButton("保存")
        save_btn.clicked.connect(self.accept)
        button_layout.addWidget(save_btn)
        
        cancel_btn = QPushButton("取消")
        cancel_btn.clicked.connect(self.reject)
        button_layout.addWidget(cancel_btn)
        
        layout.addLayout(button_layout)
        
        self.setLayout(layout)
        
    def getCompoundData(self):
        """获取编辑后的化合物数据"""
        updated_compound = self.compound.copy()
        
        for field_name, field_widget in self.fields.items():
            value = field_widget.text().strip()
            
            if field_name in ['mol_weight', 'mass_number', 'ionization_energy']:
                if value:
                    try:
                        updated_compound[field_name] = float(value)
                    except ValueError:
                        pass  # 保持原值
                else:
                    if field_name in updated_compound:
                        del updated_compound[field_name]
            else:
                if value:
                    updated_compound[field_name] = value
                else:
                    if field_name in updated_compound:
                        del updated_compound[field_name]
                        
        return updated_compound
 