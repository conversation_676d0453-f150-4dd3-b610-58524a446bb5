import sys
import io
sys.stdout = io.TextIOWrapper(sys.stdout.buffer, encoding='utf-8')
import json
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from scipy.optimize import nnls
from scipy.interpolate import interp1d
import re
import argparse
import os
import copy

def load_compounds_data(json_file):
    """加载化合物数据库"""
    with open(json_file, 'r', encoding='utf-8') as f:
        data = json.load(f)
    return data

def load_target_pie(excel_file, sheet_name=0):
    """加载目标光电离曲线数据"""
    df = pd.read_excel(excel_file, sheet_name=sheet_name)
    # 假设第一列是能量，第二列是光电离数据
    energy = df.iloc[:, 0].values
    pie_data = df.iloc[:, 1].values
    return energy, pie_data

def filter_compounds_by_mass(compounds_data, target_mass, tolerance=0):
    """根据质量数筛选化合物
    - 排除Total和Fragment，但如果某source下仅有Total则保留
    """
    filtered_compounds = {}
    # 先按source分组，再按name分组
    source_groups = {}
    for key, compound in compounds_data.items():
        try:
            mass = float(compound['mass_number'])
            if abs(mass - target_mass) > tolerance:
                continue
            source = compound.get('source', '未知来源')
            if source not in source_groups:
                source_groups[source] = []
            source_groups[source].append((key, compound))
        except (ValueError, TypeError):
            continue

    print(f"\n开始筛选质量数为 {target_mass} 的化合物（容差：±{tolerance}）...")
    print("\n找到的候选化合物：")
    print("-" * 100)
    print(f"{'化合物名称':<20} {'分子式':<15} {'质量数':<10} {'数据点数':<10} {'来源'}")
    print("-" * 100)

    for source, items in source_groups.items():
        # 统计非Total/Fragment条目
        non_total = [(k, c) for k, c in items if 'Total' not in c['name'] and 'fragment' not in c['name']]
        total = [(k, c) for k, c in items if 'Total' in c['name'] and 'fragment' not in c['name']]
        # 只保留非Total/Fragment，如果没有则保留Total
        if non_total:
            for key, compound in non_total:
                filtered_compounds[key] = compound
                print(f"{compound['name']:<20} {compound['formula']:<15} {compound['mass_number']:<10} {len(compound['energy']):<10} {compound.get('source', '未知来源')}")
        elif total:
            for key, compound in total:
                filtered_compounds[key] = compound
                print(f"{compound['name']:<20} {compound['formula']:<15} {compound['mass_number']:<10} {len(compound['energy']):<10} {compound.get('source', '未知来源')}")

    print(f"\n共筛选出 {len(filtered_compounds)} 个候选（包含相同化合物的不同来源）")
    print("=" * 100)
    return filtered_compounds

def interpolate_pie_data(compounds, target_energy, window_size=3):
    """将不同化合物的能量步长统一到目标能量轴上
    
    Args:
        compounds: 化合物数据字典
        target_energy: 目标能量轴
        window_size: 插值窗口大小，必须为奇数
    """
    if window_size % 2 == 0:
        window_size = window_size + 1  # 确保为奇数
    
    interpolated_data = {}
    half_window = window_size // 2
    
    for key, compound in compounds.items():
        energy = np.array(compound['energy'])
        pie = np.array(compound['cross_section'])
        source = compound.get('source', '未知来源')
        
        # 确保能量范围和目标能量轴有重叠
        valid_min = max(energy.min(), target_energy.min())
        valid_max = min(energy.max(), target_energy.max())
        
        if valid_min >= valid_max:
            print(f"化合物 {compound['name']} 的能量范围与目标不匹配，跳过")
            continue
        
        # 创建混合插值函数
        interpolated_pie = np.zeros_like(target_energy)
        
        # 对每个目标能量点进行插值
        for i, e in enumerate(target_energy):
            # 找到最近的点
            idx = np.searchsorted(energy, e)
            
            if idx == 0:  # 目标能量小于所有已知能量
                interpolated_pie[i] = 0
            elif idx == len(energy):  # 目标能量大于所有已知能量
                interpolated_pie[i] = pie[-1]
            else:
                # 如果目标能量正好等于某个已知能量点
                if e == energy[idx-1]:
                    interpolated_pie[i] = pie[idx-1]
                else:
                    # 获取临近点
                    start_idx = max(0, idx - half_window)
                    end_idx = min(len(energy), idx + half_window + 1)
                    
                    nearby_energy = energy[start_idx:end_idx]
                    nearby_pie = pie[start_idx:end_idx]
                    
                    # 检查前后点的截面值
                    prev_pie = pie[idx-1]
                    next_pie = pie[idx]
                    
                    # 如果前后点一个为0一个不为0，使用阶跃
                    if (prev_pie == 0 and next_pie > 0) or (prev_pie > 0 and next_pie == 0):
                        interpolated_pie[i] = prev_pie
                    else:
                        # 如果都不为0，使用线性插值
                        if prev_pie > 0 and next_pie > 0:
                            ratio = (e - energy[idx-1]) / (energy[idx] - energy[idx-1])
                            interpolated_pie[i] = prev_pie + ratio * (next_pie - prev_pie)
                        else:
                            # 如果都为0，保持为0
                            interpolated_pie[i] = 0
        
        # 确保插值结果非负
        interpolated_pie = np.maximum(0, interpolated_pie)

        # 存储插值后的数据
        interpolated_data[key] = {
            'name': compound['name'],
            'formula': compound['formula'],
            'mass_number': compound['mass_number'],
            'interpolated_pie': interpolated_pie,
            'source': source
        }
    
    return interpolated_data

def create_weights(energy, weight_ranges=None):
    """创建权重数组
    
    Args:
        energy: 能量数组
        weight_ranges: 权重范围列表，每个元素为(start_energy, end_energy, weight)的元组
                      如果为None，使用默认权重：
                      - [最小能量, 10.5] 权重为3
                      - [10.5, 11.5] 权重为2
                      - [11.5, 最大能量] 权重为1
    
    Returns:
        权重数组
    """
    weights = np.ones_like(energy)  # 初始化权重为1
    
    if weight_ranges is None:
        # 使用默认权重设置
        min_energy = np.min(energy)
        max_energy = np.max(energy)
        default_ranges = [
            (min_energy, 10.5, 2),  # 最小能量到10.5eV，权重为1
            (10.5, 11.5, 2),        # 10.5-11.5eV，权重为2
            (11.5, max_energy, 1)    # 11.5eV到最大能量，权重为1
        ]
        weight_ranges = default_ranges
    
    # 应用权重
    for start_e, end_e, weight in weight_ranges:
        mask = (energy >= start_e) & (energy <= end_e)
        weights[mask] = weight
    
    # 打印权重设置信息
    print("\n权重设置：")
    print("-" * 60)
    for start_e, end_e, weight in weight_ranges:
        print(f"能量范围 [{start_e:.1f}, {end_e:.1f}] eV: 权重 = {weight}")
    print("-" * 60)
    
    return weights

def calculate_fit_metrics(target_pie, fitted_pie, energy):
    """计算拟合质量的多个评价指标
    
    Args:
        target_pie: 目标光电离曲线数据
        fitted_pie: 拟合曲线数据
        energy: 能量数据
    
    Returns:
        metrics: 包含多个评价指标的字典
    """
    # 计算有效数据点（target_pie > 1e-10）的掩码
    mask = target_pie > 1e-10
    valid_target = target_pie[mask]
    valid_fitted = fitted_pie[mask]
    valid_energy = energy[mask]
    
    # 1. 相对误差
    rel_error = np.abs((valid_fitted - valid_target) / valid_target)
    mean_rel_error = np.mean(rel_error)
    
    # 2. 均方根误差 (RMSE)
    rmse = np.sqrt(np.mean((valid_fitted - valid_target) ** 2))
    
    # 3. 决定系数 (R²)
    ss_tot = np.sum((valid_target - np.mean(valid_target)) ** 2)
    ss_res = np.sum((valid_target - valid_fitted) ** 2)
    r_squared = 1 - (ss_res / ss_tot)
    
    # 4. 峰值位置误差
    target_peak_idx = np.argmax(valid_target)
    fitted_peak_idx = np.argmax(valid_fitted)
    peak_position_error = abs(valid_energy[target_peak_idx] - valid_energy[fitted_peak_idx])
    
    # 5. 阈值能量误差
    # 定义阈值为最大值的1%
    threshold = 0.01 * np.max(valid_target)
    target_threshold_idx = np.where(valid_target > threshold)[0][0]
    fitted_threshold_idx = np.where(valid_fitted > threshold)[0][0]
    threshold_energy_error = abs(valid_energy[target_threshold_idx] - valid_energy[fitted_threshold_idx])
    
    return {
        'relative_error': mean_rel_error,
        'rmse': rmse,
        'r_squared': r_squared,
        'peak_position_error': peak_position_error,
        'threshold_energy_error': threshold_energy_error
    }

def calculate_score(solution):
    """计算拟合方案的综合评分
    
    权重分配依据：
    1. R² (50%): 最重要，反映整体趋势的拟合效果
    2. 峰值位置误差 (25%): 关键特征点的准确性
    3. 阈值能量误差 (15%): 起始点的准确性
    4. RMSE (10%): 用于评估整体拟合的平滑度
    
    注：不再使用相对误差，因为我们更关注曲线趋势的拟合而不是点对点的精确匹配
    """
    metrics = solution[2]
    score = (
        0.70 * metrics['r_squared'] +           # R²越大越好，反映整体趋势
        -0.10 * metrics['peak_position_error'] + # 峰值位置误差越小越好
        -0.10 * metrics['threshold_energy_error'] + # 阈值能量误差越小越好
        -0.10 * metrics['rmse']                 # RMSE越小越好，反映平滑度
    )
    return score

def find_best_combinations(interpolated_data, target_pie, target_energy, max_compounds=5, 
                         rel_threshold=0.2, max_solutions=3, weight_ranges=None):
    """寻找最优的化合物组合"""
    def get_canonical_name(name):
        # 去除_Total和_fragment等后缀，保留本体名
        name = re.sub(r'_Total$', '', name)
        name = re.sub(r'_fragment.*$', '', name)
        return name

    keys = list(interpolated_data.keys())
    n = len(keys)
    
    if n == 0:
        print("没有找到符合条件的化合物")
        return []
    
    # 准备数据矩阵
    A = np.zeros((len(target_pie), n))
    for i, key in enumerate(keys):
        A[:, i] = interpolated_data[key]['interpolated_pie']
    
    # 确保所有数据都是有限值
    A = np.nan_to_num(A, nan=0.0)
    target_pie = np.nan_to_num(target_pie, nan=0.0)
    
    # 创建权重数组
    weights = create_weights(target_energy, weight_ranges)
    
    # 应用权重到数据矩阵和目标数据
    A_weighted = A * weights[:, np.newaxis]
    target_pie_weighted = target_pie * weights
    
    # 尝试不同的组合
    solutions = []
    
    print("\n尝试使用所有可能的化合物组合...")
    
    # 首先尝试使用所有数据进行拟合
    coefficients, residuals = nnls(A_weighted, target_pie_weighted)
    fitted_pie = np.dot(A, coefficients)
    metrics = calculate_fit_metrics(target_pie, fitted_pie, target_energy)
    
    # 构建解决方案，同时记录每个归一名的所有来源
    compound_sources = {}  # 用于存储每个归一名的所有来源
    for i, key in enumerate(keys):
        if coefficients[i] > 1e-6:  # 只考虑系数显著的化合物
            name = interpolated_data[key]['name']
            canonical_name = get_canonical_name(name)
            if canonical_name not in compound_sources:
                compound_sources[canonical_name] = []
            compound_sources[canonical_name].append({
                'key': key,
                'name': name,
                'coefficient': coefficients[i],
                'source': interpolated_data[key].get('source', '未知来源')
            })
    
    # 构建最优解（每个归一名只选一个来源，选系数最大的）
    best_solution = []
    for canonical_name, sources in compound_sources.items():
        best_source = max(sources, key=lambda x: x['coefficient'])
        key = best_source['key']
        best_solution.append({
            'key': key,
            'name': best_source['name'],
            'formula': interpolated_data[key]['formula'],
            'mass': interpolated_data[key]['mass_number'],
            'coefficient': best_source['coefficient'],
            'source': best_source['source']
        })
    
    # 按系数大小排序
    best_solution.sort(key=lambda x: x['coefficient'], reverse=True)
    
    # 限制最多使用的化合物数，并重新拟合
    if len(best_solution) > max_compounds:
        best_solution = best_solution[:max_compounds]
    # 用当前best_solution重新拟合
    selected_keys = [s['key'] for s in best_solution]
    selected_indices = [keys.index(key) for key in selected_keys]
    A_selected = A[:, selected_indices]
    A_selected_weighted = A_weighted[:, selected_indices]
    coefficients_sel, residuals_sel = nnls(A_selected_weighted, target_pie_weighted)
    fitted_pie_sel = np.dot(A_selected, coefficients_sel)
    metrics_sel = calculate_fit_metrics(target_pie, fitted_pie_sel, target_energy)
    # 更新solution中的系数
    for i, coef in enumerate(coefficients_sel):
        best_solution[i]['coefficient'] = coef
    # 只返回 solution, fitted_pie, metrics
    solutions.append((best_solution, fitted_pie_sel, metrics_sel))
    
    print("\n寻找替代方案，考虑不同来源...")
    # 对于每个归一名，尝试使用其他来源
    for canonical_name, sources in compound_sources.items():
        if len(sources) > 1:  # 只处理有多个来源的化合物
            print(f"\n尝试 {canonical_name} 的不同来源:")
            for source_info in sources: # Renamed source to source_info to avoid conflict
                # 创建一个新的候选方案，替换当前来源
                current_solution_alt = copy.deepcopy(best_solution) # 深拷贝，确保独立
                # 找到并替换当前化合物的条目（按归一名）
                for i_alt, comp_alt in enumerate(current_solution_alt):
                    if get_canonical_name(comp_alt['name']) == canonical_name:
                        current_solution_alt[i_alt] = {
                            'key': source_info['key'],
                            'name': source_info['name'],
                            'formula': interpolated_data[source_info['key']]['formula'],
                            'mass': interpolated_data[source_info['key']]['mass_number'],
                            'coefficient': source_info['coefficient'], # Use coefficient from this source_info
                            'source': source_info['source']
                        }
                # 用当前current_solution_alt重新拟合
                selected_keys_alt = [s['key'] for s in current_solution_alt]
                selected_indices_alt = [keys.index(key) for key in selected_keys_alt]
                A_selected_alt = A[:, selected_indices_alt]
                A_selected_weighted_alt = A_weighted[:, selected_indices_alt] # Corrected variable name
                coefficients_sel_alt, residuals_sel_alt = nnls(A_selected_weighted_alt, target_pie_weighted)
                fitted_pie_sel_alt = np.dot(A_selected_alt, coefficients_sel_alt)
                metrics_sel_alt = calculate_fit_metrics(target_pie, fitted_pie_sel_alt, target_energy)
                # 更新current_solution_alt中的系数为重新拟合后的系数
                for i_alt, coef_alt in enumerate(coefficients_sel_alt):
                    current_solution_alt[i_alt]['coefficient'] = coef_alt
                
                # 检查是否是更好的方案
                base_metrics = solutions[0][2] # Assuming solutions[0] is the current best overall
                is_good_alternative = (
                    metrics_sel_alt['r_squared'] > base_metrics['r_squared'] * 0.95 and
                    metrics_sel_alt['peak_position_error'] < 0.3 and
                    metrics_sel_alt['threshold_energy_error'] < 0.3 and
                    metrics_sel_alt['rmse'] < base_metrics['rmse'] * 1.2
                )
                if is_good_alternative:
                    print(f"找到更好的方案：使用 {canonical_name} 的来源 {source_info['source']}")
                    # 只返回 solution, fitted_pie, metrics
                    solutions.append((current_solution_alt, fitted_pie_sel_alt, metrics_sel_alt))
    # 按评分排序
    solutions.sort(key=calculate_score, reverse=True)
    
    # 记录所有尝试过的组合（包括拟合效果差的）
    all_solutions = []
    # 最优解
    all_solutions.append((best_solution.copy(), fitted_pie_sel, metrics_sel))
    # 替代解
    for canonical_name, sources in compound_sources.items():
        if len(sources) > 1:
            for source_info in sources:
                current_solution_alt = copy.deepcopy(best_solution)
                for i_alt, comp_alt in enumerate(current_solution_alt):
                    if get_canonical_name(comp_alt['name']) == canonical_name:
                        current_solution_alt[i_alt] = {
                            'key': source_info['key'],
                            'name': source_info['name'],
                            'formula': interpolated_data[source_info['key']]['formula'],
                            'mass': interpolated_data[source_info['key']]['mass_number'],
                            'coefficient': source_info['coefficient'],
                            'source': source_info['source']
                        }
                selected_keys_alt = [s['key'] for s in current_solution_alt]
                selected_indices_alt = [keys.index(key) for key in selected_keys_alt]
                A_selected_alt = A[:, selected_indices_alt]
                A_selected_weighted_alt = A_weighted[:, selected_indices_alt]
                coefficients_sel_alt, residuals_sel_alt = nnls(A_selected_weighted_alt, target_pie_weighted)
                fitted_pie_sel_alt = np.dot(A_selected_alt, coefficients_sel_alt)
                metrics_sel_alt = calculate_fit_metrics(target_pie, fitted_pie_sel_alt, target_energy)
                for i_alt, coef_alt in enumerate(coefficients_sel_alt):
                    current_solution_alt[i_alt]['coefficient'] = coef_alt
                all_solutions.append((copy.deepcopy(current_solution_alt), fitted_pie_sel_alt, metrics_sel_alt))
    # 按评分排序
    all_solutions.sort(key=calculate_score, reverse=True)
    # 强制输出max_solutions种组分/来源组合不同的方案
    unique_solutions = []
    def solution_signature(solution_tuple):
        solution_dict = solution_tuple[0]
        return tuple(sorted((comp['key'], comp['source']) for comp in solution_dict))
    seen_signatures = set()
    for sol in all_solutions:
        sig = solution_signature(sol)
        if sig not in seen_signatures:
            unique_solutions.append(sol)
            seen_signatures.add(sig)
        if len(unique_solutions) >= max_solutions:
            break
    return unique_solutions

def plot_results(energy, target_pie, solutions, interpolated_data, output_file=None):
    """绘制拟合结果图"""
    # 设置中文字体支持
    plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans', 'Arial']
    plt.rcParams['axes.unicode_minus'] = False
    
    for i, solution_tuple in enumerate(solutions):
        # solution_dict, fitted_pie_original, metrics_original = solution_tuple # Unpack, keep original names for clarity
        solution_dict, _, metrics_original = solution_tuple # Unpack, ignore the potentially stale fitted_pie

        plt.figure(figsize=(14, 10))
        gs = plt.GridSpec(3, 1, height_ratios=[3, 1, 0.5])
        ax1 = plt.subplot(gs[0])
        ax1.plot(energy, target_pie, 'o', color='black', markersize=6, alpha=0.7, label='目标曲线')
        
        # Recalculate sum_components based on the final coefficients in solution_dict
        sum_components = np.zeros_like(energy) # Use energy for shape, fitted_pie might be stale
        print("\n【DEBUG】计算sum_components时的系数和数据(从solution_dict)：") 
        for comp_for_sum in solution_dict:
            key_for_sum = comp_for_sum['key']
            coef_for_sum = comp_for_sum['coefficient']
            if 'Ethenol' in key_for_sum or 'C2H3OH' in key_for_sum: 
                print(f"  sum_components: Ethenol key={key_for_sum}, coef={coef_for_sum:.4f}")
            sum_components += interpolated_data[key_for_sum]['interpolated_pie'] * coef_for_sum
        
        # IMPORTANT: Use sum_components as the definitive fitted curve from now on
        fitted_pie_definitive = sum_components

        # Recalculate metrics using the definitive fitted curve
        metrics_definitive = calculate_fit_metrics(target_pie, fitted_pie_definitive, energy)

        # Debug output comparing the original passed fitted_pie and the recalculated one
        # print(f"方案{i+1}：原始fitted_pie与sum_components(即definitive)是否一致：", np.allclose(fitted_pie_original, fitted_pie_definitive))
        # print(f"  原始 fitted_pie: min={fitted_pie_original.min():.6f}, max={fitted_pie_original.max():.6f}, mean={fitted_pie_original.mean():.6f}")
        print(f"  计算得到的 sum_components (definitive): min={fitted_pie_definitive.min():.6f}, max={fitted_pie_definitive.max():.6f}, mean={fitted_pie_definitive.mean():.6f}")
        # diff_original = np.abs(fitted_pie_original - fitted_pie_definitive)
        # print(f"  原始与计算差值: max={diff_original.max():.6e}, mean={diff_original.mean():.6e}, rmse={np.sqrt(np.mean(diff_original**2)):.6e}")
        
        # Plot sum_components (Orange dashed line, for debug)
        ax1.plot(energy, sum_components, '--', color='orange', linewidth=2, label='组分加和(调试)')
        
        # Plot the definitive fitted curve (Red solid line) using recalculated metrics
        ax1.plot(energy, fitted_pie_definitive, '-', color='red', linewidth=3,
                 label=f'总拟合曲线\\n' + 
                       f'相对误差: {metrics_definitive["relative_error"]:.2%}\\n' +
                       f'R²: {metrics_definitive["r_squared"]:.4f}\\n' +
                       f'RMSE: {metrics_definitive["rmse"]:.4f}\\n' +
                       f'峰值误差: {metrics_definitive["peak_position_error"]:.2f}eV\\n' +
                       f'阈值误差: {metrics_definitive["threshold_energy_error"]:.2f}eV')
        
        # components_data calculation remains the same, using coefficients from solution_dict
        colors = plt.cm.tab10(np.linspace(0, 1, len(solution_dict))) # Define colors before the loop
        components_data = []
        print("\n【DEBUG】计算components_data时的系数和数据：") 
        for j, comp_in_solution in enumerate(solution_dict):
            if comp_in_solution['coefficient'] > 1e-6:
                key_for_comp = comp_in_solution['key']
                coef_for_comp = comp_in_solution['coefficient']
                if 'Ethenol' in key_for_comp or 'C2H3OH' in key_for_comp:
                    print(f"  components_data: Ethenol key={key_for_comp}, coef={coef_for_comp:.4f}")

                formula = comp_in_solution['formula']
                name = comp_in_solution['name']
                source = interpolated_data[key_for_comp]['source']
                
                component_pie = interpolated_data[key_for_comp]['interpolated_pie'] * coef_for_comp
                # Use definitive fitted pie for contribution calculation
                contribution = np.sum(component_pie) / np.sum(fitted_pie_definitive) * 100 
                components_data.append((formula, name, component_pie, colors[j], coef_for_comp, contribution, source))
        
        # Output all components' formula and name, for debug
        print("\n【DEBUG】所有分量formula和name：")
        for formula, name, _, _, _, _, _ in components_data:
            print(f"formula={formula}, name={name}")
        # Plot individual components
        for formula, name, component_pie, color, coef, contribution, source in components_data:
             ax1.plot(energy, component_pie, '--', color=color, linewidth=2,
                     label=f'{formula} ({name})\\n系数={coef:.2f}, 贡献={contribution:.1f}%\\n来源: {source}')
             # debug: 针对C2H3OH输出详细数值
             if 'C2H3OH' in formula or 'Ethenol' in name:
                 print(f"\n【DEBUG】C2H3OH分量曲线与加和曲线数值对比：")
                 for idx in range(len(energy)):
                     print(f"energy={energy[idx]:.2f}, C2H3OH={component_pie[idx]:.2f}, sum={sum_components[idx]:.2f}, fitted={fitted_pie_definitive[idx]:.2f}")
             # debug: 针对Propane输出9.23-11.06区间的数值（更宽松的判断）
             if 'C3H8' in formula or 'Propane' in formula or 'C3H8' in name or 'Propane' in name:
                 print(f"\n【DEBUG】Propane分量曲线9.23-11.06eV区间：")
                 for idx in range(len(energy)):
                     if 9.23 <= energy[idx] <= 11.06:
                         print(f"energy={energy[idx]:.2f}, Propane={component_pie[idx]:.2f}")
        
        ax1.set_xlabel('能量 (eV)', fontsize=14)
        ax1.set_ylabel('光电离截面 (Å²)', fontsize=14)
        ax1.set_title(f'光电离曲线拟合结果 - 方案{i+1}', fontsize=16)
        ax1.grid(True, linestyle='--', alpha=0.7)
        ax1.legend(fontsize=12, loc='best')

        # Relative error plot (use definitive fitted curve)
        ax2 = plt.subplot(gs[1], sharex=ax1)
        mask = target_pie > 1e-10
        rel_error = np.zeros_like(target_pie)
        rel_error[mask] = (fitted_pie_definitive[mask] - target_pie[mask]) / target_pie[mask] * 100
        ax2.axhline(y=0, color='k', linestyle='-', alpha=0.3)
        ax2.axhline(y=10, color='r', linestyle='--', alpha=0.3)
        ax2.axhline(y=-10, color='r', linestyle='--', alpha=0.3)
        ax2.plot(energy, rel_error, '-o', color='blue', markersize=4)
        ax2.set_ylabel('相对误差 (%)', fontsize=12)
        ax2.set_ylim(-50, 50) # Limit error display range
        ax2.grid(True, linestyle='--', alpha=0.7)

        # Component distribution pie chart (contribution calculated using definitive fitted curve)
        ax3 = plt.subplot(gs[2])
        components_data.sort(key=lambda x: x[5], reverse=True)
        labels = [f"{item[0]}\\n({item[5]:.1f}%)" for item in components_data] # Corrected index for contribution
        sizes = [item[5] for item in components_data] # Corrected index for contribution
        explode = [0.1 if j == 0 else 0 for j in range(len(components_data))]
        pie_colors = [item[3] for item in components_data] # Corrected index for color
        ax3.pie(sizes, explode=explode, labels=labels, colors=pie_colors,
                autopct='%1.1f%%', shadow=True, startangle=90)
        ax3.axis('equal')
        ax3.set_title('组分贡献分布', fontsize=14)

        plt.tight_layout()
        
        # Save image
        if output_file:
            base, ext = os.path.splitext(output_file)
            this_output = f"{base}_solution{i+1}{ext}"
            plt.savefig(this_output, dpi=300, bbox_inches='tight')
            print(f"方案{i+1}的结果图已保存至: {this_output}")
    
    plt.show()

def analyze_pie_curve(json_file, target_file, target_mass, tolerance=0, max_compounds=5, 
                   max_solutions=3, rel_threshold=0.2, output_file=None, 
                   window_size=3, weight_ranges=None):
    """主函数：分析光电离曲线"""
    print(f"正在加载化合物数据库: {json_file}")
    compounds_data = load_compounds_data(json_file)
    print(f"共加载 {len(compounds_data)} 个化合物数据")
    
    print(f"正在加载目标光电离曲线: {target_file}")
    target_energy, target_pie = load_target_pie(target_file)
    print(f"目标曲线包含 {len(target_energy)} 个数据点")
    
    print(f"根据质量数 {target_mass} 筛选化合物 (容差 ±{tolerance})")
    filtered_compounds = filter_compounds_by_mass(compounds_data, target_mass, tolerance)
    print(f"找到 {len(filtered_compounds)} 个候选化合物")
    
    if not filtered_compounds:
        print("没有找到符合条件的化合物，请调整质量数或增加容差")
        return
    
    print(f"统一能量步长（使用{window_size}点插值窗口）...")
    interpolated_data = interpolate_pie_data(filtered_compounds, target_energy, window_size)
    print(f"成功插值处理 {len(interpolated_data)} 个化合物数据")
    
    print(f"寻找最优组合（权重范围: {weight_ranges})...")
    solutions = find_best_combinations(interpolated_data, target_pie, target_energy, 
                                    max_compounds, rel_threshold, max_solutions, weight_ranges)
    
    if not solutions:
        print("未找到满意的拟合方案")
        return
    
    print("\n最优拟合方案:")
    for i, solution_tuple in enumerate(solutions): # New
        solution_dict, _, metrics = solution_tuple # Unpack
        print(f"\n方案 {i+1}:")
        print("-" * 60)
        print(f"相对误差: {metrics['relative_error']:.2%}")
        print(f"RMSE: {metrics['rmse']:.4f}") # Corrected f-string
        print(f"R²: {metrics['r_squared']:.4f}")
        print(f"峰值位置误差: {metrics['peak_position_error']:.2f} eV")
        print(f"阈值能量误差: {metrics['threshold_energy_error']:.2f} eV")
        print("\n组分:")
        for s_comp in solution_dict: # Iterate through solution_dict
            print(f"  - {s_comp['formula']} ({s_comp['name']}): 系数 = {s_comp['coefficient']:.4f}")
            print(f"    来源: {s_comp['source']}")
    
    # 绘制结果
    if output_file is None:
        output_file = f"pie_analysis_mass{target_mass}.png"
    plot_results(target_energy, target_pie, solutions, interpolated_data, output_file)
    print(f"\n结果图已保存至: {output_file}")

if __name__ == "__main__":
    # 创建命令行参数解析器
    parser = argparse.ArgumentParser(description='光电离曲线分析工具')
    
    parser.add_argument('--json', type=str, default='compounds_data.json',
                        help='化合物数据库JSON文件路径')
    parser.add_argument('--target', type=str, default='PIE_44.xlsx',
                        help='目标光电离曲线Excel文件路径')
    parser.add_argument('--mass', type=float, default=44,
                        help='目标化合物质量数')
    parser.add_argument('--tolerance', type=float, default=0,
                        help='质量数搜索容差')
    parser.add_argument('--max-compounds', type=int, default=5,
                        help='每个方案最多使用的化合物数')
    parser.add_argument('--max-solutions', type=int, default=3,
                        help='最多输出的方案数')
    parser.add_argument('--threshold', type=float, default=0.2,
                        help='备选方案的相对误差容差（比例）')
    parser.add_argument('--output', type=str, default=None,
                        help='结果图像输出文件路径')
    parser.add_argument('--window-size', type=int, default=3,
                        help='插值窗口大小（奇数）')
    parser.add_argument('--weight-ranges', type=str, default=None,
                        help='权重范围列表，格式为start_energy,end_energy,weight;多个范围用逗号分隔')
    
    args = parser.parse_args()
    
    # 解析权重范围
    if args.weight_ranges:
        weight_ranges = []
        for range_str in args.weight_ranges.split(','):
            start_e, end_e, weight = map(float, range_str.split(','))
            weight_ranges.append((start_e, end_e, weight))
    else:
        weight_ranges = None
    
    # 运行分析
    analyze_pie_curve(
        json_file=args.json,
        target_file=args.target,
        target_mass=args.mass,
        tolerance=args.tolerance,
        max_compounds=args.max_compounds,
        max_solutions=args.max_solutions,
        rel_threshold=args.threshold,
        output_file=args.output,
        window_size=args.window_size,
        weight_ranges=weight_ranges
    ) 