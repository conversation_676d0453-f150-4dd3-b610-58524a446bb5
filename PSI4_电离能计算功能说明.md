# PSI4电离能计算功能说明

## 概述
化合物查询对话框已成功集成PSI4量子化学计算功能，用户可以直接在界面中进行绝热电离能计算。

## 界面结构
- **标签页1**: 化合物信息（原有NIST数据库查询功能）
- **标签页2**: PSI4电离能计算（新增功能）

## 新增功能详解

### 1. 智能SMILES分析
- **分子量计算**: 自动计算精确分子量
- **重原子数统计**: 显示非氢原子数量
- **质量数比较**: 与主窗口当前查询质量数比较
  - 差异 > 2 Da 时显示橙色警告
  - 差异 ≤ 2 Da 时显示绿色确认

### 2. 计算耗时预警
- **重原子数检查**: 重原子数 > 6 时显示红色警告
- **耗时提醒**: "重原子数较多，计算可能非常耗时！"

### 3. 参数配置
- **SMILES输入**: 支持手动输入或从选中化合物自动填充
- **CPU线程数**: 1-32线程可选（默认4）
- **内存设置**: 1GB-16GB可选（默认2GB）
- **计算方法**:
  - 低精度方法: b3lyp, pbe0, m06, wb97xd, hf
  - 低精度基组: 6-31g(d), 6-31g(d,p), 6-31+g(d), 6-31+g(d,p), def2-svp
  - 高精度方法: m062x, wb97xd, b3lyp, pbe0, ccsd(t)
  - 高精度基组: aug-cc-pvtz, aug-cc-pvdz, cc-pvtz, cc-pvdz, def2-tzvp
- **构象设置**: 最大构象数1-100（默认10）
- **快速模式**: 跳过低精度优化，直接使用力场最优构象
- **缓存目录**: 可自定义PSI4临时文件存储位置

### 4. 实时计算监控
- **进度显示**: 显示当前计算阶段
- **PSI4输出**: 实时同步显示PSI4控制台输出
- **停止功能**: 可随时终止计算

### 5. 结果管理
- **详细结果**: 显示分子能量、电离能（Hartree/eV/kcal/mol）
- **复制结果**: 一键复制计算结果到剪贴板
- **添加到图表**: 将电离能值发送到主窗口图表显示
- **保存到数据库**: 自动保存到NIST电离能数据库

### 6. 数据库自动保存
计算成功后可自动保存到`database/nist_compounds.json`：
- **SMILES**: 原始输入
- **分子式**: 自动从SMILES计算
- **分子量**: 精确分子量
- **电离能**: 计算结果（eV）
- **方法**: PSI4方法/基组信息
- **来源**: "P.W"
- **备注**: 包含计算日期的方法描述

## 使用流程

### 基本使用
1. 在主窗口点击"化合物查询"
2. 切换到"PSI4电离能计算"标签页
3. 输入SMILES或从化合物列表填充
4. 查看分子信息和耗时警告
5. 调整计算参数（可选）
6. 点击"开始计算"
7. 监控计算进度和PSI4输出
8. 查看结果并选择保存方式

### 高级使用
- **大分子计算**: 重原子数>6时建议启用"跳过低精度优化"
- **高精度计算**: 选择CCSD(T)方法进行基准计算
- **批量计算**: 可通过缓存目录避免重复文件创建
- **结果验证**: 与NIST实验值对比验证计算精度

## 注意事项

### 系统要求
- 必须安装PSI4和RDKit
- 推荐4GB以上内存
- 多核CPU可显著提升计算速度

### 计算耗时
- 小分子(≤6重原子): 几分钟到几十分钟
- 中等分子(7-10重原子): 数小时
- 大分子(>10重原子): 可能需要数天

### 精度说明
- 低精度优化：用于快速构象筛选
- 高精度计算：产生最终电离能结果
- 推荐组合：M06-2X/aug-cc-pVTZ（精度与效率的平衡）

## 错误处理
- **SMILES无效**: 自动检测并提示
- **内存不足**: 建议减少线程数或增加内存设置
- **计算收敛失败**: 尝试更换计算方法或基组
- **意外中断**: 可通过"停止计算"按钮安全终止

## 技术特性
- **多线程计算**: 支持并行化计算
- **内存管理**: 自动管理PSI4内存分配
- **输出重定向**: 实时捕获PSI4输出到GUI
- **异常处理**: 完善的错误捕获和用户提示
- **数据验证**: 自动验证SMILES有效性和参数合理性

此功能将理论计算与实验数据库查询完美结合，为质谱分析提供强大的电离能预测工具。 