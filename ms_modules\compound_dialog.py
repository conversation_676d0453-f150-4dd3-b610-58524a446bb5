"""
化合物信息对话框
用于显示符合分子式的化合物列表和电离能信息，并提供PSI4电离能计算功能
"""

import os
import threading
import json
import uuid
import time
from datetime import datetime
from PyQt6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QLabel, QListWidget, QListWidgetItem,
    QTableWidget, QTableWidgetItem, QPushButton, QSplitter, QHeaderView,
    QMessageBox, QGroupBox, QMenu, QApplication, QTabWidget, QWidget,
    QLineEdit, QSpinBox, QComboBox, QCheckBox, QProgressBar, QTextEdit,
    QScrollArea, QFormLayout, QFileDialog
)
from PyQt6.QtCore import Qt, pyqtSignal, QThread, pyqtSlot
from PyQt6.QtGui import QFont, QClipboard

try:
    from .nist_database import NISTDatabase
except ImportError:
    from nist_database import NISTDatabase

# 导入电离能计算模块
try:
    try:
        from .adiabatic_ionization_energy import calculate_ionization_energy
    except ImportError:
        from adiabatic_ionization_energy import calculate_ionization_energy
    PSI4_AVAILABLE = True
except ImportError as e:
    print(f"PSI4电离能计算模块不可用: {e}")
    PSI4_AVAILABLE = False

# 导入RDKit用于分子量和重原子数计算
try:
    from rdkit import Chem
    from rdkit.Chem import rdMolDescriptors
    RDKIT_AVAILABLE = True
except ImportError:
    print("RDKit不可用，分子量和重原子数计算功能将被禁用")
    RDKIT_AVAILABLE = False

# 定义类级别信号，用于将电离能值发送到主窗口
from PyQt6.QtCore import QObject

class CompoundDialogSignals(QObject):
    ionization_energy_selected = pyqtSignal(float, str)

# 创建信号实例
compound_dialog_signals = CompoundDialogSignals()


class IonizationEnergyCalculationThread(QThread):
    """电离能计算线程"""
    
    # 定义信号
    calculation_started = pyqtSignal()
    calculation_finished = pyqtSignal(dict)
    calculation_error = pyqtSignal(str)
    progress_updated = pyqtSignal(str)
    output_updated = pyqtSignal(str)  # PSI4输出信号
    
    def __init__(self, params):
        super().__init__()
        self.params = params
        self.stop_monitoring = False
        self.last_file_size = 0
        
    def run(self):
        """执行计算"""
        try:
            self.calculation_started.emit()
            
            # 更新进度
            self.progress_updated.emit("开始电离能计算...")
            
            if not PSI4_AVAILABLE:
                raise ImportError("PSI4模块不可用，请确保已正确安装PSI4和RDKit")
            
            import os
            
            # 设置PSI4缓存目录
            if 'scratch_dir' not in self.params or not self.params['scratch_dir']:
                # 默认使用程序目录下的psi4_scratch目录
                default_scratch_dir = os.path.join(os.getcwd(), "psi4_scratch")
                os.makedirs(default_scratch_dir, exist_ok=True)
                self.params['scratch_dir'] = default_scratch_dir
                print(f"使用默认PSI4缓存目录: {default_scratch_dir}")
            else:
                # 确保用户指定的目录存在
                os.makedirs(self.params['scratch_dir'], exist_ok=True)
                print(f"使用指定PSI4缓存目录: {self.params['scratch_dir']}")
            
            # 创建PSI4输出文件（在缓存目录中）
            psi4_output_file = os.path.join(self.params['scratch_dir'], "psi4_calculation.out")
            
            # 启动文件监控线程
            monitor_thread = threading.Thread(
                target=self.monitor_output_file,
                args=(psi4_output_file,),
                daemon=True
            )
            monitor_thread.start()

            # 发送初始监控状态
            self.progress_updated.emit("开始监控PSI4输出...")
            
            # 执行计算
            results = self.run_calculation_with_output_capture(self.params, psi4_output_file)
            
            # 停止监控
            self.stop_monitoring = True
            
            # 发送结果
            self.calculation_finished.emit(results)
            
        except Exception as e:
            self.stop_monitoring = True
            self.calculation_error.emit(str(e))
    
    def monitor_output_file(self, file_path):
        """监控PSI4输出文件的变化并实时发送更新"""
        try:
            # 初始化文件大小
            self.last_file_size = 0

            # 如果文件已存在，从当前大小开始（避免读取旧内容）
            if os.path.exists(file_path):
                self.last_file_size = os.path.getsize(file_path)

            # 缓冲区用于处理不完整的行
            line_buffer = ""

            while not getattr(self, 'stop_monitoring', False):
                try:
                    if os.path.exists(file_path):
                        current_size = os.path.getsize(file_path)
                        if current_size > self.last_file_size:
                            # 读取新增内容
                            with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                                f.seek(self.last_file_size)
                                new_content = f.read()

                                if new_content:
                                    # 将新内容添加到缓冲区
                                    line_buffer += new_content

                                    # 处理完整的行
                                    lines = line_buffer.split('\n')
                                    # 保留最后一行（可能不完整）
                                    line_buffer = lines[-1]
                                    complete_lines = lines[:-1]

                                    if complete_lines:
                                        complete_content = '\n'.join(complete_lines)

                                        # 过滤PSI4输出信息
                                        filtered_content = self.filter_psi4_output(complete_content)
                                        if filtered_content:
                                            self.output_updated.emit(filtered_content)

                                self.last_file_size = current_size

                        # 如果文件大小没有变化，检查是否有缓冲的内容需要处理
                        elif line_buffer.strip():
                            # 处理缓冲区中的内容（可能是最后一行）
                            filtered_content = self.filter_psi4_output(line_buffer)
                            if filtered_content:
                                self.output_updated.emit(filtered_content)
                            line_buffer = ""

                except Exception as e:
                    # 忽略读取错误，继续监控
                    pass

                time.sleep(0.2)  # 进一步缩短检查间隔，提高实时性

        except Exception as e:
            print(f"监控线程异常: {e}")
        finally:
            # 清理缓冲区
            if hasattr(self, 'line_buffer'):
                del self.line_buffer
    
    def filter_psi4_output(self, content):
        """过滤PSI4输出，保留重要的计算进度和结果信息"""
        lines = content.split('\n')
        filtered_lines = []

        # 重要关键词列表
        important_keywords = [
            'optimization', 'optimizing', 'converged', 'convergence',
            'step', 'energy', 'hartree', 'scf', 'iteration', 'iter',
            'gradient', 'geometry', 'complete', 'completed', 'finished',
            'starting', 'beginning', 'initializing', 'final',
            'warning', 'error', 'failed', 'success', 'successful',
            'conformer', 'conformers', 'generated', 'optimized',
            'neutral', 'cation', 'ionization', 'ie', 'ev',
            'calculation', 'computing', 'processing'
        ]

        # 需要跳过的模式
        skip_patterns = [
            '***', '==>',  # PSI4装饰性输出
            'scratch directory', 'temporary directory',  # 目录信息
            'psi4 exiting successfully',  # 退出信息
            'reading options from',  # 配置读取
            'nuclear repulsion energy',  # 核排斥能（太详细）
            'basis set information',  # 基组信息（太详细）
            'molecular point group',  # 分子点群（太详细）
        ]

        for line in lines:
            line_lower = line.lower().strip()

            # 跳过空行
            if not line.strip():
                continue

            # 跳过特定模式
            should_skip = False
            for pattern in skip_patterns:
                if pattern in line_lower:
                    should_skip = True
                    break

            if should_skip:
                continue

            # 保留包含重要关键词的行
            contains_important = False
            for keyword in important_keywords:
                if keyword in line_lower:
                    contains_important = True
                    break

            # 保留重要信息或简短的状态行
            if contains_important or len(line.strip()) < 80:
                # 清理行格式
                cleaned_line = line.strip()
                if cleaned_line and not cleaned_line.startswith('*'):
                    filtered_lines.append(cleaned_line)

        return '\n'.join(filtered_lines) if filtered_lines else ''
    
    def run_calculation_with_output_capture(self, params, output_file):
        """运行计算并设置输出文件"""
        import psi4
        import sys

        from datetime import datetime

        # 发送初始化信息到输出
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write("=== PSI4绝热电离能计算开始 ===\n")
            f.write(f"时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"SMILES: {params['smiles']}\n")
            f.write(f"方法: {params['high_level_method']}/{params['high_level_basis']}\n")
            f.write(f"线程数: {params['nthreads']}\n")
            f.write(f"内存: {params['memory']}\n")
            f.write(f"缓存目录: {params['scratch_dir']}\n")
            f.write("="*50 + "\n\n")
            f.flush()

        # 设置PSI4输出文件（文件监控线程会实时读取这个文件）
        try:
            # 设置PSI4的主输出文件
            psi4.core.set_output_file(output_file, False)

            # 同时重定向Python的stdout和stderr到文件（捕获更多输出）
            original_stdout = sys.stdout
            original_stderr = sys.stderr

            # 创建一个自定义的输出流，同时写入文件和保持原始输出
            class TeeOutput:
                def __init__(self, file_path, original_stream):
                    self.file_path = file_path
                    self.original_stream = original_stream

                def write(self, text):
                    # 写入原始流
                    if self.original_stream:
                        self.original_stream.write(text)
                    # 写入文件
                    try:
                        with open(self.file_path, 'a', encoding='utf-8') as f:
                            f.write(text)
                            f.flush()
                    except:
                        pass  # 忽略写入错误

                def flush(self):
                    if self.original_stream:
                        self.original_stream.flush()

            # 重定向输出
            tee_stdout = TeeOutput(output_file, original_stdout)
            tee_stderr = TeeOutput(output_file, original_stderr)

            sys.stdout = tee_stdout
            sys.stderr = tee_stderr

            # 添加进度信息
            print("开始PSI4计算...")

            # 调用计算函数
            results = calculate_ionization_energy(**params)

        finally:
            # 恢复原始输出流
            sys.stdout = original_stdout
            sys.stderr = original_stderr

        # 在输出文件末尾添加完成信息
        with open(output_file, 'a', encoding='utf-8') as f:
            f.write("\n" + "="*50 + "\n")
            f.write("=== PSI4计算完成 ===\n")
            if results['success']:
                f.write(f"绝热电离能: {results['ionization_energy_ev']:.3f} eV\n")
                f.write(f"中性分子能量: {results['neutral_energy']:.8f} Hartree\n")
                f.write(f"阳离子能量: {results['cation_energy']:.8f} Hartree\n")
            else:
                f.write(f"计算失败: {results.get('error_message', '未知错误')}\n")
            f.write(f"完成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.flush()

        return results

class CompoundDialog(QDialog):
    """化合物信息对话框"""

    def __init__(self, formula, parent=None, main_window_mass=None):
        """初始化对话框

        参数:
            formula: 分子式
            parent: 父窗口
            main_window_mass: 主窗口当前查询的质量数
        """
        super().__init__(parent)
        self.formula = formula
        self.main_window_mass = main_window_mass
        self.db = NISTDatabase()
        self.compounds = []

        # 计算线程相关
        self.calculation_thread = None

        self.setWindowTitle(f"化合物信息与电离能计算 - {formula}")
        self.setGeometry(100, 100, 1200, 800)
        self.setMinimumSize(1000, 600)

        self.init_ui()
        self.load_compounds()

    def init_ui(self):
        """初始化界面"""
        main_layout = QVBoxLayout(self)

        # 创建水平分割器
        main_splitter = QSplitter(Qt.Orientation.Horizontal)
        main_splitter.setChildrenCollapsible(False)

        # 左侧：化合物列表
        left_widget = self.create_compound_list_widget()
        main_splitter.addWidget(left_widget)

        # 右侧：标签页控件
        self.tab_widget = QTabWidget()
        
        # 标签页1：化合物信息
        energetics_widget = self.create_energetics_widget()
        self.tab_widget.addTab(energetics_widget, "化合物信息")
        
        # 标签页2：PSI4电离能计算
        calculation_widget = self.create_calculation_widget()
        self.tab_widget.addTab(calculation_widget, "PSI4电离能计算")

        main_splitter.addWidget(self.tab_widget)
        
        # 设置分割器比例 (左侧:右侧 = 1:2)
        main_splitter.setSizes([400, 800])

        main_layout.addWidget(main_splitter)

        # 底部按钮
        button_layout = QHBoxLayout()

        # 添加复制按钮
        self.copy_button = QPushButton("复制化合物信息")
        self.copy_button.clicked.connect(self.copy_selected_compound)
        button_layout.addWidget(self.copy_button)

        button_layout.addStretch(1)

        self.close_button = QPushButton("关闭")
        self.close_button.clicked.connect(self.accept)
        button_layout.addWidget(self.close_button)

        main_layout.addLayout(button_layout)
        
    def create_compound_list_widget(self):
        """创建化合物列表小部件"""
        widget = QWidget()
        layout = QVBoxLayout(widget)

        # 化合物列表
        compounds_group = QGroupBox("符合分子式的化合物")
        compounds_layout = QVBoxLayout(compounds_group)

        self.compounds_list = QListWidget()
        self.compounds_list.setAlternatingRowColors(True)
        self.compounds_list.currentItemChanged.connect(self.on_compound_selected)
        compounds_layout.addWidget(self.compounds_list)

        layout.addWidget(compounds_group)
        return widget
    
    def create_energetics_widget(self):
        """创建电离能信息小部件"""
        widget = QWidget()
        layout = QVBoxLayout(widget)

        # 电离能信息
        energetics_group = QGroupBox("NIST电离能信息")
        energetics_layout = QVBoxLayout(energetics_group)

        self.energetics_table = QTableWidget(0, 4)
        self.energetics_table.setHorizontalHeaderLabels(["电离能 (eV)", "方法", "来源", "备注"])
        self.energetics_table.horizontalHeader().setSectionResizeMode(QHeaderView.ResizeMode.Stretch)
        self.energetics_table.setAlternatingRowColors(True)
        self.energetics_table.setEditTriggers(QTableWidget.EditTrigger.NoEditTriggers)

        # 添加右键菜单
        self.energetics_table.setContextMenuPolicy(Qt.ContextMenuPolicy.CustomContextMenu)
        self.energetics_table.customContextMenuRequested.connect(self.show_energetics_context_menu)

        # 添加单击事件
        self.energetics_table.cellClicked.connect(self.on_energetics_cell_clicked)

        # 添加双击事件
        self.energetics_table.cellDoubleClicked.connect(self.on_energetics_cell_double_clicked)
        energetics_layout.addWidget(self.energetics_table)

        layout.addWidget(energetics_group)
        return widget
        
    def create_calculation_widget(self):
        """创建电离能计算小部件"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # 计算参数组
        params_group = QGroupBox("PSI4电离能计算参数")
        params_layout = QFormLayout(params_group)
        
        # SMILES输入
        self.smiles_input = QLineEdit()
        self.smiles_input.setPlaceholderText("输入分子的SMILES字符串，例如: CCO")
        self.smiles_input.textChanged.connect(self.on_smiles_changed)
        params_layout.addRow("SMILES:", self.smiles_input)
        
        # 填充选中化合物SMILES按钮
        smiles_button_layout = QHBoxLayout()
        self.fill_smiles_button = QPushButton("从选中化合物填充SMILES")
        self.fill_smiles_button.clicked.connect(self.fill_smiles_from_compound)
        smiles_button_layout.addWidget(self.fill_smiles_button)
        smiles_button_layout.addStretch()
        params_layout.addRow("", smiles_button_layout)
        
        # 分子信息显示
        self.mol_info_label = QLabel("")
        self.mol_info_label.setStyleSheet("color: blue; font-weight: bold;")
        self.mol_info_label.setWordWrap(True)
        params_layout.addRow("分子信息:", self.mol_info_label)
        
        # 线程数
        self.nthreads_input = QSpinBox()
        self.nthreads_input.setMinimum(1)
        self.nthreads_input.setMaximum(32)
        self.nthreads_input.setValue(10)
        params_layout.addRow("CPU线程数:", self.nthreads_input)

        # 内存设置
        self.memory_input = QComboBox()
        self.memory_input.addItems(["1GB", "2GB", "4GB", "8GB", "16GB"])
        self.memory_input.setCurrentText("10GB")
        self.memory_input.setEditable(True)
        params_layout.addRow("内存大小:", self.memory_input)
        
        # 构象搜索方法
        self.low_method_input = QComboBox()
        self.low_method_input.addItems(["b3lyp", "pbe0", "m062x", "wb97x-d", "hf"])
        self.low_method_input.setCurrentText("b3lyp")
        self.low_method_input.setEditable(True)
        params_layout.addRow("构象搜索方法:", self.low_method_input)
        
        # 构象搜索基组
        self.low_basis_input = QComboBox()
        self.low_basis_input.addItems(["6-31g(d)", "6-31g(d,p)", "6-31+g(d)", "6-31+g(d,p)", "def2-svp"])
        self.low_basis_input.setCurrentText("6-31g(d)")
        self.low_basis_input.setEditable(True)
        params_layout.addRow("构象搜索基组:", self.low_basis_input)
        
        # 电离能计算方法
        self.high_method_input = QComboBox()
        self.high_method_input.addItems(["m062x", "wb97x-d", "b3lyp", "pbe0", "ccsd(t)"])
        self.high_method_input.setCurrentText("m062x")
        self.high_method_input.setEditable(True)
        params_layout.addRow("电离能计算方法:", self.high_method_input)
        
        # 电离能计算基组
        self.high_basis_input = QComboBox()
        self.high_basis_input.addItems(["aug-cc-pvtz", "aug-cc-pvdz", "cc-pvtz", "cc-pvdz", "def2-tzvp"])
        self.high_basis_input.setCurrentText("aug-cc-pvtz")
        self.high_basis_input.setEditable(True)
        params_layout.addRow("电离能计算基组:", self.high_basis_input)

        # 最大构象数
        self.max_conformers_input = QSpinBox()
        self.max_conformers_input.setMinimum(1)
        self.max_conformers_input.setMaximum(100)
        self.max_conformers_input.setValue(10)
        params_layout.addRow("最大构象数:", self.max_conformers_input)
        
        # 跳过构象搜索优化
        self.skip_optimization_checkbox = QCheckBox("跳过构象搜索优化（快速模式）")
        params_layout.addRow("", self.skip_optimization_checkbox)
        
        # 缓存目录
        cache_layout = QHBoxLayout()
        self.scratch_dir_input = QLineEdit()
        self.scratch_dir_input.setPlaceholderText("留空使用默认目录")
        cache_layout.addWidget(self.scratch_dir_input)
        
        self.browse_button = QPushButton("浏览")
        self.browse_button.clicked.connect(self.browse_scratch_dir)
        cache_layout.addWidget(self.browse_button)
        
        params_layout.addRow("缓存目录:", cache_layout)
        
        layout.addWidget(params_group)
        
        # 计算控制按钮
        control_layout = QHBoxLayout()
        
        self.calculate_button = QPushButton("开始计算")
        self.calculate_button.clicked.connect(self.start_calculation)
        control_layout.addWidget(self.calculate_button)
        
        self.stop_button = QPushButton("停止计算")
        self.stop_button.clicked.connect(self.stop_calculation)
        self.stop_button.setEnabled(False)
        control_layout.addWidget(self.stop_button)
        
        control_layout.addStretch()
        layout.addLayout(control_layout)
        
        # 进度条
        self.progress_label = QLabel("就绪")
        layout.addWidget(self.progress_label)
        
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        layout.addWidget(self.progress_bar)
        
        # PSI4输出显示
        output_group = QGroupBox("PSI4计算输出")
        output_layout = QVBoxLayout(output_group)
        
        self.output_text = QTextEdit()
        self.output_text.setMaximumHeight(150)
        self.output_text.setReadOnly(True)
        self.output_text.setStyleSheet("background-color: #f0f0f0; font-family: monospace;")
        output_layout.addWidget(self.output_text)
        
        layout.addWidget(output_group)
        
        # 结果显示
        results_group = QGroupBox("计算结果")
        results_layout = QVBoxLayout(results_group)
        
        self.results_text = QTextEdit()
        self.results_text.setMaximumHeight(200)
        self.results_text.setReadOnly(True)
        results_layout.addWidget(self.results_text)
        
        # 结果操作按钮
        results_button_layout = QHBoxLayout()
        
        self.copy_results_button = QPushButton("复制结果")
        self.copy_results_button.clicked.connect(self.copy_calculation_results)
        self.copy_results_button.setEnabled(False)
        results_button_layout.addWidget(self.copy_results_button)
        
        self.add_to_plot_button = QPushButton("添加到图表")
        self.add_to_plot_button.clicked.connect(self.add_result_to_plot)
        self.add_to_plot_button.setEnabled(False)
        results_button_layout.addWidget(self.add_to_plot_button)
        
        self.save_to_db_button = QPushButton("保存到数据库")
        self.save_to_db_button.clicked.connect(self.save_result_to_database)
        self.save_to_db_button.setEnabled(False)
        results_button_layout.addWidget(self.save_to_db_button)
        
        results_button_layout.addStretch()
        results_layout.addLayout(results_button_layout)
        
        layout.addWidget(results_group)
        
        # PSI4可用性提示
        if not PSI4_AVAILABLE:
            warning_label = QLabel("⚠️ PSI4模块不可用，请安装PSI4和RDKit")
            warning_label.setStyleSheet("color: red; font-weight: bold;")
            layout.addWidget(warning_label)
            self.calculate_button.setEnabled(False)
        
        return widget

    def load_compounds(self):
        """加载符合分子式的化合物"""
        try:
            self.compounds = self.db.search_by_formula(self.formula)

            if not self.compounds:
                QMessageBox.information(self, "提示", f"未找到符合分子式 {self.formula} 的化合物")
                return

            # 更新窗口标题
            self.setWindowTitle(f"化合物信息 - {self.formula} (找到 {len(self.compounds)} 个结果)")

            # 清空列表
            self.compounds_list.clear()

            # 添加化合物
            for compound in self.compounds:
                name = compound.get('name', '')
                formula = compound.get('formula', '')
                cas_rn = compound.get('cas_rn', '')

                # 创建显示文本
                display_text = f"{name} ({formula})"
                if cas_rn:
                    display_text += f" [CAS: {cas_rn}]"

                # 创建列表项
                item = QListWidgetItem(display_text)
                item.setData(Qt.ItemDataRole.UserRole, compound.get('id', ''))
                self.compounds_list.addItem(item)

            # 选择第一个化合物
            if self.compounds_list.count() > 0:
                self.compounds_list.setCurrentRow(0)

        except Exception as e:
            QMessageBox.critical(self, "错误", f"加载化合物信息时出错: {str(e)}")
            import traceback
            traceback.print_exc()

    def on_compound_selected(self, current, previous):
        """当选择化合物时更新电离能信息

        参数:
            current: 当前选中的项
            previous: 之前选中的项
        """
        if not current:
            return

        # 获取化合物ID
        compound_id = current.data(Qt.ItemDataRole.UserRole)

        # 查找化合物
        compound = None
        for c in self.compounds:
            if c.get('id', '') == compound_id:
                compound = c
                break

        if not compound:
            return

        # 更新电离能信息
        self.update_energetics(compound)

    def update_energetics(self, compound):
        """更新电离能信息

        参数:
            compound: 化合物信息字典
        """
        # 清空表格
        self.energetics_table.setRowCount(0)

        # 获取电离能信息
        ion_energetics = compound.get('ion_energetics', None)
        if not ion_energetics:
            return

        # 获取各列数据
        ie_values = ion_energetics.get('ie_values', [])
        methods = ion_energetics.get('methods', [])
        references = ion_energetics.get('references', [])
        comments = ion_energetics.get('comments', [])

        # 确定行数
        max_rows = max(
            len(ie_values) if ie_values else 0,
            len(methods) if methods else 0,
            len(references) if references else 0,
            len(comments) if comments else 0
        )

        if max_rows == 0:
            return

        # 设置行数
        self.energetics_table.setRowCount(max_rows)

        # 填充数据
        for row in range(max_rows):
            # 电离能
            if ie_values and row < len(ie_values):
                value = ie_values[row]
                if value is not None:
                    self.energetics_table.setItem(row, 0, QTableWidgetItem(str(value)))

            # 方法
            if methods and row < len(methods):
                value = methods[row]
                if value is not None:
                    self.energetics_table.setItem(row, 1, QTableWidgetItem(str(value)))

            # 来源
            if references and row < len(references):
                value = references[row]
                if value is not None:
                    self.energetics_table.setItem(row, 2, QTableWidgetItem(str(value)))

            # 备注
            if comments and row < len(comments):
                value = comments[row]
                if value is not None:
                    self.energetics_table.setItem(row, 3, QTableWidgetItem(str(value)))

    def on_energetics_cell_double_clicked(self, row, column):
        """当用户双击电离能表格时触发

        参数:
            row: 行索引
            column: 列索引
        """
        print(f"双击电离能表格: 行={row}, 列={column}")

        # 只处理电离能列
        if column != 0:
            return

        # 获取电离能值
        ie_item = self.energetics_table.item(row, column)
        if not ie_item:
            print(f"无法获取电离能值项: 行={row}, 列={column}")
            return

        ie_text = ie_item.text()
        if not ie_text:
            print(f"电离能值为空: 行={row}, 列={column}")
            return

        try:
            print(f"处理电离能值: {ie_text}")
            # 处理电离能值，可能包含不确定度（如"8.05±0.17"）
            ie_value = self.parse_ionization_energy(ie_text)

            if ie_value is None:
                print(f"无法解析电离能值: {ie_text}")
                return

            print(f"解析后的电离能值: {ie_value}")

            # 获取化合物名称
            compound_name = ""
            current_item = self.compounds_list.currentItem()
            if current_item:
                compound_name = current_item.text()
                print(f"当前选中的化合物: {compound_name}")

            # 直接绘制电离能值
            if ie_value is not None:
                print(f"发送电离能值进行绘制: {ie_value} eV, 化合物: {compound_name}")
                compound_dialog_signals.ionization_energy_selected.emit(ie_value, compound_name)
                # 显示成功消息
                QMessageBox.information(self, "成功", f"已将电离能值 {ie_value:.2f} eV 发送到结果图表")
        except Exception as e:
            print(f"处理电离能值时出错: {str(e)}")
            import traceback
            traceback.print_exc()

    def on_energetics_cell_clicked(self, row, column):
        """当用户点击电离能表格时触发

        参数:
            row: 行索引
            column: 列索引
        """
        # 只处理电离能列的点击
        if column != 0:
            return

        # 获取电离能值
        ie_item = self.energetics_table.item(row, column)
        if not ie_item:
            return

        ie_text = ie_item.text()
        if not ie_text:
            return

        try:
            # 处理电离能值，可能包含不确定度（如"8.05±0.17"）
            ie_value = self.parse_ionization_energy(ie_text)

            # 获取化合物名称
            compound_name = ""
            current_item = self.compounds_list.currentItem()
            if current_item:
                compound_name = current_item.text()

            # 发送信号
            if ie_value is not None:
                compound_dialog_signals.ionization_energy_selected.emit(ie_value, compound_name)
                print(f"选中电离能值: {ie_value} eV, 化合物: {compound_name}")
        except Exception as e:
            print(f"处理电离能值时出错: {str(e)}")

    def show_energetics_context_menu(self, pos):
        """显示电离能表格的右键菜单

        参数:
            pos: 鼠标位置
        """
        # 直接获取点击位置的行和列
        row = self.energetics_table.rowAt(pos.y())
        column = self.energetics_table.columnAt(pos.x())

        # 打印调试信息
        print(f"右键点击位置: ({pos.x()}, {pos.y()}), 行: {row}, 列: {column}")

        # 检查行和列是否有效
        if row < 0 or column < 0:
            print(f"无效的行或列: {row}, {column}")
            return

        # 只处理电离能列
        if column != 0:
            print(f"非电离能列: {column}")
            return

        # 获取电离能值
        ie_item = self.energetics_table.item(row, column)
        if not ie_item or not ie_item.text():
            print(f"无法获取电离能值或为空: 行={row}, 列={column}")
            return

        print(f"获取到电离能值: {ie_item.text()}")

        # 创建菜单
        menu = QMenu(self)

        # 解析电离能值
        ie_text = ie_item.text()
        ie_value = self.parse_ionization_energy(ie_text)

        if ie_value is not None:
            # 获取化合物名称
            compound_name = ""
            current_item = self.compounds_list.currentItem()
            if current_item:
                compound_name = current_item.text()

            # 添加绘制电离能菜单项
            plot_action = menu.addAction(f"在结果图上绘制电离能值: {ie_value:.2f} eV")
            plot_action.triggered.connect(lambda: self.on_energetics_cell_double_clicked(row, column))

            # 添加复制电离能值菜单项
            copy_action = menu.addAction(f"复制电离能值: {ie_value:.2f}")
            copy_action.triggered.connect(lambda: QApplication.clipboard().setText(f"{ie_value:.6f}"))

            # 显示菜单
            global_pos = self.energetics_table.viewport().mapToGlobal(pos)
            print(f"将显示菜单在全局位置: {global_pos.x()}, {global_pos.y()}")
            menu.exec(global_pos)

    def plot_ionization_energy(self, ie_value, compound_name):
        """将电离能值发送到主窗口进行绘制

        参数:
            ie_value: 电离能值
            compound_name: 化合物名称
        """
        try:
            # 发送信号
            print(f"发送电离能值进行绘制: {ie_value:.2f} eV, 化合物: {compound_name}")
            compound_dialog_signals.ionization_energy_selected.emit(ie_value, compound_name)
            # 显示成功消息
            QMessageBox.information(self, "成功", f"已将电离能值 {ie_value:.2f} eV 发送到结果图表")
        except Exception as e:
            print(f"发送电离能值时出错: {str(e)}")
            import traceback
            traceback.print_exc()

    def parse_ionization_energy(self, ie_text):
        """解析电离能值，处理可能的不确定度

        参数:
            ie_text: 电离能文本，如"8.05"或"8.05±0.17"

        返回:
            浮点数电离能值，如果无法解析则返回None
        """
        try:
            # 如果包含“±”符号，取前面的值
            if "±" in ie_text:
                ie_value = float(ie_text.split("±")[0])
                return ie_value

            # 如果包含“≤”或“≥”符号，取后面的值
            if "≤" in ie_text:
                ie_value = float(ie_text.replace("≤", "").strip())
                return ie_value
            if "≥" in ie_text:
                ie_value = float(ie_text.replace("≥", "").strip())
                return ie_value

            # 如果包含范围，取平均值
            if "-" in ie_text and ie_text.count(".") > 1:
                parts = ie_text.split("-")
                if len(parts) == 2:
                    try:
                        min_val = float(parts[0].strip())
                        max_val = float(parts[1].strip())
                        return (min_val + max_val) / 2
                    except ValueError:
                        pass

            # 直接转换为浮点数
            return float(ie_text)
        except ValueError:
            # 如果无法转换为浮点数，返回None
            return None

    def copy_selected_compound(self):
        """复制选定的化合物信息"""
        # 获取当前选中的化合物项
        current_item = self.compounds_list.currentItem()
        if not current_item:
            QMessageBox.warning(self, "警告", "请先选择一个化合物")
            return

        # 获取化合物ID
        compound_id = current_item.data(Qt.ItemDataRole.UserRole)

        # 查找化合物详细信息
        compound = None
        for c in self.compounds:
            if c.get('id', '') == compound_id:
                compound = c
                break

        if not compound:
            QMessageBox.warning(self, "警告", "无法获取化合物信息")
            return

        # 提取化合物信息
        name = compound.get('name', '')
        formula = compound.get('formula', '')
        cas_rn = compound.get('cas_rn', '')
        mol_weight = compound.get('mol_weight', '')

        # 构建复制文本
        copy_text = f"{name} ({formula})"
        if cas_rn:
            copy_text += f" [CAS: {cas_rn}]"
        if mol_weight:
            copy_text += f" 分子量: {mol_weight}"

        # 获取电离能信息
        ion_energetics = compound.get('ion_energetics', None)
        if ion_energetics:
            ie_values = ion_energetics.get('ie_values', [])
            if ie_values and len(ie_values) > 0 and ie_values[0]:
                copy_text += f" 电离能: {ie_values[0]} eV"

        # 复制到剪贴板
        clipboard = QApplication.clipboard()
        clipboard.setText(copy_text)

        # 显示成功消息
        QMessageBox.information(self, "复制成功", f"已复制化合物信息到剪贴板")
        print(f"已复制化合物信息: {copy_text}")

    # ========== 电离能计算相关方法 ==========
    
    def on_smiles_changed(self):
        """当SMILES输入改变时处理"""
        smiles = self.smiles_input.text().strip()
        if not smiles:
            self.mol_info_label.setText("")
            return
        
        if not RDKIT_AVAILABLE:
            self.mol_info_label.setText("RDKit不可用，无法分析分子信息")
            return
        
        try:
            # 从SMILES创建分子
            mol = Chem.MolFromSmiles(smiles)
            if mol is None:
                self.mol_info_label.setText("❌ 无效的SMILES字符串")
                self.mol_info_label.setStyleSheet("color: red; font-weight: bold;")
                return
            
            # 计算分子量
            mol_weight = rdMolDescriptors.CalcExactMolWt(mol)
            
            # 计算重原子数（非氢原子数）
            heavy_atoms = mol.GetNumHeavyAtoms()
            
            # 构建信息文本
            info_text = f"分子量: {mol_weight:.4f} Da, 重原子数: {heavy_atoms}"
            
            # 检查与主窗口质量数的差异
            if self.main_window_mass is not None:
                mass_diff = abs(mol_weight - self.main_window_mass)
                if mass_diff > 2.0:  # 差异超过2 Da
                    info_text += f"\n⚠️ 与主窗口质量数({self.main_window_mass:.1f})相差{mass_diff:.1f} Da"
                    self.mol_info_label.setStyleSheet("color: orange; font-weight: bold;")
                else:
                    self.mol_info_label.setStyleSheet("color: green; font-weight: bold;")
            else:
                self.mol_info_label.setStyleSheet("color: blue; font-weight: bold;")
            
            # 检查重原子数，给出耗时警告
            if heavy_atoms > 6:
                info_text += f"\n⚠️ 重原子数较多({heavy_atoms})，计算可能非常耗时！"
                self.mol_info_label.setStyleSheet("color: red; font-weight: bold;")
            
            self.mol_info_label.setText(info_text)
            
        except Exception as e:
            self.mol_info_label.setText(f"❌ 分析SMILES时出错: {str(e)}")
            self.mol_info_label.setStyleSheet("color: red; font-weight: bold;")
    
    def fill_smiles_from_compound(self):
        """从选中的化合物填充SMILES"""
        current_item = self.compounds_list.currentItem()
        if not current_item:
            QMessageBox.warning(self, "警告", "请先选择一个化合物")
            return

        # 获取化合物ID
        compound_id = current_item.data(Qt.ItemDataRole.UserRole)

        # 查找化合物详细信息
        compound = None
        for c in self.compounds:
            if c.get('id', '') == compound_id:
                compound = c
                break

        if not compound:
            QMessageBox.warning(self, "警告", "无法获取化合物信息")
            return

        # 获取SMILES
        smiles = compound.get('smiles', '')
        if not smiles:
            QMessageBox.information(self, "提示", "该化合物没有SMILES信息")
            return

        # 填充到输入框
        self.smiles_input.setText(smiles)
        QMessageBox.information(self, "成功", f"已填充SMILES: {smiles}")

    def browse_scratch_dir(self):
        """浏览缓存目录"""
        dir_path = QFileDialog.getExistingDirectory(
            self, "选择PSI4缓存目录", 
            self.scratch_dir_input.text() or os.path.expanduser("~")
        )
        if dir_path:
            self.scratch_dir_input.setText(dir_path)

    def get_calculation_params(self):
        """获取计算参数"""
        smiles = self.smiles_input.text().strip()
        if not smiles:
            raise ValueError("请输入SMILES字符串")

        # 获取缓存目录
        scratch_dir = self.scratch_dir_input.text().strip()
        if not scratch_dir:
            scratch_dir = None

        params = {
            'smiles': smiles,
            'nthreads': self.nthreads_input.value(),
            'memory': self.memory_input.currentText(),
            'low_level_method': self.low_method_input.currentText(),
            'low_level_basis': self.low_basis_input.currentText(),
            'high_level_method': self.high_method_input.currentText(),
            'high_level_basis': self.high_basis_input.currentText(),
            'max_conformers': self.max_conformers_input.value(),
            'skip_low_level_optimization': self.skip_optimization_checkbox.isChecked(),
            'scratch_dir': scratch_dir
        }
        return params

    def start_calculation(self):
        """开始电离能计算"""
        try:
            # 获取计算参数
            params = self.get_calculation_params()
            
            # 创建并启动计算线程
            self.calculation_thread = IonizationEnergyCalculationThread(params)
            
            # 连接信号
            self.calculation_thread.calculation_started.connect(self.on_calculation_started)
            self.calculation_thread.calculation_finished.connect(self.on_calculation_finished)
            self.calculation_thread.calculation_error.connect(self.on_calculation_error)
            self.calculation_thread.progress_updated.connect(self.on_progress_updated)
            self.calculation_thread.output_updated.connect(self.on_output_updated)
            
            # 启动线程
            self.calculation_thread.start()
            
        except Exception as e:
            QMessageBox.critical(self, "参数错误", f"无法开始计算: {str(e)}")

    def stop_calculation(self):
        """停止计算"""
        if self.calculation_thread and self.calculation_thread.isRunning():
            self.calculation_thread.terminate()
            self.calculation_thread.wait()
            self.on_calculation_stopped()

    @pyqtSlot()
    def on_calculation_started(self):
        """计算开始时的处理"""
        self.calculate_button.setEnabled(False)
        self.stop_button.setEnabled(True)
        self.progress_bar.setVisible(True)
        self.progress_bar.setRange(0, 0)  # 不确定进度
        self.progress_label.setText("计算进行中...")
        self.results_text.clear()
        self.output_text.clear()  # 清除PSI4输出
        self.copy_results_button.setEnabled(False)
        self.add_to_plot_button.setEnabled(False)
        self.save_to_db_button.setEnabled(False)

    @pyqtSlot(dict)
    def on_calculation_finished(self, results):
        """计算完成时的处理"""
        self.calculate_button.setEnabled(True)
        self.stop_button.setEnabled(False)
        self.progress_bar.setVisible(False)
        
        if results.get('success', False):
            self.progress_label.setText("计算完成")
            self.display_calculation_results(results)
            self.copy_results_button.setEnabled(True)
            self.add_to_plot_button.setEnabled(True)
            self.save_to_db_button.setEnabled(True)
        else:
            self.progress_label.setText("计算失败")
            error_msg = results.get('error_message', '未知错误')
            self.results_text.setPlainText(f"计算失败: {error_msg}")

    @pyqtSlot(str)
    def on_calculation_error(self, error_message):
        """计算出错时的处理"""
        self.calculate_button.setEnabled(True)
        self.stop_button.setEnabled(False)
        self.progress_bar.setVisible(False)
        self.progress_label.setText("计算出错")
        
        self.results_text.setPlainText(f"计算出错: {error_message}")
        QMessageBox.critical(self, "计算错误", f"计算过程中出现错误:\n{error_message}")

    @pyqtSlot(str)
    def on_progress_updated(self, message):
        """更新进度信息"""
        self.progress_label.setText(message)

    @pyqtSlot(str)
    def on_output_updated(self, output):
        """更新PSI4输出"""
        if output.strip():
            # 添加时间戳（可选）
            from datetime import datetime
            timestamp = datetime.now().strftime("%H:%M:%S")

            # 格式化输出
            formatted_output = output.strip()

            # 为不同类型的信息添加颜色标记（通过HTML）
            if any(keyword in output.lower() for keyword in ['error', 'failed', 'warning']):
                # 错误和警告信息用红色
                formatted_output = f'<span style="color: red;">[{timestamp}] {formatted_output}</span>'
            elif any(keyword in output.lower() for keyword in ['completed', 'converged', 'finished', 'success']):
                # 成功信息用绿色
                formatted_output = f'<span style="color: green;">[{timestamp}] {formatted_output}</span>'
            elif any(keyword in output.lower() for keyword in ['starting', 'beginning', 'initializing']):
                # 开始信息用蓝色
                formatted_output = f'<span style="color: blue;">[{timestamp}] {formatted_output}</span>'
            else:
                # 普通信息用默认颜色
                formatted_output = f'[{timestamp}] {formatted_output}'

            # 使用HTML格式添加内容
            cursor = self.output_text.textCursor()
            cursor.movePosition(cursor.MoveOperation.End)
            cursor.insertHtml(formatted_output + '<br>')

            # 自动滚动到底部
            scrollbar = self.output_text.verticalScrollBar()
            scrollbar.setValue(scrollbar.maximum())

            # 限制输出文本的长度，避免内存占用过多
            if self.output_text.document().blockCount() > 1000:
                # 删除前面的内容，保留最新的1000行
                cursor = self.output_text.textCursor()
                cursor.movePosition(cursor.MoveOperation.Start)
                for _ in range(100):  # 删除前100行
                    cursor.select(cursor.SelectionType.BlockUnderCursor)
                    cursor.removeSelectedText()
                    cursor.deleteChar()  # 删除换行符

    def on_calculation_stopped(self):
        """计算停止时的处理"""
        self.calculate_button.setEnabled(True)
        self.stop_button.setEnabled(False)
        self.progress_bar.setVisible(False)
        self.progress_label.setText("计算已停止")
        self.results_text.setPlainText("计算已被用户停止")

    def display_calculation_results(self, results):
        """显示计算结果"""
        result_text = "PSI4电离能计算结果\n"
        result_text += "="*50 + "\n\n"
        
        result_text += f"SMILES: {results['smiles']}\n"
        result_text += f"构象搜索方法: {results['low_level_method']}\n"
        result_text += f"电离能计算方法: {results['high_level_method']}\n"
        result_text += f"生成构象数: {results['conformers_generated']}\n"
        result_text += f"优化构象数: {results['conformers_optimized']}\n\n"
        
        if results.get('neutral_energy') is not None:
            result_text += f"中性分子能量: {results['neutral_energy']:.8f} Hartree\n"
        
        if results.get('cation_energy') is not None:
            result_text += f"阳离子能量: {results['cation_energy']:.8f} Hartree\n\n"
        
        if results.get('ionization_energy_ev') is not None:
            result_text += f"绝热电离能:\n"
            result_text += f"  {results['ionization_energy_hartree']:.6f} Hartree\n"
            result_text += f"  {results['ionization_energy_ev']:.3f} eV\n"
            result_text += f"  {results['ionization_energy_kcal_mol']:.2f} kcal/mol\n"
        
        self.results_text.setPlainText(result_text)
        
        # 保存结果供后续使用
        self.last_calculation_results = results

    def copy_calculation_results(self):
        """复制计算结果"""
        if hasattr(self, 'last_calculation_results'):
            results = self.last_calculation_results
            
            # 构建复制文本
            copy_text = f"PSI4电离能计算结果\n"
            copy_text += f"SMILES: {results['smiles']}\n"
            copy_text += f"方法: {results['high_level_method']}\n"
            
            if results.get('ionization_energy_ev') is not None:
                copy_text += f"电离能: {results['ionization_energy_ev']:.3f} eV\n"
                copy_text += f"电离能: {results['ionization_energy_kcal_mol']:.2f} kcal/mol\n"
            
            # 复制到剪贴板
            clipboard = QApplication.clipboard()
            clipboard.setText(copy_text)
            
            QMessageBox.information(self, "复制成功", "计算结果已复制到剪贴板")
        else:
            QMessageBox.warning(self, "警告", "没有可复制的计算结果")

    def add_result_to_plot(self):
        """将计算结果添加到图表"""
        if hasattr(self, 'last_calculation_results'):
            results = self.last_calculation_results
            
            if results.get('ionization_energy_ev') is not None:
                ie_value = results['ionization_energy_ev']
                compound_name = f"PSI4计算: {results['smiles']}"
                
                # 发送信号添加到图表
                compound_dialog_signals.ionization_energy_selected.emit(ie_value, compound_name)
                
                QMessageBox.information(self, "成功", 
                                      f"已将计算结果 {ie_value:.3f} eV 添加到图表")
            else:
                QMessageBox.warning(self, "警告", "计算结果中没有有效的电离能值")
        else:
            QMessageBox.warning(self, "警告", "没有可添加的计算结果")

    def save_result_to_database(self):
        """将计算结果保存到电离能数据库"""
        if not hasattr(self, 'last_calculation_results'):
            QMessageBox.warning(self, "警告", "没有可保存的计算结果")
            return

        results = self.last_calculation_results

        if not results.get('success', False) or results.get('ionization_energy_ev') is None:
            QMessageBox.warning(self, "警告", "计算结果无效或不完整")
            return

        try:
            # 弹出对话框让用户输入化合物名称
            from PyQt6.QtWidgets import QInputDialog

            # 从SMILES推导默认名称
            default_name = f"PSI4_{results.get('smiles', 'Unknown')[:10]}"
            if RDKIT_AVAILABLE:
                try:
                    from rdkit import Chem
                    from rdkit.Chem import rdMolDescriptors
                    mol = Chem.MolFromSmiles(results['smiles'])
                    if mol is not None:
                        formula = rdMolDescriptors.CalcMolFormula(mol)
                        default_name = f"PSI4_{formula}"
                except:
                    pass

            compound_name, ok = QInputDialog.getText(
                self,
                "输入化合物名称",
                "请输入要保存的化合物名称:",
                text=default_name
            )

            if not ok or not compound_name.strip():
                return  # 用户取消或输入为空

            compound_name = compound_name.strip()

            # 验证名称格式（避免特殊字符）
            import re
            if not re.match(r'^[a-zA-Z0-9_\-\s\(\)]+$', compound_name):
                QMessageBox.warning(self, "警告", "化合物名称只能包含字母、数字、下划线、连字符、空格和括号")
                return

            # 获取数据库文件路径
            db_path = os.path.join('database', 'nist_compounds.json')

            # 确保数据库目录存在
            os.makedirs(os.path.dirname(db_path), exist_ok=True)

            # 读取现有数据库或创建新的
            if os.path.exists(db_path):
                try:
                    with open(db_path, 'r', encoding='utf-8') as f:
                        database = json.load(f)
                except (json.JSONDecodeError, UnicodeDecodeError) as e:
                    QMessageBox.warning(self, "警告", f"数据库文件格式错误，将创建新的数据库文件: {e}")
                    database = []
            else:
                database = []
            
            # 使用RDKit从SMILES推导分子式和分子量
            mol_formula = ""
            mol_weight = 0.0
            
            if RDKIT_AVAILABLE:
                try:
                    from rdkit import Chem
                    from rdkit.Chem import rdMolDescriptors
                    
                    mol = Chem.MolFromSmiles(results['smiles'])
                    if mol is not None:
                        mol_formula = rdMolDescriptors.CalcMolFormula(mol)
                        mol_weight = rdMolDescriptors.CalcExactMolWt(mol)
                    else:
                        QMessageBox.warning(self, "警告", f"无法从SMILES解析分子: {results['smiles']}")
                        return
                except Exception as e:
                    QMessageBox.warning(self, "警告", f"计算分子式和分子量时出错: {e}")
                    return
            else:
                QMessageBox.warning(self, "警告", "RDKit不可用，无法计算分子式和分子量")
                return
            
            # 构造方法/基组字符串
            method_basis = f"{self.high_method_input.currentText().upper()}/{self.high_basis_input.currentText().upper()}"

            # 检查是否已存在相同的化合物（基于SMILES）
            existing_compound = None
            for i, compound in enumerate(database):
                if compound.get('smiles') == results['smiles']:
                    existing_compound = i
                    break

            # 生成新的化合物条目，按用户要求的格式
            new_compound = {
                "id": str(uuid.uuid4()),
                "name": compound_name,  # 使用用户输入的名称
                "smiles": results['smiles'],
                "formula": mol_formula,
                "mol_weight": mol_weight,
                "cas_rn": "",
                "source": "PSI4",  # 添加来源标识
                "ion_energetics": {
                    "ie_values": [f"{results['ionization_energy_ev']:.3f}"],
                    "methods": ["Calc."],  # 按用户要求设置为"Calc."
                    "references": ["P.W."],  # 按用户要求设置为"P.W."
                    "comments": [method_basis]  # 使用计算方法/基组作为备注
                },
                "metadata": {
                    "calculation_method": results.get('high_level_method', 'Unknown'),
                    "calculation_basis": results.get('high_level_basis', 'Unknown'),
                    "conformer_method": results.get('low_level_method', 'Unknown'),
                    "conformer_basis": results.get('low_level_basis', 'Unknown'),
                    "conformers_generated": results.get('conformers_generated', 0),
                    "conformers_optimized": results.get('conformers_optimized', 0),
                    "neutral_energy": results.get('neutral_energy', 0.0),
                    "cation_energy": results.get('cation_energy', 0.0),
                    "ie_hartree": results.get('ionization_energy_hartree', 0.0),
                    "ie_kcal_mol": results.get('ionization_energy_kcal_mol', 0.0),
                    "calculated_at": datetime.now().isoformat()
                }
            }

            if existing_compound is not None:
                # 询问用户是否追加新的电离能数据
                existing_entry = database[existing_compound]
                existing_ie_values = existing_entry.get('ion_energetics', {}).get('ie_values', [])

                reply = QMessageBox.question(
                    self,
                    "化合物已存在",
                    f"数据库中已存在相同SMILES的化合物：{existing_entry.get('name', 'Unknown')}\n"
                    f"现有电离能值: {', '.join(existing_ie_values)} eV\n"
                    f"新计算值: {results['ionization_energy_ev']:.3f} eV\n\n"
                    f"是否将新的电离能数据追加到现有化合物中？\n"
                    f"(选择'是'将追加数据，选择'否'将取消保存)",
                    QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
                    QMessageBox.StandardButton.Yes
                )

                if reply == QMessageBox.StandardButton.Yes:
                    # 追加新的电离能数据到现有化合物
                    existing_ion_energetics = existing_entry.setdefault('ion_energetics', {
                        'ie_values': [],
                        'methods': [],
                        'references': [],
                        'comments': []
                    })

                    # 追加新数据到各个数组
                    existing_ion_energetics['ie_values'].append(f"{results['ionization_energy_ev']:.3f}")
                    existing_ion_energetics['methods'].append("Calc.")
                    existing_ion_energetics['references'].append("P.W.")
                    existing_ion_energetics['comments'].append(method_basis)

                    # 更新元数据（如果存在的话）
                    if 'metadata' not in existing_entry:
                        existing_entry['metadata'] = {}

                    # 添加新的计算记录到元数据
                    if 'calculation_history' not in existing_entry['metadata']:
                        existing_entry['metadata']['calculation_history'] = []

                    existing_entry['metadata']['calculation_history'].append({
                        'calculation_method': results.get('high_level_method', 'Unknown'),
                        'calculation_basis': results.get('high_level_basis', 'Unknown'),
                        'conformer_method': results.get('low_level_method', 'Unknown'),
                        'conformer_basis': results.get('low_level_basis', 'Unknown'),
                        'conformers_generated': results.get('conformers_generated', 0),
                        'conformers_optimized': results.get('conformers_optimized', 0),
                        'neutral_energy': results.get('neutral_energy', 0.0),
                        'cation_energy': results.get('cation_energy', 0.0),
                        'ie_hartree': results.get('ionization_energy_hartree', 0.0),
                        'ie_kcal_mol': results.get('ionization_energy_kcal_mol', 0.0),
                        'calculated_at': datetime.now().isoformat(),
                        'user_name': compound_name  # 记录用户输入的名称
                    })

                    # 更新最后修改时间
                    existing_entry['metadata']['last_updated'] = datetime.now().isoformat()

                else:
                    return
            else:
                database.append(new_compound)

            # 保存数据库
            try:
                with open(db_path, 'w', encoding='utf-8') as f:
                    json.dump(database, f, ensure_ascii=False, indent=2)

                QMessageBox.information(self, "保存成功",
                                      f"PSI4计算结果已成功保存到NIST数据库！\n\n"
                                      f"化合物名称: {compound_name}\n"
                                      f"分子式: {mol_formula}\n"
                                      f"分子量: {mol_weight:.4f}\n"
                                      f"电离能: {results['ionization_energy_ev']:.3f} eV\n"
                                      f"计算方法: {method_basis}\n"
                                      f"保存位置: {db_path}")

            except Exception as e:
                QMessageBox.critical(self, "保存失败", f"写入数据库文件时出错: {str(e)}")

        except Exception as e:
            QMessageBox.critical(self, "保存失败", f"保存到数据库时出错:\n{str(e)}")
            import traceback
            traceback.print_exc()
