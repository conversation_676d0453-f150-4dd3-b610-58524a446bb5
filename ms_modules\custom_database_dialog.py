"""
自定义数据库数据输入对话框
允许用户手动添加电离能和光电离截面数据到数据库
"""

import json
import os
from PyQt6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QTabWidget, QWidget, QLabel, 
    QLineEdit, QTextEdit, QPushButton, QGroupBox, QFormLayout,
    QMessageBox, QFileDialog, QCheckBox, QScrollArea
)
from PyQt6.QtGui import QDoubleValidator
from PyQt6.QtCore import Qt


class CustomDatabaseDialog(QDialog):
    """自定义数据库数据输入对话框"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("自定义数据库数据")
        self.setGeometry(200, 200, 800, 700)
        self.setModal(True)
        
        self.initUI()
        
    def initUI(self):
        """初始化用户界面"""
        layout = QVBoxLayout()
        
        # 创建选项卡控件
        self.tab_widget = QTabWidget()
        
        # 创建电离能数据选项卡
        self.ie_tab = self.createIonizationEnergyTab()
        self.tab_widget.addTab(self.ie_tab, "电离能数据")
        
        # 创建光电离截面数据选项卡
        self.cs_tab = self.createCrossSectionTab()
        self.tab_widget.addTab(self.cs_tab, "光电离截面数据")
        
        layout.addWidget(self.tab_widget)
        
        # 按钮布局
        button_layout = QHBoxLayout()
        
        # 预览数据按钮
        self.preview_btn = QPushButton("预览数据")
        self.preview_btn.clicked.connect(self.previewData)
        button_layout.addWidget(self.preview_btn)
        
        button_layout.addStretch()
        
        # 保存按钮
        self.save_btn = QPushButton("保存数据")
        self.save_btn.clicked.connect(self.saveData)
        button_layout.addWidget(self.save_btn)
        
        # 取消按钮
        self.cancel_btn = QPushButton("取消")
        self.cancel_btn.clicked.connect(self.reject)
        button_layout.addWidget(self.cancel_btn)
        
        layout.addLayout(button_layout)
        
        self.setLayout(layout)
        
    def createIonizationEnergyTab(self):
        """创建电离能数据输入选项卡"""
        tab = QWidget()
        
        # 创建滚动区域
        scroll = QScrollArea()
        scroll_widget = QWidget()
        layout = QVBoxLayout(scroll_widget)
        
        # 基本信息组
        basic_group = QGroupBox("基本信息 (必填)")
        basic_layout = QFormLayout()
        
        # SMILES字符串 (必填)
        self.ie_smiles = QLineEdit()
        self.ie_smiles.setPlaceholderText("例: O=[N+]([O-])c1ccc(O)cc1")
        basic_layout.addRow("SMILES结构式 *:", self.ie_smiles)
        
        # 化合物名称 (必填)
        self.ie_name = QLineEdit()
        self.ie_name.setPlaceholderText("例: 4-Nitrophenol")
        basic_layout.addRow("化合物名称 *:", self.ie_name)
        
        # 分子量 (必填)
        self.ie_mol_weight = QLineEdit()
        self.ie_mol_weight.setValidator(QDoubleValidator())
        self.ie_mol_weight.setPlaceholderText("例: 139.1088")
        basic_layout.addRow("分子量 *:", self.ie_mol_weight)
        
        basic_group.setLayout(basic_layout)
        layout.addWidget(basic_group)
        
        # 电离能数据组 (必填)
        ie_group = QGroupBox("电离能数据 (必填)")
        ie_layout = QVBoxLayout()
        
        # 电离能值
        ie_layout.addWidget(QLabel("电离能值 * (每行一个值，或用逗号分隔):"))
        self.ie_values = QTextEdit()
        self.ie_values.setMaximumHeight(80)
        self.ie_values.setPlaceholderText("例:\n9.1\n7.38\n8.8 ± 0.1\n\n或: 9.1, 7.38, 8.8 ± 0.1")
        ie_layout.addWidget(self.ie_values)
        
        # 测量方法
        ie_layout.addWidget(QLabel("测量方法 * (与电离能值一一对应):"))
        self.ie_methods = QTextEdit()
        self.ie_methods.setMaximumHeight(80)
        self.ie_methods.setPlaceholderText("例:\nPE\nEI\nEI\n\n或: PE, EI, EI")
        ie_layout.addWidget(self.ie_methods)
        
        # 参考文献
        ie_layout.addWidget(QLabel("参考文献 * (与电离能值一一对应):"))
        self.ie_references = QTextEdit()
        self.ie_references.setMaximumHeight(80)
        self.ie_references.setPlaceholderText("例:\nKobayashi and Nagakura, 1975\nJohnstone and Mellon, 1973\n\n或用逗号分隔")
        ie_layout.addWidget(self.ie_references)
        
        ie_group.setLayout(ie_layout)
        layout.addWidget(ie_group)
        
        # 可选信息组
        optional_group = QGroupBox("可选信息")
        optional_layout = QFormLayout()
        
        # 分子式
        self.ie_formula = QLineEdit()
        self.ie_formula.setPlaceholderText("例: C6H5NO3")
        optional_layout.addRow("分子式:", self.ie_formula)
        
        # CAS号
        self.ie_cas_rn = QLineEdit()
        self.ie_cas_rn.setPlaceholderText("例: 100-02-7")
        optional_layout.addRow("CAS号:", self.ie_cas_rn)
        
        # InChI
        self.ie_inchi = QLineEdit()
        self.ie_inchi.setPlaceholderText("例: InChI=1S/C6H5NO3/c8-6-3-1-5(2-4-6)7(9)10/h1-4,8H")
        optional_layout.addRow("InChI:", self.ie_inchi)
        
        # InChI Key
        self.ie_inchi_key = QLineEdit()
        self.ie_inchi_key.setPlaceholderText("例: BTJIUGUIPKRLHP-UHFFFAOYSA-N")
        optional_layout.addRow("InChI Key:", self.ie_inchi_key)
        
        # 备注
        self.ie_comments = QTextEdit()
        self.ie_comments.setMaximumHeight(60)
        self.ie_comments.setPlaceholderText("例:\nLLK\nLLK\nRDSH\n\n或用逗号分隔")
        optional_layout.addRow("备注:", self.ie_comments)
        
        optional_group.setLayout(optional_layout)
        layout.addWidget(optional_group)
        
        # 同义词组
        synonyms_group = QGroupBox("同义词 (可选)")
        synonyms_layout = QVBoxLayout()
        synonyms_layout.addWidget(QLabel("同义词 (每行一个，或用逗号分隔):"))
        self.ie_synonyms = QTextEdit()
        self.ie_synonyms.setMaximumHeight(80)
        self.ie_synonyms.setPlaceholderText("例:\nPhenol, p-nitro-\np-Hydroxynitrobenzene\np-Nitrophenol\n\n或用逗号分隔")
        synonyms_layout.addWidget(self.ie_synonyms)
        synonyms_group.setLayout(synonyms_layout)
        layout.addWidget(synonyms_group)
        
        # 设置滚动区域
        scroll.setWidget(scroll_widget)
        scroll.setWidgetResizable(True)
        
        tab_layout = QVBoxLayout(tab)
        tab_layout.addWidget(scroll)
        
        return tab
        
    def createCrossSectionTab(self):
        """创建光电离截面数据输入选项卡"""
        tab = QWidget()
        
        # 创建滚动区域
        scroll = QScrollArea()
        scroll_widget = QWidget()
        layout = QVBoxLayout(scroll_widget)
        
        # 基本信息组 (必填)
        basic_group = QGroupBox("基本信息 (必填)")
        basic_layout = QFormLayout()
        
        # 化合物名称 (必填)
        self.cs_name = QLineEdit()
        self.cs_name.setPlaceholderText("例: Atomic hydrogen")
        basic_layout.addRow("化合物名称 *:", self.cs_name)
        
        # 质量数 (必填)
        self.cs_mass_number = QLineEdit()
        self.cs_mass_number.setValidator(QDoubleValidator())
        self.cs_mass_number.setPlaceholderText("例: 1")
        basic_layout.addRow("质量数 *:", self.cs_mass_number)
        
        # 数据来源 (必填)
        self.cs_source = QLineEdit()
        self.cs_source.setPlaceholderText("例: H. P. Palenius, et al., Phys. Rev. A 1976, 13 (5), 1805-1816.")
        basic_layout.addRow("数据来源 *:", self.cs_source)
        
        basic_group.setLayout(basic_layout)
        layout.addWidget(basic_group)
        
        # 光电离截面数据组 (必填)
        cs_group = QGroupBox("光电离截面数据 (必填)")
        cs_layout = QVBoxLayout()
        
        # 能量数据
        cs_layout.addWidget(QLabel("能量数据 * (eV，每行一个值，或用逗号分隔):"))
        self.cs_energy = QTextEdit()
        self.cs_energy.setMaximumHeight(100)
        self.cs_energy.setPlaceholderText("例:\n13.6\n14.0\n14.5\n15.0\n\n或: 13.6, 14.0, 14.5, 15.0")
        cs_layout.addWidget(self.cs_energy)
        
        # 截面数据
        cs_layout.addWidget(QLabel("光电离截面 * (Mb，与能量数据一一对应):"))
        self.cs_cross_section = QTextEdit()
        self.cs_cross_section.setMaximumHeight(100)
        self.cs_cross_section.setPlaceholderText("例:\n6.3\n5.8\n5.2\n4.7\n\n或: 6.3, 5.8, 5.2, 4.7")
        cs_layout.addWidget(self.cs_cross_section)
        
        cs_group.setLayout(cs_layout)
        layout.addWidget(cs_group)
        
        # 可选信息组
        optional_group = QGroupBox("可选信息")
        optional_layout = QFormLayout()
        
        # 分子式
        self.cs_formula = QLineEdit()
        self.cs_formula.setPlaceholderText("例: H")
        optional_layout.addRow("分子式:", self.cs_formula)
        
        # 电离能
        self.cs_ionization_energy = QLineEdit()
        self.cs_ionization_energy.setValidator(QDoubleValidator())
        self.cs_ionization_energy.setPlaceholderText("例: 13.59844")
        optional_layout.addRow("电离能 (eV):", self.cs_ionization_energy)
        
        optional_group.setLayout(optional_layout)
        layout.addWidget(optional_group)
        
        # 设置滚动区域
        scroll.setWidget(scroll_widget)
        scroll.setWidgetResizable(True)
        
        tab_layout = QVBoxLayout(tab)
        tab_layout.addWidget(scroll)
        
        return tab
        
    def parseArrayInput(self, text):
        """解析数组输入"""
        if not text.strip():
            return []
            
        # 首先尝试按行分割
        lines = [line.strip() for line in text.strip().split('\n') if line.strip()]
        
        # 如果只有一行，尝试按逗号分割
        if len(lines) == 1:
            values = [val.strip() for val in lines[0].split(',') if val.strip()]
        else:
            values = lines
            
        return values
        
    def validateIonizationEnergyData(self):
        """验证电离能数据"""
        # 检查必填字段
        if not self.ie_smiles.text().strip():
            QMessageBox.warning(self, "输入错误", "请输入SMILES结构式")
            return False
            
        if not self.ie_name.text().strip():
            QMessageBox.warning(self, "输入错误", "请输入化合物名称")
            return False
            
        if not self.ie_mol_weight.text().strip():
            QMessageBox.warning(self, "输入错误", "请输入分子量")
            return False
            
        try:
            float(self.ie_mol_weight.text())
        except ValueError:
            QMessageBox.warning(self, "输入错误", "分子量必须是数字")
            return False
            
        # 检查电离能数据
        ie_values = self.parseArrayInput(self.ie_values.toPlainText())
        methods = self.parseArrayInput(self.ie_methods.toPlainText())
        references = self.parseArrayInput(self.ie_references.toPlainText())
        
        if not ie_values:
            QMessageBox.warning(self, "输入错误", "请输入至少一个电离能值")
            return False
            
        if not methods:
            QMessageBox.warning(self, "输入错误", "请输入测量方法")
            return False
            
        if not references:
            QMessageBox.warning(self, "输入错误", "请输入参考文献")
            return False
            
        if len(ie_values) != len(methods):
            QMessageBox.warning(self, "输入错误", f"电离能值数量({len(ie_values)})与测量方法数量({len(methods)})不匹配")
            return False
            
        if len(ie_values) != len(references):
            QMessageBox.warning(self, "输入错误", f"电离能值数量({len(ie_values)})与参考文献数量({len(references)})不匹配")
            return False
            
        return True
        
    def validateCrossSectionData(self):
        """验证光电离截面数据"""
        # 检查必填字段
        if not self.cs_name.text().strip():
            QMessageBox.warning(self, "输入错误", "请输入化合物名称")
            return False
            
        if not self.cs_mass_number.text().strip():
            QMessageBox.warning(self, "输入错误", "请输入质量数")
            return False
            
        try:
            float(self.cs_mass_number.text())
        except ValueError:
            QMessageBox.warning(self, "输入错误", "质量数必须是数字")
            return False
            
        if not self.cs_source.text().strip():
            QMessageBox.warning(self, "输入错误", "请输入数据来源")
            return False
            
        # 检查能量和截面数据
        energy_values = self.parseArrayInput(self.cs_energy.toPlainText())
        cs_values = self.parseArrayInput(self.cs_cross_section.toPlainText())
        
        if not energy_values:
            QMessageBox.warning(self, "输入错误", "请输入至少一个能量值")
            return False
            
        if not cs_values:
            QMessageBox.warning(self, "输入错误", "请输入至少一个光电离截面值")
            return False
            
        if len(energy_values) != len(cs_values):
            QMessageBox.warning(self, "输入错误", f"能量数据点数量({len(energy_values)})与截面数据点数量({len(cs_values)})不匹配")
            return False
            
        # 验证数值格式
        try:
            for val in energy_values:
                float(val)
        except ValueError:
            QMessageBox.warning(self, "输入错误", "能量值必须是数字")
            return False
            
        try:
            for val in cs_values:
                float(val)
        except ValueError:
            QMessageBox.warning(self, "输入错误", "光电离截面值必须是数字")
            return False
            
        return True
        
    def generateIonizationEnergyData(self):
        """生成电离能数据字典"""
        # 解析数组数据
        ie_values = self.parseArrayInput(self.ie_values.toPlainText())
        methods = self.parseArrayInput(self.ie_methods.toPlainText())
        references = self.parseArrayInput(self.ie_references.toPlainText())
        comments = self.parseArrayInput(self.ie_comments.toPlainText())
        synonyms = self.parseArrayInput(self.ie_synonyms.toPlainText())
        
        # 生成ID (简单使用名称+分子式)
        name = self.ie_name.text().strip()
        formula = self.ie_formula.text().strip() or "Unknown"
        compound_id = f"{name}_{formula}".replace(" ", "_")
        
        data = {
            "id": compound_id,
            "name": name,
            "formula": formula,
            "mol_weight": float(self.ie_mol_weight.text()),
            "smiles": self.ie_smiles.text().strip(),
            "ion_energetics": {
                "ie_values": ie_values,
                "methods": methods,
                "references": references
            }
        }
        
        # 添加可选字段
        if self.ie_cas_rn.text().strip():
            data["cas_rn"] = self.ie_cas_rn.text().strip()
            
        if self.ie_inchi.text().strip():
            data["inchi"] = self.ie_inchi.text().strip()
            
        if self.ie_inchi_key.text().strip():
            data["inchi_key"] = self.ie_inchi_key.text().strip()
            
        if synonyms:
            data["synonyms"] = synonyms
            
        if comments:
            data["ion_energetics"]["comments"] = comments
            
        return data
        
    def generateCrossSectionData(self):
        """生成光电离截面数据字典"""
        # 解析数组数据
        energy_values = [float(val) for val in self.parseArrayInput(self.cs_energy.toPlainText())]
        cs_values = [float(val) for val in self.parseArrayInput(self.cs_cross_section.toPlainText())]
        
        # 生成唯一键
        name = self.cs_name.text().strip()
        source = self.cs_source.text().strip()
        key = f"{name}_{source}".replace(" ", "_").replace(",", "").replace(".", "")
        
        data = {
            "name": name,
            "mass_number": float(self.cs_mass_number.text()),
            "source": source,
            "energy": energy_values,
            "cross_section": cs_values
        }
        
        # 添加可选字段
        if self.cs_formula.text().strip():
            data["formula"] = self.cs_formula.text().strip()
            
        if self.cs_ionization_energy.text().strip():
            data["ionization_energy"] = float(self.cs_ionization_energy.text())
            
        return key, data
        
    def previewData(self):
        """预览要保存的数据"""
        current_tab = self.tab_widget.currentIndex()
        
        try:
            if current_tab == 0:  # 电离能数据
                if not self.validateIonizationEnergyData():
                    return
                data = self.generateIonizationEnergyData()
                preview_text = json.dumps(data, indent=2, ensure_ascii=False)
                
            else:  # 光电离截面数据
                if not self.validateCrossSectionData():
                    return
                key, data = self.generateCrossSectionData()
                preview_data = {key: data}
                preview_text = json.dumps(preview_data, indent=2, ensure_ascii=False)
                
            # 显示预览对话框
            preview_dialog = QDialog(self)
            preview_dialog.setWindowTitle("数据预览")
            preview_dialog.setGeometry(300, 300, 600, 500)
            
            layout = QVBoxLayout()
            
            preview_area = QTextEdit()
            preview_area.setPlainText(preview_text)
            preview_area.setReadOnly(True)
            layout.addWidget(preview_area)
            
            close_btn = QPushButton("关闭")
            close_btn.clicked.connect(preview_dialog.accept)
            layout.addWidget(close_btn)
            
            preview_dialog.setLayout(layout)
            preview_dialog.exec()
            
        except Exception as e:
            QMessageBox.critical(self, "预览错误", f"生成预览数据时出错: {str(e)}")
            
    def saveData(self):
        """保存数据到相应的JSON文件"""
        current_tab = self.tab_widget.currentIndex()
        
        try:
            if current_tab == 0:  # 电离能数据
                if not self.validateIonizationEnergyData():
                    return
                    
                data = self.generateIonizationEnergyData()
                
                # 保存到nist_compounds.json
                db_path = "database/nist_compounds.json"
                if os.path.exists(db_path):
                    with open(db_path, 'r', encoding='utf-8') as f:
                        existing_data = json.load(f)
                else:
                    existing_data = []
                    
                # 添加新数据
                existing_data.append(data)
                
                # 保存回文件
                with open(db_path, 'w', encoding='utf-8') as f:
                    json.dump(existing_data, f, indent=2, ensure_ascii=False)
                    
                QMessageBox.information(self, "保存成功", f"电离能数据已添加到 {db_path}")
                
            else:  # 光电离截面数据
                if not self.validateCrossSectionData():
                    return
                    
                key, data = self.generateCrossSectionData()
                
                # 保存到compounds_data.json
                db_path = "database/compounds_data.json"
                if os.path.exists(db_path):
                    with open(db_path, 'r', encoding='utf-8') as f:
                        existing_data = json.load(f)
                else:
                    existing_data = {}
                    
                # 添加新数据
                existing_data[key] = data
                
                # 保存回文件
                with open(db_path, 'w', encoding='utf-8') as f:
                    json.dump(existing_data, f, indent=2, ensure_ascii=False)
                    
                QMessageBox.information(self, "保存成功", f"光电离截面数据已添加到 {db_path}")
                
            # 询问是否继续添加
            reply = QMessageBox.question(self, "继续添加", 
                                       "数据已保存。是否继续添加更多数据？",
                                       QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No)
            
            if reply == QMessageBox.StandardButton.No:
                self.accept()
            else:
                # 清空当前tab的输入字段
                self.clearCurrentTab()
                
        except Exception as e:
            QMessageBox.critical(self, "保存错误", f"保存数据时出错: {str(e)}")
            
    def clearCurrentTab(self):
        """清空当前选项卡的输入字段"""
        current_tab = self.tab_widget.currentIndex()
        
        if current_tab == 0:  # 电离能数据
            self.ie_smiles.clear()
            self.ie_name.clear()
            self.ie_mol_weight.clear()
            self.ie_values.clear()
            self.ie_methods.clear()
            self.ie_references.clear()
            self.ie_formula.clear()
            self.ie_cas_rn.clear()
            self.ie_inchi.clear()
            self.ie_inchi_key.clear()
            self.ie_comments.clear()
            self.ie_synonyms.clear()
            
        else:  # 光电离截面数据
            self.cs_name.clear()
            self.cs_mass_number.clear()
            self.cs_source.clear()
            self.cs_energy.clear()
            self.cs_cross_section.clear()
            self.cs_formula.clear()
            self.cs_ionization_energy.clear() 