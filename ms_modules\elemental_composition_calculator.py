import sys
import os
import json
import copy
import time
import threading
import tempfile
import shutil
import uuid
from datetime import datetime
import numpy as np
from scipy.optimize import nnls
from scipy.interpolate import interp1d

from PyQt6.QtWidgets import (QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
                            QLabel, QLineEdit, QPushButton, QTableWidget, QTableWidgetItem,
                            QGridLayout, QGroupBox, QSpinBox, QDoubleSpinBox, QFileDialog,
                            QMessageBox, QTabWidget, QSplitter, QListWidget, QListWidgetItem,
                            QScrollArea, QHeaderView, QMenu, QComboBox, QTextEdit, QCheckBox)
from PyQt6.QtCore import Qt, pyqtSignal, QThread, pyqtSlot
from PyQt6.QtGui import QFont, QClipboard, QPixmap, QColor

# 导入PyQtGraph
import pyqtgraph as pg

# 导入化合物信息模块
from .nist_database import NISTDatabase
from .molecule_drawer import MoleculeDrawer

# 尝试导入sklearn，如果不可用则提供一个警告
try:
    from sklearn.linear_model import Lasso, Ridge
    SKLEARN_AVAILABLE = True
except ImportError:
    SKLEARN_AVAILABLE = False
    print("警告: sklearn库不可用，将只能使用非负最小二乘法进行拟合")

# 检查PSI4和RDKit可用性
try:
    from .adiabatic_ionization_energy import calculate_ionization_energy
    PSI4_AVAILABLE = True
except ImportError:
    PSI4_AVAILABLE = False
    print("注意: PSI4/RDKit模块不可用，PSI4电离能计算功能将被禁用")

class CalculatorInstanceManager:
    """管理计算器实例的工具类"""
    _instances = []

    @classmethod
    def add_instance(cls, instance):
        """添加实例"""
        cls._instances.append(instance)

    @classmethod
    def remove_instance(cls, instance):
        """移除实例"""
        if instance in cls._instances:
            cls._instances.remove(instance)

    @classmethod
    def get_instances(cls):
        """获取所有实例"""
        return cls._instances.copy()


class WeightCalculator:
    """权重计算工具类，避免重复代码"""

    @staticmethod
    def create_energy_weights(energy_points, weight_ranges=None):
        """创建能量区间重要性权重数组

        Args:
            energy_points: 能量数组
            weight_ranges: 权重范围列表，格式为[(start, end, weight), ...]

        Returns:
            权重数组
        """
        weights = np.ones_like(energy_points)

        if weight_ranges:
            for start_e, end_e, weight in weight_ranges:
                mask = (energy_points >= start_e) & (energy_points <= end_e)
                weights[mask] = weight

        return weights


class MetricsCalculator:
    """评价指标计算工具类，避免重复代码"""

    @staticmethod
    def calculate_fit_metrics(target_pie, fitted_pie, energy):
        """计算拟合质量的多个评价指标

        Args:
            target_pie: 目标光电离曲线数据
            fitted_pie: 拟合曲线数据
            energy: 能量数据

        Returns:
            metrics: 包含多个评价指标的字典
        """
        # 计算有效数据点（target_pie > 1e-10）的掩码
        mask = target_pie > 1e-10
        valid_target = target_pie[mask]
        valid_fitted = fitted_pie[mask]
        valid_energy = energy[mask]

        if len(valid_target) == 0:
            return {
                'r_squared': 0.0,
                'rmse': float('inf'),
                'relative_error': float('inf'),
                'peak_position_error': float('inf'),
                'threshold_energy_error': float('inf'),
                'overall_score': 0.0
            }

        # 1. R²值
        ss_res = np.sum((valid_target - valid_fitted) ** 2)
        ss_tot = np.sum((valid_target - np.mean(valid_target)) ** 2)
        r_squared = 1 - (ss_res / ss_tot) if ss_tot > 0 else 0

        # 2. RMSE
        rmse = np.sqrt(np.mean((valid_target - valid_fitted) ** 2))

        # 3. 相对误差
        relative_error = rmse / (np.mean(valid_target) + 1e-10)

        # 4. 峰值位置误差
        try:
            target_peak_idx = np.argmax(valid_target)
            fitted_peak_idx = np.argmax(valid_fitted)
            peak_position_error = abs(valid_energy[target_peak_idx] - valid_energy[fitted_peak_idx])
        except:
            peak_position_error = float('inf')

        # 5. 阈值能量误差（10%峰值处的能量差）
        try:
            threshold = 0.1 * np.max(valid_target)
            target_threshold_idx = np.where(valid_target >= threshold)[0]
            fitted_threshold_idx = np.where(valid_fitted >= threshold)[0]

            if len(target_threshold_idx) > 0 and len(fitted_threshold_idx) > 0:
                target_threshold_energy = valid_energy[target_threshold_idx[0]]
                fitted_threshold_energy = valid_energy[fitted_threshold_idx[0]]
                threshold_energy_error = abs(target_threshold_energy - fitted_threshold_energy)
            else:
                threshold_energy_error = float('inf')
        except:
            threshold_energy_error = float('inf')

        # 6. 综合评分
        overall_score = MetricsCalculator.calculate_overall_score(
            r_squared, relative_error, peak_position_error, threshold_energy_error
        )

        return {
            'r_squared': r_squared,
            'rmse': rmse,
            'relative_error': relative_error,
            'peak_position_error': peak_position_error,
            'threshold_energy_error': threshold_energy_error,
            'overall_score': overall_score
        }

    @staticmethod
    def calculate_overall_score(r_squared, relative_error, peak_position_error, threshold_energy_error):
        """计算综合评分"""
        # 权重分配
        w_r2 = 0.4
        w_rel_err = 0.3
        w_peak_pos = 0.2
        w_threshold = 0.1

        # 归一化各项指标到0-1范围
        r2_score = max(0, r_squared)

        rel_err_score = max(0, 1 - min(1, relative_error))

        peak_pos_score = max(0, 1 - min(1, peak_position_error / 2.0))

        threshold_score = max(0, 1 - min(1, threshold_energy_error / 2.0))

        # 计算加权总分
        overall_score = (w_r2 * r2_score +
                        w_rel_err * rel_err_score +
                        w_peak_pos * peak_pos_score +
                        w_threshold * threshold_score)

        return overall_score


class MainWindowFinder:
    """主窗口查找工具类，避免重复代码"""

    @staticmethod
    def find_main_window():
        """查找主窗口"""
        app = QApplication.instance()
        if app:
            for widget in app.topLevelWidgets():
                if hasattr(widget, 'results_widget') and hasattr(widget, 'peak_editor'):
                    return widget
        return None


class ElementalCompositionCalculator(QMainWindow):
    def __init__(self, initial_mass=None):
        super().__init__()
        self.setWindowTitle("化合物查询 - 质谱数据分析")
        self.setGeometry(100, 100, 1200, 700)  # 增加窗口宽度到 1200
        self.initial_mass = initial_mass

        # 将实例添加到管理器中
        CalculatorInstanceManager.add_instance(self)

        # 为每个质量数记录其实验数据的原始坐标轴范围
        self.mass_axis_ranges = {}  # 格式: {mass_value: {'x_range': [min, max], 'y_range': [min, max]}}
        self.current_mass_for_axis = None  # 当前用于坐标轴范围的质量数

        # 日志等级缓存
        self._cached_log_level = None

        # 加载化合物数据库（统一使用JSON文件）
        self.load_nist_compounds_data()

        # 更新元素及其精确质量 - 使用更精确的质量数据
        self.elements = {
            'C': 12.0107,
            'H': 1.00794,
            'N': 14.0067,
            'O': 15.9994,
            'F': 18.9984,
            'Na': 22.9897,
            'Si': 28.0855,
            'P': 30.9738,
            'S': 32.0650,  # 修正S的质量数
            'Cl': 35.4530,  # 修正Cl的质量数
            'Br': 79.9040
        }

        self.init_ui()

    def closeEvent(self, event):
        """窗口关闭时清理实例"""
        CalculatorInstanceManager.remove_instance(self)
        super().closeEvent(event)
    
    def get_log_level(self):
        """获取当前的日志显示等级
        
        返回:
            int: 日志等级 (1: 不显示, 2: 简略显示, 3: 详细显示)
        """
        # 使用缓存避免频繁获取设置
        if self._cached_log_level is not None:
            return self._cached_log_level
            
        try:
            # 尝试从主窗口获取设置
            from PyQt6.QtWidgets import QApplication
            for widget in QApplication.topLevelWidgets():
                if hasattr(widget, 'settings_widget') and hasattr(widget.settings_widget, 'getSettings'):
                    settings = widget.settings_widget.getSettings()
                    self._cached_log_level = settings.get('log_level', 3)  # 默认为详细显示
                    return self._cached_log_level
        except Exception:
            pass
        
        # 如果无法获取设置，默认为详细显示
        self._cached_log_level = 3
        return self._cached_log_level
    
    def log_print(self, message, level=2):
        """分级日志打印函数
        
        参数:
            message: 要打印的消息
            level: 消息等级 (1: 错误/警告信息, 2: 简略信息, 3: 详细信息)
        """
        current_level = self.get_log_level()
        if current_level >= level:
            print(message)
    
    def refresh_log_level(self):
        """刷新日志等级缓存（在设置变更时调用）"""
        self._cached_log_level = None

    def on_solution_changed(self):
        """当用户切换拟合方案时的处理"""
        try:
            if not hasattr(self, 'current_solutions') or not self.current_solutions:
                return

            # 获取当前选中的方案索引
            current_index = self.solution_combo.currentIndex()
            if current_index < 0 or current_index >= len(self.current_solutions):
                return

            # 获取选中的方案
            selected_compounds, selected_fit, selected_metrics = self.current_solutions[current_index]

            # 更新化合物列表显示
            self.update_compounds_display_for_solution(selected_compounds)

            # 更新主窗口显示
            self.update_main_window_for_solution(selected_compounds, selected_fit)

            # 更新结果文本
            self.update_result_text_for_solution(selected_compounds, selected_metrics)

            print(f"已切换到方案 {current_index + 1}")

        except Exception as e:
            print(f"切换方案时出错: {str(e)}")
            import traceback
            traceback.print_exc()

    def update_compounds_display_for_solution(self, selected_compounds):
        """为选定方案更新化合物列表显示"""
        try:
            # 清空当前表格
            self.pie_compounds_list.setRowCount(0)

            # 创建化合物字典用于快速查找
            solution_compounds_dict = {}
            for comp in selected_compounds:
                compound_name = comp['compound'].get('name', '')
                compound_source = comp['compound'].get('source', '')
                key = f"{compound_name}|{compound_source}"
                solution_compounds_dict[key] = comp['coefficient']

            # 使用保存的所有匹配化合物
            if hasattr(self, 'all_matching_compounds'):
                compounds_to_display = []
                for compound in self.all_matching_compounds:
                    compound_name = compound.get('name', '')
                    compound_source = compound.get('source', '')
                    key = f"{compound_name}|{compound_source}"

                    if key in solution_compounds_dict:
                        coefficient = solution_compounds_dict[key]
                        checked = True
                    else:
                        coefficient = 0.0
                        checked = False

                    compounds_to_display.append({
                        'compound': compound,
                        'coefficient': coefficient,
                        'checked': checked
                    })

                # 按系数排序
                compounds_to_display.sort(key=lambda x: -x['coefficient'])

                # 填充表格
                self.pie_compounds_list.setRowCount(len(compounds_to_display))
                for i, item in enumerate(compounds_to_display):
                    self._fill_compound_row(i, item)

                # 调整列宽
                self.pie_compounds_list.resizeColumnsToContents()

        except Exception as e:
            print(f"更新化合物显示时出错: {str(e)}")

    def update_main_window_for_solution(self, selected_compounds, selected_fit):
        """为选定方案更新主窗口显示"""
        try:
            # 查找主窗口
            main_window = self._find_main_window()
            if not main_window:
                return

            # 准备化合物数据
            compounds_to_show = []
            for comp in selected_compounds:
                # 从原始化合物列表中找到完整数据
                full_compound_data = self._find_full_compound_data(comp['compound'])
                if full_compound_data:
                    full_compound_data['weight'] = comp['coefficient']
                    compounds_to_show.append(full_compound_data)

            # 显示PIE曲线
            if compounds_to_show:
                energy_points = np.linspace(8, 16, 100)  # 默认能量范围
                self.show_pie_in_main_window(main_window, compounds_to_show, energy_points, None, selected_fit)

        except Exception as e:
            print(f"更新主窗口显示时出错: {str(e)}")

    def update_result_text_for_solution(self, selected_compounds, selected_metrics):
        """为选定方案更新结果文本"""
        try:
            result_text = f"PIE拟合结果 (R² = {selected_metrics['r_squared']:.4f}):\n\n"
            result_text += f"相对误差: {selected_metrics.get('relative_error', 0):.2%}\n"
            result_text += f"峰值位置误差: {selected_metrics.get('peak_position_error', 0):.4f} eV\n"
            result_text += f"阈值能量误差: {selected_metrics.get('threshold_energy_error', 0):.4f} eV\n"
            result_text += f"RMSE: {selected_metrics.get('rmse', 0):.4f}\n\n"

            # 计算总系数
            total_coef = sum(comp['coefficient'] for comp in selected_compounds)

            for comp in selected_compounds:
                compound_data = comp['compound']
                name = compound_data.get('name', '')
                formula = compound_data.get('formula', '')
                mass = compound_data.get('mol_weight', 0)
                coef = comp['coefficient']
                norm_coef = coef / total_coef if total_coef > 0 else 0
                source = compound_data.get('source', '')

                mass_float = float(mass) if isinstance(mass, (str, int, float)) else 0.0
                result_text += f"{name} ({formula}, {mass_float:.4f}): 系数={coef:.6f}, 占比={norm_coef:.2%}\n"

            self.pie_result_text.setText(result_text)

        except Exception as e:
            print(f"更新结果文本时出错: {str(e)}")

    def _fill_compound_row(self, row, item):
        """填充化合物表格行"""
        compound = item['compound']
        coefficient = item['coefficient']
        checked = item['checked']

        # 获取化合物信息
        name = compound.get('name', '')
        formula = compound.get('formula', '')
        mass = compound.get('mol_weight', 0)
        source = compound.get('source', '')

        # 创建表格项
        checkbox = QTableWidgetItem()
        checkbox.setCheckState(Qt.CheckState.Checked if checked else Qt.CheckState.Unchecked)

        weight_item = QTableWidgetItem(f"{coefficient:.10f}")
        name_item = QTableWidgetItem(name)
        formula_item = QTableWidgetItem(formula)
        mass_item = QTableWidgetItem(f"{mass:.4f}")
        ie_item = QTableWidgetItem(self.get_first_ionization_energy(compound))
        source_item = QTableWidgetItem(source)

        # 设置到表格
        self.pie_compounds_list.setItem(row, 0, checkbox)
        self.pie_compounds_list.setItem(row, 1, weight_item)
        self.pie_compounds_list.setItem(row, 2, name_item)
        self.pie_compounds_list.setItem(row, 3, formula_item)
        self.pie_compounds_list.setItem(row, 4, mass_item)
        self.pie_compounds_list.setItem(row, 5, ie_item)
        self.pie_compounds_list.setItem(row, 6, source_item)

        # 存储化合物数据
        compound['weight'] = coefficient
        name_item.setData(Qt.ItemDataRole.UserRole, compound)

        # 设置编辑权限
        for col in [0, 2, 3, 4, 5, 6]:
            item = self.pie_compounds_list.item(row, col)
            if item:
                item.setFlags(item.flags() & ~Qt.ItemFlag.ItemIsEditable)

    def init_ui(self):
        central_widget = QWidget()
        self.setCentralWidget(central_widget)

        main_layout = QVBoxLayout(central_widget)

        # 创建顶级标签页控件
        self.main_tab_widget = QTabWidget()
        main_layout.addWidget(self.main_tab_widget, 1)  # 占据大部分空间

        # 创建两个子页面
        self.pie_tab = QWidget()
        self.ie_tab = QWidget()

        # 添加子页面到顶级标签页
        self.main_tab_widget.addTab(self.pie_tab, "PIE查询")
        self.main_tab_widget.addTab(self.ie_tab, "电离能查询")

        # 设置PIE查询页面的布局
        pie_layout = QVBoxLayout(self.pie_tab)

        # 创建分割器
        pie_splitter = QSplitter(Qt.Orientation.Horizontal)
        pie_layout.addWidget(pie_splitter, 1)  # 占据大部分空间

        # 左侧布局 - 化合物列表和控制区域
        pie_left_widget = QWidget()
        pie_left_layout = QVBoxLayout(pie_left_widget)
        pie_left_layout.setContentsMargins(0, 0, 0, 0)

        # 右侧布局 - 图表区域
        pie_right_widget = QWidget()
        pie_right_layout = QVBoxLayout(pie_right_widget)
        pie_right_layout.setContentsMargins(0, 0, 0, 0)

        # 添加左右部分到分割器
        pie_splitter.addWidget(pie_left_widget)
        pie_splitter.addWidget(pie_right_widget)
        pie_splitter.setSizes([700, 300])  # 设置初始大小为7:3

        # 左侧 - 控制区域
        # 质量数和容差设置
        mass_group = QGroupBox("质量数设置")
        mass_layout = QGridLayout()

        # 质量数标签和输入框
        mass_label = QLabel("质量数:")
        mass_label.setFont(QFont('Arial', 10))
        mass_layout.addWidget(mass_label, 0, 0)

        self.pie_mass_input = QLineEdit("0")
        self.pie_mass_input.setMinimumWidth(100)
        self.pie_mass_input.setFixedHeight(30)
        self.pie_mass_input.setFont(QFont('Arial', 10))
        mass_layout.addWidget(self.pie_mass_input, 0, 1)

        # 容差标签和输入框
        tolerance_label = QLabel("容差:")
        tolerance_label.setFont(QFont('Arial', 10))
        mass_layout.addWidget(tolerance_label, 0, 2)

        self.pie_tolerance_input = QLineEdit("0.5")
        self.pie_tolerance_input.setMinimumWidth(80)
        self.pie_tolerance_input.setFixedHeight(30)
        self.pie_tolerance_input.setFont(QFont('Arial', 10))
        mass_layout.addWidget(self.pie_tolerance_input, 0, 3)

        # 提示标签
        tip_label = QLabel("提示: 在主窗口选择峰后，质量数会自动更新")
        tip_label.setStyleSheet("color: blue; font-style: italic;")
        mass_layout.addWidget(tip_label, 1, 0, 1, 4)

        mass_group.setLayout(mass_layout)
        pie_left_layout.addWidget(mass_group)

        # 化合物列表
        compounds_group = QGroupBox("符合质量数的化合物")
        compounds_layout = QVBoxLayout()

        self.pie_compounds_list = QTableWidget(0, 7)
        self.pie_compounds_list.setHorizontalHeaderLabels(["参与拟合", "拟合系数", "化合物名称", "分子式", "质量数", "电离能", "来源"])

        # 设置列宽
        header = self.pie_compounds_list.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.ResizeMode.ResizeToContents)  # 勾选框列
        header.setSectionResizeMode(1, QHeaderView.ResizeMode.ResizeToContents)  # 权重系数列
        header.setSectionResizeMode(2, QHeaderView.ResizeMode.Stretch)  # 化合物名称列
        header.setSectionResizeMode(3, QHeaderView.ResizeMode.Stretch)  # 分子式列
        header.setSectionResizeMode(4, QHeaderView.ResizeMode.ResizeToContents)  # 质量数列
        header.setSectionResizeMode(5, QHeaderView.ResizeMode.ResizeToContents)  # 电离能列
        header.setSectionResizeMode(6, QHeaderView.ResizeMode.ResizeToContents)  # 来源列

        # 允许编辑权重系数列
        self.pie_compounds_list.setEditTriggers(QTableWidget.EditTrigger.DoubleClicked | QTableWidget.EditTrigger.EditKeyPressed)
        self.pie_compounds_list.setAlternatingRowColors(True)
        self.pie_compounds_list.setSelectionBehavior(QTableWidget.SelectionBehavior.SelectRows)
        self.pie_compounds_list.setSelectionMode(QTableWidget.SelectionMode.SingleSelection)
        self.pie_compounds_list.itemSelectionChanged.connect(self.on_pie_compound_selected)
        self.pie_compounds_list.cellChanged.connect(self.on_pie_compound_cell_changed)
        compounds_layout.addWidget(self.pie_compounds_list)

        compounds_group.setLayout(compounds_layout)
        pie_left_layout.addWidget(compounds_group)

        # 分析设置
        analysis_group = QGroupBox("分析设置")
        analysis_layout = QGridLayout()

        # 最大化合物数
        max_compounds_label = QLabel("最大化合物数:")
        max_compounds_label.setFont(QFont('Arial', 10))
        analysis_layout.addWidget(max_compounds_label, 0, 0)

        self.max_compounds_input = QSpinBox()
        self.max_compounds_input.setRange(1, 10)
        self.max_compounds_input.setValue(5)
        self.max_compounds_input.setMinimumWidth(60)
        self.max_compounds_input.setFixedHeight(30)
        analysis_layout.addWidget(self.max_compounds_input, 0, 1)

        # 最大方案数
        max_solutions_label = QLabel("最大方案数:")
        max_solutions_label.setFont(QFont('Arial', 10))
        analysis_layout.addWidget(max_solutions_label, 0, 2)

        self.max_solutions_input = QSpinBox()
        self.max_solutions_input.setRange(1, 5)
        self.max_solutions_input.setValue(3)
        self.max_solutions_input.setMinimumWidth(60)
        self.max_solutions_input.setFixedHeight(30)
        analysis_layout.addWidget(self.max_solutions_input, 0, 3)

        # 插值窗口大小
        window_size_label = QLabel("插值窗口大小:")
        window_size_label.setFont(QFont('Arial', 10))
        analysis_layout.addWidget(window_size_label, 1, 0)

        self.window_size_input = QSpinBox()
        self.window_size_input.setRange(1, 9)
        self.window_size_input.setValue(3)
        self.window_size_input.setSingleStep(2)  # 只允许奇数
        self.window_size_input.setMinimumWidth(60)
        self.window_size_input.setFixedHeight(30)
        analysis_layout.addWidget(self.window_size_input, 1, 1)

        # 拟合方案选择
        fitting_method_label = QLabel("拟合方案:")
        fitting_method_label.setFont(QFont('Arial', 10))
        analysis_layout.addWidget(fitting_method_label, 1, 2)

        self.fitting_method_combo = QComboBox()
        self.fitting_method_combo.addItem("非负最小二乘法 (NNLS)")
        self.fitting_method_combo.addItem("LASSO回归 (L1正则化)")
        self.fitting_method_combo.addItem("岭回归 (L2正则化)")
        self.fitting_method_combo.setMinimumWidth(150)
        self.fitting_method_combo.setFixedHeight(30)
        analysis_layout.addWidget(self.fitting_method_combo, 1, 3)

        # 能量区间权重设置
        weight_settings_label = QLabel("拟合目标区间权重设置:")
        weight_settings_label.setFont(QFont('Arial', 10, QFont.Weight.Bold))
        analysis_layout.addWidget(weight_settings_label, 2, 0, 1, 6)

        # 添加说明标签
        weight_explanation = QLabel("注：这里设置的是拟合过程中不同能量区间的重要性权重，而非化合物系数")
        weight_explanation.setStyleSheet("color: blue; font-style: italic;")
        analysis_layout.addWidget(weight_explanation, 3, 0, 1, 6)

        # 第一个能量区间
        energy_range1_label = QLabel("区间1:")
        energy_range1_label.setFont(QFont('Arial', 10))
        analysis_layout.addWidget(energy_range1_label, 4, 0)

        self.energy_range1_min = QDoubleSpinBox()
        self.energy_range1_min.setRange(0, 20)
        self.energy_range1_min.setValue(9.7)
        self.energy_range1_min.setDecimals(1)
        self.energy_range1_min.setSingleStep(0.1)
        self.energy_range1_min.setMinimumWidth(60)
        analysis_layout.addWidget(self.energy_range1_min, 4, 1)

        energy_range1_to_label = QLabel("至")
        analysis_layout.addWidget(energy_range1_to_label, 4, 2)

        self.energy_range1_max = QDoubleSpinBox()
        self.energy_range1_max.setRange(0, 20)
        self.energy_range1_max.setValue(10.5)
        self.energy_range1_max.setDecimals(1)
        self.energy_range1_max.setSingleStep(0.1)
        self.energy_range1_max.setMinimumWidth(60)
        analysis_layout.addWidget(self.energy_range1_max, 4, 3)

        weight1_label = QLabel("重要性:")
        analysis_layout.addWidget(weight1_label, 4, 4)

        self.weight1_input = QSpinBox()
        self.weight1_input.setRange(1, 10)
        self.weight1_input.setValue(1)
        self.weight1_input.setMinimumWidth(40)
        analysis_layout.addWidget(self.weight1_input, 4, 5)

        # 第二个能量区间
        energy_range2_label = QLabel("区间2:")
        energy_range2_label.setFont(QFont('Arial', 10))
        analysis_layout.addWidget(energy_range2_label, 5, 0)

        self.energy_range2_min = QDoubleSpinBox()
        self.energy_range2_min.setRange(0, 20)
        self.energy_range2_min.setValue(10.5)
        self.energy_range2_min.setDecimals(1)
        self.energy_range2_min.setSingleStep(0.1)
        self.energy_range2_min.setMinimumWidth(60)
        analysis_layout.addWidget(self.energy_range2_min, 5, 1)

        energy_range2_to_label = QLabel("至")
        analysis_layout.addWidget(energy_range2_to_label, 5, 2)

        self.energy_range2_max = QDoubleSpinBox()
        self.energy_range2_max.setRange(0, 20)
        self.energy_range2_max.setValue(11.5)
        self.energy_range2_max.setDecimals(1)
        self.energy_range2_max.setSingleStep(0.1)
        self.energy_range2_max.setMinimumWidth(60)
        analysis_layout.addWidget(self.energy_range2_max, 5, 3)

        weight2_label = QLabel("重要性:")
        analysis_layout.addWidget(weight2_label, 5, 4)

        self.weight2_input = QSpinBox()
        self.weight2_input.setRange(1, 10)
        self.weight2_input.setValue(2)
        self.weight2_input.setMinimumWidth(40)
        analysis_layout.addWidget(self.weight2_input, 5, 5)

        # 第三个能量区间
        energy_range3_label = QLabel("区间3:")
        energy_range3_label.setFont(QFont('Arial', 10))
        analysis_layout.addWidget(energy_range3_label, 6, 0)

        self.energy_range3_min = QDoubleSpinBox()
        self.energy_range3_min.setRange(0, 20)
        self.energy_range3_min.setValue(11.5)
        self.energy_range3_min.setDecimals(1)
        self.energy_range3_min.setSingleStep(0.1)
        self.energy_range3_min.setMinimumWidth(60)
        analysis_layout.addWidget(self.energy_range3_min, 6, 1)

        energy_range3_to_label = QLabel("至")
        analysis_layout.addWidget(energy_range3_to_label, 6, 2)

        self.energy_range3_max = QDoubleSpinBox()
        self.energy_range3_max.setRange(0, 20)
        self.energy_range3_max.setValue(11.8)
        self.energy_range3_max.setDecimals(1)
        self.energy_range3_max.setSingleStep(0.1)
        self.energy_range3_max.setMinimumWidth(60)
        analysis_layout.addWidget(self.energy_range3_max, 6, 3)

        weight3_label = QLabel("重要性:")
        analysis_layout.addWidget(weight3_label, 6, 4)

        self.weight3_input = QSpinBox()
        self.weight3_input.setRange(1, 10)
        self.weight3_input.setValue(1)
        self.weight3_input.setMinimumWidth(40)
        analysis_layout.addWidget(self.weight3_input, 6, 5)

        # 坐标轴保持开关
        self.keep_axis_range_checkbox = QCheckBox("保持原始坐标轴范围")
        self.keep_axis_range_checkbox.setChecked(True)  # 默认开启
        self.keep_axis_range_checkbox.setToolTip("开启时，绘制PIE曲线后保持实验数据的原始坐标轴范围")
        analysis_layout.addWidget(self.keep_axis_range_checkbox, 7, 0, 1, 6)

        # 方案选择
        analysis_layout.addWidget(QLabel("拟合方案:"), 8, 0)
        self.solution_combo = QComboBox()
        self.solution_combo.setMinimumWidth(200)
        self.solution_combo.setFixedHeight(30)
        self.solution_combo.currentIndexChanged.connect(self.on_solution_changed)
        analysis_layout.addWidget(self.solution_combo, 8, 1, 1, 2)

        # 刷新方案按钮
        self.refresh_solutions_button = QPushButton("刷新方案")
        self.refresh_solutions_button.clicked.connect(self.refresh_solutions)
        self.refresh_solutions_button.setFixedHeight(30)
        analysis_layout.addWidget(self.refresh_solutions_button, 8, 3)

        # 分析按钮
        self.analyze_button = QPushButton("拟合PIE曲线")
        self.analyze_button.clicked.connect(self.analyze_pie_curve)
        self.analyze_button.setMinimumHeight(40)
        self.analyze_button.setFont(QFont('Arial', 10, QFont.Weight.Bold))
        analysis_layout.addWidget(self.analyze_button, 9, 0, 1, 6)

        analysis_group.setLayout(analysis_layout)
        pie_left_layout.addWidget(analysis_group)

        # 右侧 - 电离截面数据区域
        # 创建电离截面数据表格
        pie_data_group = QGroupBox("电离截面数据")
        pie_data_layout = QVBoxLayout(pie_data_group)

        # 创建电离截面数据表格
        self.pie_data_table = QTableWidget(0, 2)
        self.pie_data_table.setHorizontalHeaderLabels(["能量 (eV)", "电离截面 (Å²)"])
        self.pie_data_table.horizontalHeader().setSectionResizeMode(QHeaderView.ResizeMode.Stretch)
        self.pie_data_table.setEditTriggers(QTableWidget.EditTrigger.NoEditTriggers)
        self.pie_data_table.setAlternatingRowColors(True)
        pie_data_layout.addWidget(self.pie_data_table)

        pie_right_layout.addWidget(pie_data_group)

        # 添加结果文本区域
        self.pie_result_text = QTextEdit()
        self.pie_result_text.setReadOnly(True)
        self.pie_result_text.setMaximumHeight(150)
        pie_right_layout.addWidget(self.pie_result_text)

        # 初始化PIE分析相关变量
        self.pie_compounds_data = []
        self.current_solutions = []  # 存储当前的拟合方案列表
        self.all_matching_compounds = []  # 存储所有符合质量数的化合物

        # 加载化合物数据库
        print("初始化时加载化合物数据库...")
        self.load_pie_compounds_data()

        # 检查是否成功加载
        if not self.pie_compounds_data:
            self.log_print("警告: 初始化时未能加载化合物数据库", level=1)
        else:
            self.log_print(f"初始化时成功加载化合物数据库，共 {len(self.pie_compounds_data)} 个化合物", level=3)

        # 设置电离能查询页面的布局
        ie_layout = QVBoxLayout(self.ie_tab)

        # 创建分割器
        splitter = QSplitter(Qt.Orientation.Horizontal)
        ie_layout.addWidget(splitter, 1)  # 占据大部分空间

        # 左侧布局 - 结果区域
        left_widget = QWidget()
        left_layout = QVBoxLayout(left_widget)
        left_layout.setContentsMargins(0, 0, 0, 0)

        # 右侧布局 - 标签页和化合物信息
        right_widget = QWidget()
        right_layout = QVBoxLayout(right_widget)
        right_layout.setContentsMargins(0, 0, 0, 0)

        # 添加左右部分到分割器
        splitter.addWidget(left_widget)
        splitter.addWidget(right_widget)
        splitter.setSizes([500, 500])  # 设置初始大小

        # 创建标签页
        self.tab_widget = QTabWidget()
        right_layout.addWidget(self.tab_widget)

        # 创建标签页内容
        self.create_input_tab()
        self.create_element_limits_tab()
        self.create_compound_info_tab()
        self.create_psi4_calculation_tab()

        # 按钮区域
        button_layout = QHBoxLayout()

        # 计算按钮
        self.calculate_button = QPushButton("计算")
        self.calculate_button.clicked.connect(self.calculate)
        self.calculate_button.setMinimumHeight(40)  # 增加按钮高度
        self.calculate_button.setFont(QFont('Arial', 10, QFont.Weight.Bold))  # 设置字体
        button_layout.addWidget(self.calculate_button)

        # 另存为按钮
        self.save_button = QPushButton("另存为")
        self.save_button.clicked.connect(self.save_results)
        self.save_button.setMinimumHeight(40)  # 增加按钮高度
        button_layout.addWidget(self.save_button)

        # 复制按钮
        self.copy_button = QPushButton("复制")
        self.copy_button.clicked.connect(self.copy_results)
        self.copy_button.setMinimumHeight(40)  # 增加按钮高度
        button_layout.addWidget(self.copy_button)

        # 清除所有电离能按钮
        self.clear_ie_button = QPushButton("清除所有电离能")
        self.clear_ie_button.clicked.connect(self.clear_all_ionization_energies)
        self.clear_ie_button.setMinimumHeight(40)  # 增加按钮高度
        button_layout.addWidget(self.clear_ie_button)

        main_layout.addLayout(button_layout)  # 添加到主布局底部

        # 初始化化合物信息相关变量
        self.db = NISTDatabase()
        self.compounds = []
        self.current_compound = None
        
        # 初始化PSI4计算相关变量
        self.calculation_thread = None

        # 在左侧添加结果表格
        # 结果表格
        self.result_table = QTableWidget()
        self.result_table.setColumnCount(5)  # 增加一列显示是否为自由基
        self.result_table.setHorizontalHeaderLabels(["分子式", "环+双键数", "计算质量", "偏差 mmu", "自由基"])
        self.result_table.setEditTriggers(QTableWidget.EditTrigger.NoEditTriggers)
        self.result_table.setAlternatingRowColors(True)  # 交替行颜色
        self.result_table.setSelectionBehavior(QTableWidget.SelectionBehavior.SelectRows)  # 选择整行
        self.result_table.setSelectionMode(QTableWidget.SelectionMode.SingleSelection)  # 单选
        # 调整列宽，使各列信息更清晰
        header = self.result_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.ResizeMode.Stretch)  # 分子式列拉伸
        header.setSectionResizeMode(1, QHeaderView.ResizeMode.ResizeToContents)  # 环+双键数
        header.setSectionResizeMode(2, QHeaderView.ResizeMode.ResizeToContents)  # 计算质量
        header.setSectionResizeMode(3, QHeaderView.ResizeMode.ResizeToContents)  # 偏差
        header.setSectionResizeMode(4, QHeaderView.ResizeMode.ResizeToContents)  # 自由基
        self.result_table.horizontalHeader().setFont(QFont('Arial', 10, QFont.Weight.Bold))  # 设置表头字体
        self.result_table.verticalHeader().setVisible(False)  # 隐藏垂直表头
        self.result_table.setMinimumHeight(300)  # 设置最小高度

        # 设置结果表格的右键菜单策略
        self.result_table.setContextMenuPolicy(Qt.ContextMenuPolicy.CustomContextMenu)
        self.result_table.customContextMenuRequested.connect(self.show_formula_context_menu)

        # 添加单击事件
        self.result_table.cellClicked.connect(self.on_formula_clicked)

        # 添加提示信息
        formula_tip = QLabel("提示: 点击分子式可查询NIST数据库中的化合物信息")
        formula_tip.setStyleSheet("color: blue;")

        left_layout.addWidget(self.result_table)
        left_layout.addWidget(formula_tip)

    def calculate(self):
        # 首先检查当前选中的主标签页
        current_main_tab_index = self.main_tab_widget.currentIndex()

        # 如果当前是PIE查询标签页
        if current_main_tab_index == 0:  # PIE查询标签页
            # 尝试从主窗口获取质量数
            try:
                # 先尝试从主窗口获取质量数
                self.get_mass_from_results()
            except Exception as e:
                print(f"从主窗口获取质量数时出错: {str(e)}")
                # 如果获取失败，使用当前输入框中的质量数
                pass

            # 根据输入的质量数查找符合条件的化合物
            self.find_compounds_by_mass()
            return

        # 如果当前是电离能查询标签页
        elif current_main_tab_index == 1:  # 电离能查询标签页
            try:
                target_mass = float(self.mass_input.text())
                tolerance = float(self.tolerance_input.text()) / 1000  # 转换为质量单位
                # 读取电荷值，目前未使用
                _ = int(self.charge_input.text())  # 使用下划线表示未使用的变量

                # 获取元素限制
                element_constraints = {}
                for element, (min_spin, max_spin) in self.element_limits.items():
                    min_val = min_spin.value()
                    max_val = max_spin.value()
                    if max_val > 0:
                        element_constraints[element] = (min_val, max_val)

                # 生成可能的分子式
                formulas = self.generate_formulas(element_constraints, target_mass, tolerance)

                # 更新表格
                self.result_table.setRowCount(len(formulas))
                for i, (formula, rdb, calc_mass, deviation, is_radical) in enumerate(formulas):
                    self.result_table.setItem(i, 0, QTableWidgetItem(formula))
                    self.result_table.setItem(i, 1, QTableWidgetItem(f"{rdb:.1f}"))
                    self.result_table.setItem(i, 2, QTableWidgetItem(f"{calc_mass:.4f}"))
                    self.result_table.setItem(i, 3, QTableWidgetItem(f"{deviation:.3f}"))
                    self.result_table.setItem(i, 4, QTableWidgetItem("是" if is_radical else "否"))

                # 更新统计信息
                self.searched_label.setText(str(self.total_combinations))
                self.hits_label.setText(str(len(formulas)))

                # 自动切换到电离能查询标签页
                self.main_tab_widget.setCurrentIndex(1)

            except Exception as e:
                QMessageBox.critical(self, "错误", f"计算过程中出错: {str(e)}")

    def generate_formulas(self, element_constraints, target_mass, tolerance):
        formulas = []
        self.total_combinations = 0

        # 生成所有可能的元素组合
        elements = list(element_constraints.keys())
        element_ranges = []

        for element in elements:
            min_val, max_val = element_constraints[element]
            element_ranges.append(range(min_val, max_val + 1))

        # 计算总组合数
        total = 1
        for r in element_ranges:
            total *= len(r)
        self.total_combinations = total

        # 如果组合数太大，提示用户
        if total > 1000000:
            reply = QMessageBox.question(self, "警告",
                                        f"可能的组合数量非常大 ({total})，计算可能需要很长时间。是否继续？",
                                        QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No)
            if reply == QMessageBox.StandardButton.No:
                return []

        # 定义元素最大成键数
        max_bonds = {
            'C': 4, 'H': 1, 'N': 3, 'O': 2, 'F': 1, 'Na': 1, 'Si': 4,
            'P': 5, 'S': 6, 'Cl': 1, 'Br': 1
        }

        # 生成所有组合
        import itertools
        for counts in itertools.product(*element_ranges):
            # 计算分子式质量
            mass = 0
            formula_dict = {}
            for i, count in enumerate(counts):
                element = elements[i]
                if count > 0:
                    formula_dict[element] = count
                    mass += self.elements[element] * count

            # 检查质量是否在容差范围内
            if abs(mass - target_mass) <= tolerance:
                # 验证成键数限制
                if not self.validate_bonds(formula_dict, max_bonds):
                    continue

                # 判断是否为自由基
                is_radical = self.is_radical(formula_dict)

                # 计算RDB值 (环加双键)
                rdb = self.calculate_rdb(formula_dict, is_radical)

                # 生成分子式字符串
                formula_str = ""
                for element in sorted(formula_dict.keys(), key=lambda e: (e != 'C', e != 'H', e)):
                    count = formula_dict[element]
                    formula_str += element
                    if count > 1:
                        formula_str += str(count)

                # 计算偏差（以mmu为单位）
                deviation = (mass - target_mass) * 1000

                formulas.append((formula_str, rdb, mass, deviation, is_radical))

        # 按偏差排序
        formulas.sort(key=lambda x: abs(x[3]))

        return formulas

    def validate_bonds(self, formula_dict, max_bonds):
        """验证分子式是否满足成键数限制"""
        # 不验证价电子数的奇偶性，允许自由基存在

        # 检查是否有足够的价电子形成所需的键
        # 每个键需要2个电子
        min_electrons_needed = 0
        for element, count in formula_dict.items():
            if element != 'H':  # 非氢原子至少需要一个键
                min_electrons_needed += count * 2

        # 氢原子总是需要一个键
        h_count = formula_dict.get('H', 0)

        # 检查氢原子数是否超过了其他原子可以提供的最大成键数
        non_h_bonds = 0
        for element, count in formula_dict.items():
            if element != 'H' and element in max_bonds:
                non_h_bonds += count * max_bonds[element]

        if h_count > non_h_bonds:
            return False

        return True

    def is_radical(self, formula_dict):
        """判断分子式是否为自由基"""
        # 计算总价电子数
        valence_electrons = 0
        for element, count in formula_dict.items():
            if element == 'H':
                valence_electrons += count * 1
            elif element in ['C', 'Si']:
                valence_electrons += count * 4
            elif element == 'N':
                valence_electrons += count * 5
            elif element == 'O':
                valence_electrons += count * 6
            elif element in ['F', 'Cl', 'Br']:
                valence_electrons += count * 7
            elif element == 'Na':
                valence_electrons += count * 1
            elif element == 'P':
                valence_electrons += count * 5
            elif element == 'S':
                valence_electrons += count * 6

        # 如果价电子数为奇数，则为自由基
        return valence_electrons % 2 != 0

    def calculate_rdb(self, formula_dict, is_radical=False):
        # 计算环加双键数 (RDB)
        # RDB = 1 + 0.5 * (2*C + N - H - X)
        # 其中X是卤素数量
        # 如果是自由基，RDB值减去0.5
        c = formula_dict.get('C', 0)
        h = formula_dict.get('H', 0)
        n = formula_dict.get('N', 0)
        f = formula_dict.get('F', 0)
        cl = formula_dict.get('Cl', 0)
        br = formula_dict.get('Br', 0)

        rdb = 1 + 0.5 * (2*c + n - h - f - cl - br)

        # 如果是自由基，RDB值减去0.5
        if is_radical:
            rdb -= 0.5

        return rdb

    # 移除打印功能

    def save_results(self):
        # 首先检查当前选中的主标签页
        current_main_tab_index = self.main_tab_widget.currentIndex()

        # 如果当前是PIE查询标签页
        if current_main_tab_index == 0:  # PIE查询标签页
            # 暂时没有内容可保存
            QMessageBox.information(self, "提示", "PIE查询功能尚未实现，暂无内容可保存")
            return

        # 如果当前是电离能查询标签页
        elif current_main_tab_index == 1:  # 电离能查询标签页
            # 检查当前选中的子标签页
            current_tab_index = self.tab_widget.currentIndex()

            # 如果当前是化合物信息标签页，保存选中的化合物信息
            if current_tab_index == 2:  # 化合物信息标签页索引
                # 获取当前选中的化合物项
                current_item = self.compounds_list.currentItem()
                if not current_item:
                    QMessageBox.warning(self, "警告", "请先选择一个化合物")
                    return

                # 获取化合物ID
                compound_id = current_item.data(Qt.ItemDataRole.UserRole)
                if not compound_id:
                    QMessageBox.warning(self, "警告", "无法获取化合物ID")
                    return

                # 查找化合物详细信息
                compound = None
                for c in self.compounds:
                    if c.get('id', '') == compound_id:
                        compound = c
                        break

                if not compound:
                    QMessageBox.warning(self, "警告", "无法获取化合物信息")
                    return

                # 保存化合物信息到文件
                filename, _ = QFileDialog.getSaveFileName(self, "保存化合物信息", "", "文本文件 (*.txt);;所有文件 (*)")
                if filename:
                    try:
                        with open(filename, 'w') as f:
                            # 提取化合物信息
                            name = compound.get('name', '')
                            formula = compound.get('formula', '')
                            cas_rn = compound.get('cas_rn', '')
                            mol_weight = compound.get('mol_weight', '')
                            inchi = compound.get('inchi', '')
                            smiles = compound.get('smiles', '')

                            # 写入基本信息
                            f.write(f"名称: {name}\n")
                            f.write(f"分子式: {formula}\n")
                            f.write(f"CAS号: {cas_rn if cas_rn else '无'}\n")
                            f.write(f"分子量: {mol_weight}\n")
                            f.write(f"InChI: {inchi if inchi else '无'}\n")
                            f.write(f"SMILES: {smiles if smiles else '无'}\n\n")

                            # 写入电离能信息
                            ion_energetics = compound.get('ion_energetics', None)
                            if ion_energetics:
                                ie_values = ion_energetics.get('ie_values', [])
                                methods = ion_energetics.get('methods', [])
                                references = ion_energetics.get('references', [])
                                comments = ion_energetics.get('comments', [])

                                f.write("电离能信息:\n")
                                f.write("电离能 (eV)\t方法\t来源\t备注\n")

                                max_rows = max(
                                    len(ie_values) if ie_values else 0,
                                    len(methods) if methods else 0,
                                    len(references) if references else 0,
                                    len(comments) if comments else 0
                                )

                                for row in range(max_rows):
                                    ie_value = ie_values[row] if ie_values and row < len(ie_values) else ""
                                    method = methods[row] if methods and row < len(methods) else ""
                                    reference = references[row] if references and row < len(references) else ""
                                    comment = comments[row] if comments and row < len(comments) else ""

                                    f.write(f"{ie_value}\t{method}\t{reference}\t{comment}\n")

                        QMessageBox.information(self, "保存成功", f"化合物信息已保存到 {filename}")
                    except Exception as e:
                        QMessageBox.critical(self, "保存失败", f"保存化合物信息时出错: {str(e)}")
            else:
                # 否则，保存结果表格中的所有行
                filename, _ = QFileDialog.getSaveFileName(self, "保存结果", "", "文本文件 (*.txt);;所有文件 (*)")
                if filename:
                    try:
                        with open(filename, 'w') as f:
                            f.write(f"测量质量: {self.mass_input.text()}\n")
                            f.write(f"公差 (mmu): {self.tolerance_input.text()}\n")
                            f.write(f"分子电荷: {self.charge_input.text()}\n\n")

                            f.write("分子式\tRDB\t计算质量\t偏差 (mmu)\t自由基\n")
                            for row in range(self.result_table.rowCount()):
                                formula = self.result_table.item(row, 0).text()
                                rdb = self.result_table.item(row, 1).text()
                                mass = self.result_table.item(row, 2).text()
                                deviation = self.result_table.item(row, 3).text()
                                radical = self.result_table.item(row, 4).text()
                                f.write(f"{formula}\t{rdb}\t{mass}\t{deviation}\t{radical}\n")

                        QMessageBox.information(self, "保存成功", f"结果已保存到 {filename}")
                    except Exception as e:
                        QMessageBox.critical(self, "保存失败", f"保存结果时出错: {str(e)}")

    def copy_results(self):
        # 首先检查当前选中的主标签页
        current_main_tab_index = self.main_tab_widget.currentIndex()

        # 如果当前是PIE查询标签页
        if current_main_tab_index == 0:  # PIE查询标签页
            # 复制当前选中的化合物信息
            selected_items = self.pie_compounds_list.selectedItems()
            if not selected_items:
                QMessageBox.warning(self, "警告", "请先选择一个化合物")
                return

            # 获取化合物数据
            row = selected_items[0].row()
            compound_item = self.pie_compounds_list.item(row, 0)
            if not compound_item:
                QMessageBox.warning(self, "警告", "无法获取化合物数据")
                return

            compound = compound_item.data(Qt.ItemDataRole.UserRole)
            if not compound:
                QMessageBox.warning(self, "警告", "无法获取化合物数据")
                return

            # 提取化合物信息
            name = compound.get('name', '')
            formula = compound.get('formula', '')
            mass = compound.get('mol_weight', 0)
            cas_rn = compound.get('cas_rn', '')

            # 构建复制文本
            copy_text = f"{name} ({formula})"
            if cas_rn:
                copy_text += f" [CAS: {cas_rn}]"
            copy_text += f" 分子量: {mass:.4f}"

            # 复制到剪贴板
            clipboard = QApplication.clipboard()
            clipboard.setText(copy_text)

            # 显示成功消息
            QMessageBox.information(self, "复制成功", f"已复制化合物信息到剪贴板")
            print(f"已复制化合物信息: {copy_text}")
            return

        # 如果当前是电离能查询标签页
        elif current_main_tab_index == 1:  # 电离能查询标签页
            # 检查当前选中的子标签页
            current_tab_index = self.tab_widget.currentIndex()

            # 如果当前是化合物信息标签页，复制选中的化合物信息
            if current_tab_index == 2:  # 化合物信息标签页索引
                # 获取当前选中的化合物项
                current_item = self.compounds_list.currentItem()
                if not current_item:
                    QMessageBox.warning(self, "警告", "请先选择一个化合物")
                    return

                # 获取化合物ID
                compound_id = current_item.data(Qt.ItemDataRole.UserRole)
                if not compound_id:
                    QMessageBox.warning(self, "警告", "无法获取化合物ID")
                    return

                # 查找化合物详细信息
                compound = None
                for c in self.compounds:
                    if c.get('id', '') == compound_id:
                        compound = c
                        break

                if not compound:
                    QMessageBox.warning(self, "警告", "无法获取化合物信息")
                    return

                # 提取化合物信息
                name = compound.get('name', '')
                formula = compound.get('formula', '')
                cas_rn = compound.get('cas_rn', '')
                mol_weight = compound.get('mol_weight', '')

                # 构建复制文本
                copy_text = f"{name} ({formula})"
                if cas_rn:
                    copy_text += f" [CAS: {cas_rn}]"
                if mol_weight:
                    copy_text += f" 分子量: {mol_weight}"

                # 获取电离能信息
                ion_energetics = compound.get('ion_energetics', None)
                if ion_energetics:
                    ie_values = ion_energetics.get('ie_values', [])
                    if ie_values and len(ie_values) > 0 and ie_values[0]:
                        copy_text += f" 电离能: {ie_values[0]} eV"

                # 复制到剪贴板
                clipboard = QApplication.clipboard()
                clipboard.setText(copy_text)

                # 显示成功消息
                QMessageBox.information(self, "复制成功", f"已复制化合物信息到剪贴板")
                print(f"已复制化合物信息: {copy_text}")
            else:
                # 否则，复制结果表格中的所有行
                text = ""
                for row in range(self.result_table.rowCount()):
                    formula = self.result_table.item(row, 0).text()
                    rdb = self.result_table.item(row, 1).text()
                    mass = self.result_table.item(row, 2).text()
                    deviation = self.result_table.item(row, 3).text()
                    radical = self.result_table.item(row, 4).text()
                    text += f"{formula}\t{rdb}\t{mass}\t{deviation}\t{radical}\n"

                clipboard = QApplication.clipboard()
                clipboard.setText(text)
                QMessageBox.information(self, "复制成功", "结果已复制到剪贴板")

    def create_input_tab(self):
        """创建输入参数标签页"""
        input_tab = QWidget()
        input_layout = QVBoxLayout(input_tab)

        # 输入参数区域
        input_group = QGroupBox("输入参数")
        params_layout = QGridLayout()
        params_layout.setSpacing(10)  # 增加组件间的间距

        # 测量质量标签
        mass_label = QLabel("测量质量:")
        mass_label.setFont(QFont('Arial', 10))
        params_layout.addWidget(mass_label, 0, 0)

        # 测量质量输入框
        initial_mass_str = f"{self.initial_mass:.6f}" if self.initial_mass is not None else "55"
        self.mass_input = QLineEdit(initial_mass_str)
        self.mass_input.setMinimumWidth(100)  # 设置最小宽度
        self.mass_input.setFixedHeight(30)  # 设置固定高度
        self.mass_input.setFont(QFont('Arial', 10))
        params_layout.addWidget(self.mass_input, 0, 1)

        # 公差标签
        tolerance_label = QLabel("公差 (mmu):")
        tolerance_label.setFont(QFont('Arial', 10))
        params_layout.addWidget(tolerance_label, 0, 2)

        # 公差输入框
        self.tolerance_input = QLineEdit("200")
        self.tolerance_input.setMinimumWidth(80)  # 设置最小宽度
        self.tolerance_input.setFixedHeight(30)  # 设置固定高度
        self.tolerance_input.setFont(QFont('Arial', 10))
        params_layout.addWidget(self.tolerance_input, 0, 3)

        # 分子电荷标签
        charge_label = QLabel("分子电荷:")
        charge_label.setFont(QFont('Arial', 10))
        params_layout.addWidget(charge_label, 0, 4)

        # 分子电荷输入框
        self.charge_input = QLineEdit("0")
        self.charge_input.setMinimumWidth(60)  # 设置最小宽度
        self.charge_input.setFixedHeight(30)  # 设置固定高度
        self.charge_input.setFont(QFont('Arial', 10))
        params_layout.addWidget(self.charge_input, 0, 5)

        input_group.setLayout(params_layout)
        input_layout.addWidget(input_group)

        # 搜索结果统计
        stats_group = QGroupBox("搜索结果统计")
        stats_layout = QHBoxLayout(stats_group)

        search_label = QLabel("搜索数:")
        search_label.setFont(QFont('Arial', 10))
        stats_layout.addWidget(search_label)

        self.searched_label = QLabel("0")
        self.searched_label.setFont(QFont('Arial', 10, QFont.Weight.Bold))
        stats_layout.addWidget(self.searched_label)

        stats_layout.addSpacing(20)  # 添加间距

        hits_label = QLabel("命中数:")
        hits_label.setFont(QFont('Arial', 10))
        stats_layout.addWidget(hits_label)

        self.hits_label = QLabel("0")
        self.hits_label.setFont(QFont('Arial', 10, QFont.Weight.Bold))
        stats_layout.addWidget(self.hits_label)

        stats_layout.addStretch(1)  # 添加弹性空间

        input_layout.addWidget(stats_group)
        input_layout.addStretch(1)  # 添加弹性空间

        self.tab_widget.addTab(input_tab, "输入参数")

    def create_element_limits_tab(self):
        """创建元素限制标签页"""
        element_tab = QWidget()
        element_layout = QVBoxLayout(element_tab)

        # 创建滚动区域，以容纳大量元素
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_widget = QWidget()
        scroll_layout = QGridLayout(scroll_widget)
        scroll_layout.setSpacing(10)  # 增加组件间的间距

        # 添加元素限制控件
        row = 0
        col = 0
        self.element_limits = {}

        for element in self.elements:
            # 使用更大的字体和加粗显示元素符号
            element_label = QLabel(element)
            element_label.setFont(QFont('Arial', 10, QFont.Weight.Bold))
            scroll_layout.addWidget(element_label, row, col*3)

            min_spin = QSpinBox()
            min_spin.setRange(0, 100)
            min_spin.setValue(0)
            min_spin.setMinimumWidth(60)  # 设置最小宽度
            min_spin.setFixedHeight(30)  # 设置固定高度
            scroll_layout.addWidget(min_spin, row, col*3+1)

            max_spin = QSpinBox()
            max_spin.setRange(0, 100)
            max_spin.setMinimumWidth(60)  # 设置最小宽度
            max_spin.setFixedHeight(30)  # 设置固定高度
            if element == 'C':
                max_spin.setValue(30)
            elif element == 'H':
                max_spin.setValue(50)
            elif element == 'N':
                max_spin.setValue(2)
            elif element == 'O':
                max_spin.setValue(3)
            else:
                max_spin.setValue(0)
            scroll_layout.addWidget(max_spin, row, col*3+2)

            self.element_limits[element] = (min_spin, max_spin)

            col += 1
            if col > 2:  # 每行显示3个元素
                col = 0
                row += 1

        scroll_area.setWidget(scroll_widget)
        element_layout.addWidget(scroll_area)

        self.tab_widget.addTab(element_tab, "元素限制")

    def create_compound_info_tab(self):
        """创建化合物信息标签页"""
        compound_tab = QWidget()
        compound_layout = QVBoxLayout(compound_tab)

        # 化合物列表
        compounds_group = QGroupBox("化合物列表")
        compounds_layout = QVBoxLayout(compounds_group)

        self.compounds_list = QListWidget()
        self.compounds_list.setAlternatingRowColors(True)
        self.compounds_list.currentItemChanged.connect(self.on_compound_selected)
        compounds_layout.addWidget(self.compounds_list)

        compound_layout.addWidget(compounds_group)

        # 化合物详细信息
        details_group = QGroupBox("化合物详细信息")
        details_layout = QVBoxLayout(details_group)

        # 分子结构和基本信息
        info_layout = QHBoxLayout()

        # 分子结构
        self.structure_label = QLabel()
        self.structure_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.structure_label.setMinimumSize(200, 150)
        self.structure_label.setStyleSheet("background-color: white; border: 1px solid #ccc;")
        info_layout.addWidget(self.structure_label)

        # 基本信息
        basic_info_layout = QVBoxLayout()
        self.name_label = QLabel()
        self.formula_label = QLabel()
        self.cas_label = QLabel()
        self.mass_label = QLabel()
        self.inchi_label = QLabel()
        self.smiles_label = QLabel()

        # 设置样式，使文本可以换行显示
        self.inchi_label.setWordWrap(True)
        self.smiles_label.setWordWrap(True)

        basic_info_layout.addWidget(self.name_label)
        basic_info_layout.addWidget(self.formula_label)
        basic_info_layout.addWidget(self.cas_label)
        basic_info_layout.addWidget(self.mass_label)
        basic_info_layout.addWidget(self.inchi_label)
        basic_info_layout.addWidget(self.smiles_label)
        basic_info_layout.addStretch(1)

        info_layout.addLayout(basic_info_layout)
        details_layout.addLayout(info_layout)

        # 电离能信息
        self.energetics_table = QTableWidget(0, 4)
        self.energetics_table.setHorizontalHeaderLabels(["电离能 (eV)", "方法", "来源", "备注"])
        self.energetics_table.horizontalHeader().setSectionResizeMode(QHeaderView.ResizeMode.Stretch)
        self.energetics_table.setAlternatingRowColors(True)
        self.energetics_table.setEditTriggers(QTableWidget.EditTrigger.NoEditTriggers)

        # 添加右键菜单
        self.energetics_table.setContextMenuPolicy(Qt.ContextMenuPolicy.CustomContextMenu)
        self.energetics_table.customContextMenuRequested.connect(self.show_energetics_context_menu)

        # 添加单击事件
        self.energetics_table.cellClicked.connect(self.on_energetics_cell_clicked)

        # 添加双击事件
        self.energetics_table.cellDoubleClicked.connect(self.on_energetics_cell_double_clicked)

        details_layout.addWidget(self.energetics_table)

        compound_layout.addWidget(details_group)

        self.tab_widget.addTab(compound_tab, "化合物信息")

    def show_formula_context_menu(self, pos):
        """显示分子式右键菜单

        参数:
            pos: 鼠标位置
        """
        # 获取点击的单元格
        item = self.result_table.itemAt(pos)
        if not item:
            return

        # 获取行和列
        row = item.row()
        column = item.column()

        # 只处理分子式列
        if column != 0:
            return

        # 获取分子式
        formula_item = self.result_table.item(row, column)
        if not formula_item:
            return

        formula = formula_item.text()
        if not formula:
            return

        # 创建右键菜单
        menu = QMenu(self)

        # 添加菜单项
        view_action = menu.addAction(f"查看化合物信息: {formula}")
        set_formula_action = menu.addAction(f"设置为当前峰的分子式: {formula}")

        # 显示菜单并获取选择的操作
        action = menu.exec(self.result_table.mapToGlobal(pos))

        # 处理选择的操作
        if action == view_action:
            # 确保当前是电离能查询标签页
            self.main_tab_widget.setCurrentIndex(1)  # 电离能查询标签页

            # 切换到化合物信息标签页
            self.tab_widget.setCurrentIndex(2)  # 化合物信息标签页索引

            # 加载化合物信息
            self.load_compounds(formula)
        elif action == set_formula_action:
            # 设置为当前峰的分子式
            self.set_formula_to_current_peak(formula)

    def set_formula_to_current_peak(self, formula):
        """设置分子式为当前峰的分子式

        参数:
            formula: 分子式
        """
        try:
            # 获取主窗口实例
            main_window = None
            for widget in QApplication.topLevelWidgets():
                if hasattr(widget, 'windowTitle') and "质谱数据分析" in widget.windowTitle():
                    if hasattr(widget, 'results_widget') and hasattr(widget, 'peak_editor'):
                        main_window = widget
                        break

            if main_window and hasattr(main_window, 'results_widget') and hasattr(main_window, 'peak_editor'):
                # 获取当前选中的峰
                results_widget = main_window.results_widget
                selected_peak = results_widget.getSelectedPeak()

                if not selected_peak:
                    QMessageBox.warning(self, "警告", "请先在结果页面选择一个峰")
                    return

                # 获取当前选中的峰索引
                peak_index = results_widget.chartCombo.currentIndex()
                if peak_index < 0 or peak_index >= len(results_widget.peaks):
                    QMessageBox.warning(self, "警告", "无法确定当前峰的索引")
                    return

                # 获取峰编辑器
                peak_editor = main_window.peak_editor

                # 在峰编辑器中找到对应的峰
                found = False
                for row in range(peak_editor.table.rowCount()):
                    # 获取峰编辑器中的峰位置和质量数
                    position_item = peak_editor.table.item(row, 0)  # 峰位置列
                    mass_item = peak_editor.table.item(row, 1)      # 质量数列

                    if position_item and mass_item:
                        try:
                            position = float(position_item.text())
                            mass = float(mass_item.text())

                            # 检查质量数是否与选中峰的质量数相同
                            if abs(mass - selected_peak['mass']) < 0.001:  # 允许小误差
                                # 找到匹配的峰，设置分子式
                                peak_editor.table.setItem(row, 2, QTableWidgetItem(formula))
                                # 触发单元格变化事件
                                peak_editor.onCellChanged(row, 2)
                                found = True
                                break
                        except (ValueError, AttributeError):
                            continue

                if not found:
                    QMessageBox.warning(self, "警告", "在峰编辑器中找不到匹配的峰")
                    return

                # 更新结果页面中的峰信息
                results_widget.peaks[peak_index]['formula'] = formula

                # 更新同位素信息表格
                if hasattr(results_widget, 'isoInfoTable') and peak_index < results_widget.isoInfoTable.rowCount():
                    results_widget.isoInfoTable.setItem(peak_index, 0, QTableWidgetItem(formula))

                    # 重新计算同位素丰度
                    from .isotope_database import calculate_isotope_abundance
                    m1_abundance = calculate_isotope_abundance(formula, 1)
                    m2_abundance = calculate_isotope_abundance(formula, 2)

                    # 更新同位素信息表格
                    results_widget.isoInfoTable.setItem(peak_index, 2, QTableWidgetItem(f"{m1_abundance:.6f}"))
                    results_widget.isoInfoTable.setItem(peak_index, 3, QTableWidgetItem(f"{m2_abundance:.6f}"))

                # 更新图表
                results_widget.updateChart()

                QMessageBox.information(self, "成功", f"已将分子式 {formula} 设置为当前峰的分子式")
            else:
                QMessageBox.warning(self, "警告", "无法找到主窗口或结果页面")
        except Exception as e:
            QMessageBox.critical(self, "错误", f"设置分子式时出错: {str(e)}")
            import traceback
            traceback.print_exc()

    def on_formula_clicked(self, row, column):
        """当用户点击分子式时显示化合物信息

        参数:
            row: 行索引
            column: 列索引
        """
        # 只处理分子式列的点击
        if column != 0:
            return

        # 获取分子式
        formula_item = self.result_table.item(row, column)
        if not formula_item:
            return

        formula = formula_item.text()
        if not formula:
            return

        try:
            # 确保当前是电离能查询标签页
            self.main_tab_widget.setCurrentIndex(1)  # 电离能查询标签页

            # 切换到化合物信息标签页
            self.tab_widget.setCurrentIndex(2)  # 化合物信息标签页索引

            # 加载化合物信息
            self.load_compounds(formula)
        except Exception as e:
            QMessageBox.critical(self, "错误", f"显示化合物信息时出错: {str(e)}")
            import traceback
            traceback.print_exc()

    def load_compounds(self, formula):
        """加载符合分子式的化合物

        参数:
            formula: 分子式
        """
        try:
            # 清空化合物列表
            self.compounds_list.clear()
            self.compounds = []

            # 清空化合物详细信息
            self.name_label.setText("")
            self.formula_label.setText("")
            self.cas_label.setText("")
            self.mass_label.setText("")
            self.structure_label.clear()
            self.energetics_table.setRowCount(0)

            # 从JSON数据中搜索化合物
            self.compounds = self.search_by_formula_json(formula)

            if not self.compounds:
                self.compounds_list.addItem(f"未找到符合分子式 {formula} 的化合物")
                return

            # 添加化合物到列表
            for compound in self.compounds:
                name = compound.get('name', '')
                formula = compound.get('formula', '')
                cas_rn = compound.get('cas_rn', '')

                # 创建显示文本
                display_text = f"{name} ({formula})"
                if cas_rn:
                    display_text += f" [CAS: {cas_rn}]"

                # 创建列表项
                item = QListWidgetItem(display_text)
                item.setData(Qt.ItemDataRole.UserRole, compound.get('id', ''))
                self.compounds_list.addItem(item)

            # 选择第一个化合物
            if self.compounds_list.count() > 0:
                self.compounds_list.setCurrentRow(0)

        except Exception as e:
            QMessageBox.critical(self, "错误", f"加载化合物信息时出错: {str(e)}")
            import traceback
            traceback.print_exc()

    def search_by_formula_json(self, formula):
        """从JSON数据中根据分子式搜索化合物

        参数:
            formula: 分子式，如 'C6H6'

        返回:
            符合条件的化合物列表
        """
        if not hasattr(self, 'nist_compounds_data') or not self.nist_compounds_data:
            print("NIST化合物数据未加载")
            return []

        # 规范化分子式（去除空格）
        formula = formula.strip()

        # 查找精确匹配
        matching_compounds = []
        for compound in self.nist_compounds_data:
            if compound.get('formula', '') == formula:
                matching_compounds.append(compound)

        # 如果没有精确匹配，尝试不考虑元素顺序的匹配
        if not matching_compounds:
            query_elements = self._parse_formula_json(formula)
            
            for compound in self.nist_compounds_data:
                compound_formula = compound.get('formula', '')
                compound_elements = self._parse_formula_json(compound_formula)
                
                # 检查元素组成是否相同
                if query_elements == compound_elements:
                    matching_compounds.append(compound)

        return matching_compounds

    def _parse_formula_json(self, formula):
        """解析分子式，提取元素及其数量

        参数:
            formula: 分子式，如 'C6H6'

        返回:
            元素及其数量的字典，如 {'C': 6, 'H': 6}
        """
        elements = {}
        i = 0

        while i < len(formula):
            # 找到元素符号（大写字母开头，可能跟着小写字母）
            if formula[i].isupper():
                element = formula[i]
                i += 1

                # 检查是否有小写字母
                while i < len(formula) and formula[i].islower():
                    element += formula[i]
                    i += 1

                # 找到数量
                count = ''
                while i < len(formula) and formula[i].isdigit():
                    count += formula[i]
                    i += 1

                # 如果没有指定数量，默认为1
                count = int(count) if count else 1

                # 更新元素字典
                if element in elements:
                    elements[element] += count
                else:
                    elements[element] = count
            else:
                # 跳过非元素字符
                i += 1

        return elements

    def load_nist_compounds_data(self):
        """加载NIST化合物数据库（统一使用JSON文件）"""
        try:
            # NIST数据库文件路径
            nist_path = os.path.join(os.path.dirname(os.path.dirname(__file__)), "database", "nist_compounds.json")
            print(f"尝试加载NIST化合物数据库: {nist_path}")

            # 检查文件是否存在
            if os.path.exists(nist_path):
                # 加载NIST数据库
                print("开始加载NIST JSON数据...")
                with open(nist_path, 'r', encoding='utf-8') as f:
                    try:
                        self.nist_compounds_data = json.load(f)
                        print(f"NIST数据库加载成功，共 {len(self.nist_compounds_data)} 个化合物")
                    except json.JSONDecodeError as e:
                        print(f"NIST JSON解析错误: {str(e)}")
                        self.nist_compounds_data = []
            else:
                print(f"NIST数据库文件不存在: {nist_path}")
                self.nist_compounds_data = []
                
        except Exception as e:
            print(f"加载NIST化合物数据库时出错: {str(e)}")
            self.nist_compounds_data = []

    def on_compound_selected(self, current, _previous=None):
        """当选择化合物时更新详细信息

        参数:
            current: 当前选中的项
            _previous: 之前选中的项（未使用）
        """
        if not current:
            return

        # 获取化合物ID
        compound_id = current.data(Qt.ItemDataRole.UserRole)
        if not compound_id:
            return

        # 查找化合物
        compound = None
        for c in self.compounds:
            if c.get('id', '') == compound_id:
                compound = c
                break

        if not compound:
            return

        # 更新化合物详细信息
        self.update_compound_details(compound)

    def update_compound_details(self, compound):
        """更新化合物详细信息

        参数:
            compound: 化合物信息字典
        """
        # 更新基本信息
        name = compound.get('name', '')
        formula = compound.get('formula', '')
        cas_rn = compound.get('cas_rn', '')
        mol_weight = compound.get('mol_weight', 0.0)
        inchi = compound.get('inchi', '')

        # 尝试从数据库中获取SMILES信息
        # 注意：当前数据库中可能没有SMILES信息，我们可以尝试从InChI生成
        smiles = ""
        if inchi and MoleculeDrawer.is_available():
            try:
                from rdkit import Chem
                mol = Chem.MolFromInchi(inchi)
                if mol:
                    smiles = Chem.MolToSmiles(mol)
            except Exception as e:
                print(f"从 InChI 生成 SMILES 时出错: {str(e)}")

        self.name_label.setText(f"名称: {name}")
        self.formula_label.setText(f"分子式: {formula}")
        self.cas_label.setText(f"CAS号: {cas_rn if cas_rn else '无'}")
        self.mass_label.setText(f"分子量: {mol_weight:.6f}")
        self.inchi_label.setText(f"InChI: {inchi if inchi else '无'}")
        self.smiles_label.setText(f"SMILES: {smiles if smiles else '无'}")

        # 更新分子结构
        inchi = compound.get('inchi', '')
        if inchi and MoleculeDrawer.is_available():
            pixmap = MoleculeDrawer.draw_from_inchi(inchi)
            if pixmap:
                self.structure_label.setPixmap(pixmap)
            else:
                self.structure_label.setText("无法绘制分子结构")
        else:
            self.structure_label.setText("无分子结构信息")

        # 更新电离能信息
        self.update_energetics(compound)

    def update_energetics(self, compound):
        """更新电离能信息

        参数:
            compound: 化合物信息字典
        """
        # 清空表格
        self.energetics_table.setRowCount(0)

        # 获取电离能信息
        ion_energetics = compound.get('ion_energetics', None)
        if not ion_energetics:
            return

        # 获取各列数据
        ie_values = ion_energetics.get('ie_values', [])
        methods = ion_energetics.get('methods', [])
        references = ion_energetics.get('references', [])
        comments = ion_energetics.get('comments', [])

        # 确定行数
        max_rows = max(
            len(ie_values) if ie_values else 0,
            len(methods) if methods else 0,
            len(references) if references else 0,
            len(comments) if comments else 0
        )

        if max_rows == 0:
            return

        # 设置行数
        self.energetics_table.setRowCount(max_rows)

        # 填充数据
        for row in range(max_rows):
            # 电离能
            if ie_values and row < len(ie_values):
                value = ie_values[row]
                if value is not None:
                    self.energetics_table.setItem(row, 0, QTableWidgetItem(str(value)))

            # 方法
            if methods and row < len(methods):
                value = methods[row]
                if value is not None:
                    self.energetics_table.setItem(row, 1, QTableWidgetItem(str(value)))

            # 来源
            if references and row < len(references):
                value = references[row]
                if value is not None:
                    self.energetics_table.setItem(row, 2, QTableWidgetItem(str(value)))

            # 备注
            if comments and row < len(comments):
                value = comments[row]
                if value is not None:
                    self.energetics_table.setItem(row, 3, QTableWidgetItem(str(value)))

    def on_energetics_cell_double_clicked(self, row, column):
        """当用户双击电离能表格时触发

        参数:
            row: 行索引
            column: 列索引
        """
        print(f"双击电离能表格: 行={row}, 列={column}")

        # 只处理电离能列
        if column != 0:
            return

        # 获取电离能值
        ie_item = self.energetics_table.item(row, column)
        if not ie_item:
            print(f"无法获取电离能值项: 行={row}, 列={column}")
            return

        ie_text = ie_item.text()
        if not ie_text:
            print(f"电离能值为空: 行={row}, 列={column}")
            return

        try:
            print(f"处理电离能值: {ie_text}")
            # 处理电离能值，可能包含不确定度（如"8.05±0.17"）
            ie_value = self.parse_ionization_energy(ie_text)

            if ie_value is None:
                print(f"无法解析电离能值: {ie_text}")
                return

            print(f"解析后的电离能值: {ie_value}")

            # 获取化合物名称
            compound_name = ""
            current_item = self.compounds_list.currentItem()
            if current_item:
                compound_name = current_item.text()
                print(f"当前选中的化合物: {compound_name}")

            # 导入信号模块
            from .compound_dialog import compound_dialog_signals

            # 检查是否已经有这个电离能值在图表上
            # 如果已经有，则发送信号删除它，否则添加它
            # 注意：这里需要主窗口实现一个方法来检查电离能值是否已经存在
            # 这里我们先发送信号添加它
            if ie_value is not None:
                print(f"发送电离能值进行绘制: {ie_value} eV, 化合物: {compound_name}")
                compound_dialog_signals.ionization_energy_selected.emit(ie_value, compound_name)
                # 显示成功消息
                QMessageBox.information(self, "成功", f"已将电离能值 {ie_value:.2f} eV 发送到结果图表")
        except Exception as e:
            print(f"处理电离能值时出错: {str(e)}")
            import traceback
            traceback.print_exc()

    def load_pie_compounds_data(self):
        """加载PIE分析所需的化合物数据库"""
        try:
            # 数据库文件路径
            db_path = os.path.join(os.path.dirname(os.path.dirname(__file__)), "database", "compounds_data.json")
            print(f"尝试加载化合物数据库: {db_path}")

            # 检查文件是否存在
            if not os.path.exists(db_path):
                print(f"错误: 找不到化合物数据库文件: {db_path}")
                QMessageBox.critical(self, "错误", f"找不到化合物数据库文件: {db_path}")
                return

            # 检查文件大小
            file_size = os.path.getsize(db_path)
            print(f"数据库文件大小: {file_size / 1024:.2f} KB")

            # 加载数据库
            print("开始加载JSON数据...")
            with open(db_path, 'r', encoding='utf-8') as f:
                try:
                    compounds_data = json.load(f)
                    print(f"JSON数据加载成功，类型: {type(compounds_data)}")
                except json.JSONDecodeError as e:
                    print(f"JSON解析错误: {str(e)}")
                    QMessageBox.critical(self, "错误", f"JSON解析错误: {str(e)}")
                    return

            # 检查数据格式
            if isinstance(compounds_data, dict):
                print(f"数据格式为字典，包含 {len(compounds_data)} 个键")
                # 将字典转换为列表，每个元素包含ID和化合物信息
                compounds_list = []
                for compound_id, compound_info in compounds_data.items():
                    # 添加ID到化合物信息中
                    compound_info['id'] = compound_id
                    compounds_list.append(compound_info)
                compounds_data = compounds_list
                print(f"将化合物数据从字典转换为列表，共 {len(compounds_data)} 个化合物")

            # 处理每个化合物的数据
            self.pie_compounds_data = []
            print(f"开始处理 {len(compounds_data)} 个化合物数据...")

            # 打印前几个化合物的原始数据
            for i, compound in enumerate(compounds_data[:3]):
                print(f"化合物 {i+1} 原始数据:")
                for key, value in compound.items():
                    if key in ['energy', 'cross_section']:
                        print(f"  {key}: [数组，长度 {len(value)}]")
                    else:
                        print(f"  {key}: {value}")

            for i, compound in enumerate(compounds_data):
                if i % 1000 == 0:
                    print(f"正在处理第 {i+1}/{len(compounds_data)} 个化合物...")

                # 提取分子量
                try:
                    # 尝试直接获取分子量
                    mol_weight = compound.get('mol_weight')
                    if mol_weight is not None:
                        try:
                            compound['mol_weight'] = float(mol_weight)
                        except (ValueError, TypeError):
                            compound['mol_weight'] = 0
                    else:
                        # 尝试从质量数计算分子量
                        mass_number = compound.get('mass_number', '')
                        if mass_number:
                            try:
                                compound['mol_weight'] = float(mass_number)
                            except (ValueError, TypeError):
                                compound['mol_weight'] = 0
                        else:
                            # 尝试从mass字段获取
                            mass = compound.get('mass', 0)
                            if mass:
                                try:
                                    compound['mol_weight'] = float(mass)
                                except (ValueError, TypeError):
                                    compound['mol_weight'] = 0
                            else:
                                # 如果没有质量数，尝试从分子式计算
                                formula = compound.get('formula', '')
                                if formula:
                                    # 这里可以添加分子式解析和质量计算的代码
                                    # 暂时使用一个简单的估计值
                                    compound['mol_weight'] = 0
                except Exception as e:
                    print(f"计算化合物 {compound.get('id', '')} 的分子量时出错: {str(e)}")
                    compound['mol_weight'] = 0

                # 检查PIE数据
                pie_data = compound.get('pie_data')
                if pie_data:
                    # 已经有PIE数据，检查格式
                    if not isinstance(pie_data, dict):
                        print(f"化合物 {compound.get('id', '')} 的PIE数据格式错误: {type(pie_data)}")
                        compound['pie_data'] = None
                    elif 'energy' not in pie_data or 'pie' not in pie_data:
                        print(f"化合物 {compound.get('id', '')} 的PIE数据缺少能量或PIE值")
                        # 尝试从其他字段获取
                        energy = compound.get('energy')
                        cross_section = compound.get('cross_section')
                        if energy and cross_section:
                            if i < 3:  # 只打印前几个化合物的调试信息
                                print(f"从其他字段创建PIE数据: energy长度={len(energy)}, cross_section长度={len(cross_section)}")
                            compound['pie_data'] = {
                                'energy': energy,
                                'pie': cross_section
                            }
                else:
                    # 尝试从其他字段创建PIE数据
                    energy = compound.get('energy')
                    cross_section = compound.get('cross_section')
                    if energy and cross_section:
                        if i < 3:  # 只打印前几个化合物的调试信息
                            print(f"从其他字段创建PIE数据: energy长度={len(energy)}, cross_section长度={len(cross_section)}")
                        compound['pie_data'] = {
                            'energy': energy,
                            'pie': cross_section
                        }

                # 添加到列表
                self.pie_compounds_data.append(compound)

            self.log_print(f"成功加载化合物数据库，共 {len(self.pie_compounds_data)} 个化合物", level=3)

            # 打印一些调试信息
            pie_compounds = [c for c in self.pie_compounds_data if c.get('pie_data')]
            self.log_print(f"其中有 {len(pie_compounds)} 个化合物包含PIE数据", level=3)

            # 打印前5个化合物的信息
            for i, compound in enumerate(self.pie_compounds_data[:5]):
                self.log_print(f"化合物 {i+1}: {compound.get('name', '')}, 分子式: {compound.get('formula', '')}, 质量数: {compound.get('mol_weight', 0)}", level=3)
                pie_data = compound.get('pie_data')
                if pie_data:
                    energy = pie_data.get('energy', [])
                    pie = pie_data.get('pie', [])
                    self.log_print(f"  PIE数据点数: {len(energy) if energy else 0}", level=3)
                else:
                    self.log_print("  没有PIE数据", level=3)

            # 检查是否有有效的化合物数据
            valid_compounds = [c for c in self.pie_compounds_data if c.get('mol_weight', 0) > 0]
            if not valid_compounds:
                print("警告: 没有找到有效的化合物数据（所有化合物的质量数都为0）")
                # 尝试修复数据
                for compound in self.pie_compounds_data:
                    # 尝试从名称中提取质量数
                    name = compound.get('name', '')
                    if name:
                        # 尝试从名称中提取数字
                        import re
                        numbers = re.findall(r'\d+\.\d+|\d+', name)
                        if numbers:
                            try:
                                compound['mol_weight'] = float(numbers[0])
                                print(f"从名称 '{name}' 中提取质量数: {compound['mol_weight']}")
                            except (ValueError, TypeError):
                                pass

            # 再次检查是否有有效的化合物数据
            valid_compounds = [c for c in self.pie_compounds_data if c.get('mol_weight', 0) > 0]
            print(f"有效的化合物数据（质量数大于0）: {len(valid_compounds)} 个")

            return True
        except Exception as e:
            print(f"加载化合物数据库时出错: {str(e)}")
            QMessageBox.critical(self, "错误", f"加载化合物数据库时出错: {str(e)}")
            import traceback
            traceback.print_exc()
            return False

    def get_mass_from_results(self):
        """从结果页面获取当前选中峰的质量数"""
        try:
            # 找到主窗口
            main_window = None

            # 首先尝试使用类名查找
            for widget in QApplication.topLevelWidgets():
                if widget.__class__.__name__ == 'MainWindow':
                    main_window = widget
                    print(f"通过类名找到主窗口: {widget.__class__.__name__}")
                    break

            # 如果没找到，尝试使用窗口标题查找（排除当前窗口）
            if not main_window:
                for widget in QApplication.topLevelWidgets():
                    if (hasattr(widget, 'windowTitle') and
                        "质谱数据分析" in widget.windowTitle() and
                        "化合物查询" not in widget.windowTitle()):
                        main_window = widget
                        print(f"通过窗口标题找到主窗口: {widget.windowTitle()}")
                        break

            # 如果还没找到，尝试查找有tabs和results_widget属性的窗口
            if not main_window:
                for widget in QApplication.topLevelWidgets():
                    if (widget != self and  # 排除当前窗口
                        hasattr(widget, 'tabs') and
                        hasattr(widget, 'results_widget')):
                        main_window = widget
                        print(f"通过属性找到主窗口: {widget.windowTitle() if hasattr(widget, 'windowTitle') else widget.__class__.__name__}")
                        break

            # 打印所有顶级窗口的信息，用于调试
            if not main_window:
                print("无法找到主窗口，打印所有顶级窗口信息:")
                for i, widget in enumerate(QApplication.topLevelWidgets()):
                    print(f"窗口 {i+1}:")
                    print(f"  类名: {widget.__class__.__name__}")
                    print(f"  标题: {widget.windowTitle() if hasattr(widget, 'windowTitle') else 'N/A'}")
                    print(f"  有tabs属性: {hasattr(widget, 'tabs')}")
                    print(f"  有results_widget属性: {hasattr(widget, 'results_widget')}")
                    print(f"  是当前窗口: {widget == self}")

                # 尝试使用全局变量查找主窗口
                import builtins
                if hasattr(builtins, 'main_window') and builtins.main_window is not None:
                    main_window = builtins.main_window
                    print(f"通过全局变量找到主窗口: {main_window.__class__.__name__}")
                else:
                    QMessageBox.warning(self, "警告", "无法找到主窗口")
                    return

            # 检查results_widget属性
            if not hasattr(main_window, 'results_widget'):
                print(f"主窗口 {main_window.__class__.__name__} 没有results_widget属性")
                print(f"主窗口属性: {dir(main_window)}")
                QMessageBox.warning(self, "警告", "主窗口没有结果组件")
                return

            # 获取结果组件
            results_widget = main_window.results_widget

            # 检查是否有峰数据
            if not hasattr(results_widget, 'peaks') or not results_widget.peaks:
                QMessageBox.warning(self, "警告", "结果页面没有峰数据")
                return

            # 获取当前选中的峰索引
            peak_index = results_widget.chartCombo.currentIndex()
            if peak_index < 0 or peak_index >= len(results_widget.peaks):
                QMessageBox.warning(self, "警告", "未选择峰")
                return

            # 获取峰的质量数
            peak_mass = results_widget.peaks[peak_index]['mass']

            # 更新质量数输入框
            self.pie_mass_input.setText(f"{peak_mass:.4f}")

            # 查找符合质量数的化合物
            self.find_compounds_by_mass()

            # 显示成功消息
            QMessageBox.information(self, "成功", f"已获取质量数: {peak_mass:.4f}")
        except Exception as e:
            print(f"获取质量数时出错: {str(e)}")
            QMessageBox.critical(self, "错误", f"获取质量数时出错: {str(e)}")
            import traceback
            traceback.print_exc()

    def find_compounds_by_mass(self):
        """查找符合质量数的化合物"""
        try:
            # 获取质量数和容差
            try:
                target_mass = float(self.pie_mass_input.text())
                tolerance = float(self.pie_tolerance_input.text())
                print(f"查找质量数: {target_mass:.6f}, 容差: {tolerance:.6f}")
            except ValueError:
                QMessageBox.warning(self, "警告", "请输入有效的质量数和容差")
                return

            # 检查是否切换了质量数，如果是新的质量数，需要记录其实验数据坐标范围
            if (hasattr(self, 'current_mass_for_axis') and self.current_mass_for_axis is not None and
                abs(self.current_mass_for_axis - target_mass) > 0.0001):  # 质量数发生了变化
                print(f"检测到质量数从 {self.current_mass_for_axis:.4f} 切换到 {target_mass:.4f}")
                
                # 尝试从主窗口获取当前实验数据的坐标范围
                try:
                    # 查找主窗口
                    main_window = None
                    for widget in QApplication.topLevelWidgets():
                        if widget.__class__.__name__ == 'MainWindow':
                            main_window = widget
                            break
                    
                    if main_window and hasattr(main_window, 'results_widget'):
                        results_widget = main_window.results_widget
                        
                        # 获取新质量数对应的峰索引
                        if hasattr(results_widget, 'chartCombo') and hasattr(results_widget, 'peaks'):
                            peak_index = results_widget.chartCombo.currentIndex()
                            if peak_index >= 0 and peak_index < len(results_widget.peaks):
                                selected_peak = results_widget.peaks[peak_index]
                                peak_mass = selected_peak.get('mass', 0.0)
                                
                                # 如果当前选中的峰对应新的质量数，记录其实验数据坐标范围
                                if abs(peak_mass - target_mass) <= tolerance:
                                    # 获取实验数据
                                    if (hasattr(results_widget, 'results') and hasattr(results_widget, 'file_names') and 
                                        hasattr(results_widget, 'file_metadata')):
                                        
                                        # 获取X轴数据（能量）
                                        x_values = []
                                        for filename in results_widget.file_names:
                                            energy = results_widget.file_metadata.get(filename, {}).get('energy')
                                            x_values.append(energy if energy is not None else float('nan'))
                                        
                                        # 获取Y轴数据（积分强度）
                                        if isinstance(results_widget.results, dict):
                                            y_values = []
                                            for file_name in results_widget.file_names:
                                                if file_name in results_widget.results:
                                                    result_data = results_widget.results[file_name]
                                                    if peak_index < result_data.shape[0]:
                                                        y_values.append(float(result_data[peak_index, 0]))
                                                    else:
                                                        y_values.append(0.0)
                                                else:
                                                    y_values.append(0.0)
                                        else:
                                            y_values = results_widget.results[:, peak_index].tolist()
                                        
                                        # 移除NaN值
                                        valid_indices = []
                                        for i, (x, y) in enumerate(zip(x_values, y_values)):
                                            if not np.isnan(x) and not np.isnan(y):
                                                valid_indices.append(i)
                                        
                                        if valid_indices:
                                            x_exp = np.array([x_values[i] for i in valid_indices])
                                            y_exp = np.array([y_values[i] for i in valid_indices])
                                            
                                            # 记录新质量数的实验数据坐标范围（Y轴范围增加10%的边距）
                                            exp_x_range = [float(min(x_exp)), float(max(x_exp))]
                                            y_min, y_max = float(min(y_exp)), float(max(y_exp))
                                            y_margin = (y_max - y_min) * 0.1  # 10%的边距
                                            exp_y_range = [y_min - y_margin, y_max + y_margin]
                                            self.mass_axis_ranges[target_mass] = {
                                                'x_range': exp_x_range,
                                                'y_range': exp_y_range
                                            }
                                            print(f"记录新质量数 {target_mass:.4f} 的实验数据坐标范围: X={exp_x_range}, Y={exp_y_range} (Y轴已添加10%边距)")
                                        
                except Exception as e:
                    print(f"记录新质量数坐标范围时出错: {str(e)}")
                
                # 更新当前质量数
                self.current_mass_for_axis = target_mass

            # 检查是否已加载化合物数据库
            if not self.pie_compounds_data:
                print("化合物数据库未加载，尝试加载...")
                if not self.load_pie_compounds_data():
                    QMessageBox.warning(self, "警告", "无法加载化合物数据库")
                    return

            print(f"化合物数据库中有 {len(self.pie_compounds_data)} 个化合物")

            # 检查数据库中的有效化合物数量
            valid_compounds = [c for c in self.pie_compounds_data if c.get('mol_weight', 0) > 0]
            print(f"其中有 {len(valid_compounds)} 个化合物的质量数大于0")

            # 打印前几个化合物的信息
            for i, compound in enumerate(self.pie_compounds_data[:5]):
                print(f"化合物 {i+1}: {compound.get('name', '')}, 分子式: {compound.get('formula', '')}, 质量数: {compound.get('mol_weight', 0)}")

            # 清空化合物列表
            self.pie_compounds_list.setRowCount(0)

            # 创建一个简单的测试化合物列表
            test_compounds = [
                {
                    'name': '测试化合物1',
                    'formula': 'C2H6',
                    'mol_weight': 30.07,
                    'source': '测试数据'
                },
                {
                    'name': '测试化合物2',
                    'formula': 'CH4',
                    'mol_weight': 16.04,
                    'source': '测试数据'
                },
                {
                    'name': '测试化合物3',
                    'formula': 'C3H8',
                    'mol_weight': 44.1,
                    'source': '测试数据'
                },
                {
                    'name': '测试化合物4',
                    'formula': 'C4H10',
                    'mol_weight': 58.12,
                    'source': '测试数据'
                },
                {
                    'name': '测试化合物5',
                    'formula': 'C5H12',
                    'mol_weight': 72.15,
                    'source': '测试数据'
                }
            ]

            # 查找符合质量数的化合物
            matching_compounds = []

            # 首先尝试从数据库中查找
            if self.pie_compounds_data:
                for compound in self.pie_compounds_data:
                    # 获取化合物质量
                    compound_mass = compound.get('mol_weight', 0)
                    if not compound_mass:
                        continue

                    # 检查质量是否在容差范围内
                    mass_diff = abs(compound_mass - target_mass)
                    if mass_diff <= tolerance:
                        matching_compounds.append((compound, mass_diff))

            # 如果数据库中没有找到匹配的化合物，使用测试数据
            if not matching_compounds:
                print("数据库中未找到匹配的化合物，使用测试数据...")
                for compound in test_compounds:
                    # 获取化合物质量
                    compound_mass = compound.get('mol_weight', 0)

                    # 检查质量是否在容差范围内
                    mass_diff = abs(compound_mass - target_mass)
                    if mass_diff <= tolerance:
                        matching_compounds.append((compound, mass_diff))

            # 按质量数差异排序
            matching_compounds.sort(key=lambda x: x[1])

            # 提取排序后的化合物列表
            sorted_compounds = [item[0] for item in matching_compounds]

            # 打印调试信息
            print(f"查找质量数 {target_mass:.4f} (±{tolerance})，找到 {len(sorted_compounds)} 个匹配的化合物")

            # 如果没有找到匹配的化合物，打印更多调试信息
            if not sorted_compounds:
                # 找出最接近的几个化合物
                if valid_compounds:
                    closest_compounds = sorted(valid_compounds, key=lambda x: abs(x.get('mol_weight', 0) - target_mass))[:5]
                    print("数据库中最接近的化合物:")
                    for i, compound in enumerate(closest_compounds):
                        mass = compound.get('mol_weight', 0)
                        print(f"  {i+1}. {compound.get('name', '')}, 质量数: {mass:.4f}, 差异: {abs(mass - target_mass):.4f}")

                # 从测试数据中找出最接近的
                closest_test = sorted(test_compounds, key=lambda x: abs(x.get('mol_weight', 0) - target_mass))[:5]
                print("测试数据中最接近的化合物:")
                for i, compound in enumerate(closest_test):
                    mass = compound.get('mol_weight', 0)
                    print(f"  {i+1}. {compound.get('name', '')}, 质量数: {mass:.4f}, 差异: {abs(mass - target_mass):.4f}")

            matching_compounds = sorted_compounds

            # 如果没有找到匹配的化合物，尝试使用更大的容差
            if not matching_compounds:
                larger_tolerance = tolerance * 2
                print(f"使用更大的容差 {larger_tolerance:.4f} 重新查找...")

                for compound in self.pie_compounds_data:
                    compound_mass = compound.get('mol_weight', 0)
                    if not compound_mass:
                        continue

                    if abs(compound_mass - target_mass) <= larger_tolerance:
                        matching_compounds.append(compound)

                # 按质量数差异排序
                matching_compounds.sort(key=lambda x: abs(x.get('mol_weight', 0) - target_mass))

                print(f"使用容差 {larger_tolerance:.4f}，找到 {len(matching_compounds)} 个匹配的化合物")

            # 更新化合物列表
            self.pie_compounds_list.setRowCount(len(matching_compounds))

            # 打印调试信息
            print(f"更新化合物列表，共 {len(matching_compounds)} 个匹配的化合物")

            for i, compound in enumerate(matching_compounds):
                # 获取化合物信息
                name = compound.get('name', '')
                formula = compound.get('formula', '')
                mass = compound.get('mol_weight', 0)
                source = compound.get('source', '')

                # 设置默认拟合系数
                if 'weight' not in compound:
                    compound['weight'] = 0.0

                # 创建勾选框
                checkbox = QTableWidgetItem()
                # 默认碎片不参与拟合
                is_fragment = 'fragment' in name.lower() or 'fragment' in source.lower()
                checkbox.setCheckState(Qt.CheckState.Unchecked if is_fragment else Qt.CheckState.Checked)

                # 创建拟合系数输入框
                weight_item = QTableWidgetItem(str(compound['weight']))

                # 创建其他列的项目
                name_item = QTableWidgetItem(name)
                formula_item = QTableWidgetItem(formula)
                mass_item = QTableWidgetItem(f"{mass:.4f}")
                ie_item = QTableWidgetItem(self.get_first_ionization_energy(compound))
                source_item = QTableWidgetItem(source)

                # 添加到表格
                self.pie_compounds_list.setItem(i, 0, checkbox)
                self.pie_compounds_list.setItem(i, 1, weight_item)
                self.pie_compounds_list.setItem(i, 2, name_item)
                self.pie_compounds_list.setItem(i, 3, formula_item)
                self.pie_compounds_list.setItem(i, 4, mass_item)
                self.pie_compounds_list.setItem(i, 5, ie_item)
                self.pie_compounds_list.setItem(i, 6, source_item)

                # 存储化合物数据
                name_item.setData(Qt.ItemDataRole.UserRole, compound)

                # 设置权重系数列为可编辑，其他列为不可编辑
                for col in [0, 2, 3, 4, 5, 6]:
                    item = self.pie_compounds_list.item(i, col)
                    if item:
                        item.setFlags(item.flags() & ~Qt.ItemFlag.ItemIsEditable)

                # 打印前几个匹配的化合物信息
                if i < 5:
                    print(f"匹配化合物 {i+1}: {name}, 分子式: {formula}, 质量数: {mass:.4f}, 差异: {abs(mass - target_mass):.4f}")

            # 调整列宽
            self.pie_compounds_list.resizeColumnsToContents()

            # 确保表格可见
            self.pie_compounds_list.show()

            # 如果有匹配的化合物，选中第一个
            if matching_compounds:
                self.pie_compounds_list.selectRow(0)
                # 触发选中事件
                self.on_pie_compound_selected()

            # 显示结果
            if matching_compounds:
                self.pie_result_text.setText(f"找到 {len(matching_compounds)} 个符合质量数 {target_mass:.4f} (±{tolerance}) 的化合物")

                # 如果只有一个匹配的化合物，自动选中它
                if len(matching_compounds) == 1:
                    self.pie_compounds_list.selectRow(0)
                    self.on_pie_compound_selected()
            else:
                self.pie_result_text.setText(f"未找到符合质量数 {target_mass:.4f} (±{tolerance}) 的化合物")
        except Exception as e:
            print(f"查找化合物时出错: {str(e)}")
            QMessageBox.critical(self, "错误", f"查找化合物时出错: {str(e)}")
            import traceback
            traceback.print_exc()

    def on_pie_compound_cell_changed(self, row, column):
        """当化合物表格单元格内容变化时的处理"""
        try:
            # 只处理拟合系数列的变化
            if column != 1:
                return

            # 获取拟合系数
            weight_item = self.pie_compounds_list.item(row, column)
            if not weight_item:
                return

            # 尝试将拟合系数转换为浮点数
            try:
                weight = float(weight_item.text())
                # 确保拟合系数不为负数
                if weight < 0:
                    weight = 0
                    self.pie_compounds_list.setItem(row, column, QTableWidgetItem(f"{weight:.10f}"))
                print(f"行 {row} 的拟合系数已更新为: {weight:.10f}")

                # 获取化合物数据
                compound_item = self.pie_compounds_list.item(row, 2)  # 化合物名称列
                if compound_item:
                    compound = compound_item.data(Qt.ItemDataRole.UserRole)
                    if compound:
                        # 更新化合物的拟合系数
                        compound['weight'] = weight
                        compound_item.setData(Qt.ItemDataRole.UserRole, compound)
                        print(f"化合物 {compound.get('name', '')} 的拟合系数已更新为: {weight}")

                        # 更新勾选状态
                        checkbox = self.pie_compounds_list.item(row, 0)
                        if checkbox:
                            # 如果系数大于0，自动勾选；否则取消勾选
                            checkbox.setCheckState(Qt.CheckState.Checked if weight > 0 else Qt.CheckState.Unchecked)

                        # 直接更新绘图，不使用延迟
                        try:
                            # 先更新主窗口绘图
                            self.update_main_window_plot()

                            # 然后按拟合系数重新排序化合物列表
                            self.sort_compounds_by_coefficient()

                            print("已完成更新操作")
                        except Exception as e:
                            print(f"更新操作时出错: {str(e)}")
                            import traceback
                            traceback.print_exc()
            except ValueError:
                # 如果输入的不是有效的浮点数，重置为0.0
                self.pie_compounds_list.setItem(row, column, QTableWidgetItem("0.0000000000"))
                print(f"行 {row} 的拟合系数无效，已重置为0.0000000000")
        except Exception as e:
            print(f"处理拟合系数变化时出错: {str(e)}")
            import traceback
            traceback.print_exc()

    def on_pie_compound_selected(self):
        """当选择PIE化合物时的处理"""
        try:
            # 获取当前选中的行
            selected_items = self.pie_compounds_list.selectedItems()
            if not selected_items:
                print("没有选中的化合物")
                return

            # 获取化合物数据
            row = selected_items[0].row()
            print(f"选中的行: {row}")

            compound_item = self.pie_compounds_list.item(row, 2)  # 化合物名称列
            if not compound_item:
                print(f"行 {row} 的化合物名称列没有项目")
                return

            compound = compound_item.data(Qt.ItemDataRole.UserRole)
            if not compound:
                print(f"行 {row} 的化合物名称列没有关联的化合物数据")
                # 尝试从表格中重建化合物数据
                try:
                    name = compound_item.text()
                    formula_item = self.pie_compounds_list.item(row, 3)  # 分子式列
                    mass_item = self.pie_compounds_list.item(row, 4)     # 质量数列
                    weight_item = self.pie_compounds_list.item(row, 1)   # 权重系数列
                    source_item = self.pie_compounds_list.item(row, 6)   # 来源列

                    formula = formula_item.text() if formula_item else ""
                    mass = float(mass_item.text()) if mass_item else 0
                    source = source_item.text() if source_item else ""
                    
                    # 获取当前的权重系数，不设置为默认的0.0
                    weight = 0.0
                    if weight_item:
                        try:
                            weight = float(weight_item.text())
                        except ValueError:
                            weight = 0.0

                    # 创建化合物数据
                    compound = {
                        'name': name,
                        'formula': formula,
                        'mol_weight': mass,
                        'source': source,
                        'pie_data': None,  # 没有PIE数据
                        'weight': weight   # 使用表格中的实际权重系数
                    }
                    print(f"从表格重建化合物数据: {name}, {formula}, {mass}")
                except Exception as e:
                    print(f"重建化合物数据时出错: {str(e)}")
                    return

            print(f"选中的化合物: {compound.get('name', '')}, {compound.get('formula', '')}, {compound.get('mol_weight', 0)}")

            # 显示化合物信息
            name = compound.get('name', '')
            formula = compound.get('formula', '')
            mass = compound.get('mol_weight', 0)
            source = compound.get('source', '')

            info_text = f"选中化合物: {name} ({formula})\n"
            info_text += f"质量数: {mass:.4f}\n"
            if source:
                info_text += f"数据来源: {source}\n"

            # 清空电离截面数据表格
            self.pie_data_table.setRowCount(0)

            # 获取PIE数据
            pie_data = compound.get('pie_data', None)
            if pie_data:
                # 获取能量和PIE值
                energy = pie_data.get('energy', [])
                pie = pie_data.get('pie', [])

                if energy and pie and len(energy) == len(pie):
                    # 显示PIE数据信息
                    info_text += f"PIE数据点数: {len(energy)}\n"
                    info_text += f"能量范围: {min(energy):.2f} - {max(energy):.2f} eV\n"

                    # 在表格中显示PIE数据
                    self.pie_data_table.setRowCount(len(energy))
                    for i, (e, p) in enumerate(zip(energy, pie)):
                        self.pie_data_table.setItem(i, 0, QTableWidgetItem(f"{e:.4f}"))
                        self.pie_data_table.setItem(i, 1, QTableWidgetItem(f"{p:.4f}"))
                else:
                    info_text += "PIE数据不完整或格式错误\n"
            else:
                info_text += "没有PIE数据\n"

            # 更新结果文本
            self.pie_result_text.setText(info_text)

            # 打印调试信息
            print(f"已选择化合物: {name} ({formula}), 质量数: {mass:.4f}")
            if pie_data:
                print(f"PIE数据点数: {len(energy) if energy else 0}")
            else:
                print("没有PIE数据")

            # 当选择化合物时，更新主窗口的PIE曲线显示
            # 这确保选择不同化合物时，主窗口显示正确的权重信息
            try:
                self.update_main_window_plot()
                print(f"已更新主窗口PIE曲线显示")
            except Exception as update_e:
                print(f"更新主窗口PIE曲线显示时出错: {str(update_e)}")
                
        except Exception as e:
            print(f"处理PIE化合物选择时出错: {str(e)}")
            import traceback
            traceback.print_exc()

    def analyze_pie_curve(self):
        """分析PIE曲线，使用与pie_analysis.py相同的方法"""
        try:
            # 🔧 在分析开始前保存当前勾选状态，确保分析后保持用户的选择
            self.original_checked_compounds = set()
            for row in range(self.pie_compounds_list.rowCount()):
                checkbox = self.pie_compounds_list.item(row, 0)
                if checkbox and checkbox.checkState() == Qt.CheckState.Checked:
                    compound_item = self.pie_compounds_list.item(row, 2)  # 化合物名称列
                    source_item = self.pie_compounds_list.item(row, 6)    # 来源列
                    if compound_item and source_item:
                        compound_name = compound_item.text()
                        compound_source = source_item.text()
                        key = f"{compound_name}|{compound_source}"
                        self.original_checked_compounds.add(key)
            
            print(f"🔧 分析开始前保存了 {len(self.original_checked_compounds)} 个勾选化合物的状态")
            
            # 导入必要的模块
            import numpy as np
            from scipy.optimize import nnls
            from scipy.interpolate import interp1d

            # 获取结果页面的数据
            main_window = None

            # 首先尝试使用类名查找
            for widget in QApplication.topLevelWidgets():
                if widget.__class__.__name__ == 'MainWindow':
                    main_window = widget
                    print(f"通过类名找到主窗口: {widget.__class__.__name__}")
                    break

            # 如果没找到，尝试使用窗口标题查找（排除当前窗口）
            if not main_window:
                for widget in QApplication.topLevelWidgets():
                    if (hasattr(widget, 'windowTitle') and
                        "质谱数据分析" in widget.windowTitle() and
                        "化合物查询" not in widget.windowTitle()):
                        main_window = widget
                        print(f"通过窗口标题找到主窗口: {widget.windowTitle()}")
                        break

            # 检查主窗口和结果页面
            if not main_window or not hasattr(main_window, 'results_widget'):
                QMessageBox.warning(self, "错误", "无法找到主窗口或结果页面")
                return

            results_widget = main_window.results_widget

            # 检查横坐标类型，必须是能量(eV)
            if hasattr(results_widget, 'xAxisCombo'):
                x_axis_type = results_widget.xAxisCombo.currentText()
                if x_axis_type != "能量(eV)":
                    QMessageBox.warning(self, "横坐标类型错误", 
                                      f"PIE拟合分析需要以能量(eV)为横坐标。\n"
                                      f"当前横坐标类型为：{x_axis_type}\n\n"
                                      f"请在主窗口的结果页面中将横坐标类型切换为'能量(eV)'后再进行PIE分析。")
                    return
            else:
                QMessageBox.warning(self, "错误", "无法获取横坐标类型信息")
                return

            print(f"横坐标类型检查通过: {x_axis_type}")

            # 检查是否有峰数据
            if not hasattr(results_widget, 'peaks') or not results_widget.peaks:
                QMessageBox.warning(self, "警告", "结果页面没有峰数据")
                return

            # 获取当前选中的峰索引
            peak_index = results_widget.chartCombo.currentIndex()
            if peak_index < 0 or peak_index >= len(results_widget.peaks):
                QMessageBox.warning(self, "警告", "未选择峰")
                return

            # 获取峰的质量数
            peak_mass = results_widget.peaks[peak_index]['mass']

            # 自动更新质量数输入框
            self.pie_mass_input.setText(f"{peak_mass:.4f}")

            # 获取质量数和容差
            try:
                target_mass = float(self.pie_mass_input.text())
                tolerance = float(self.pie_tolerance_input.text())
            except ValueError:
                QMessageBox.warning(self, "警告", "请输入有效的质量数和容差")
                return

            # 获取最大化合物数和最大方案数
            max_compounds = self.max_compounds_input.value()
            max_solutions = self.max_solutions_input.value()
            window_size = self.window_size_input.value()

            self.log_print(f"\n开始PIE分析，质量数: {target_mass:.4f}，容差: ±{tolerance}", level=2)
            self.log_print(f"最大化合物数: {max_compounds}，最大方案数: {max_solutions}，插值窗口大小: {window_size}", level=3)

            # 检查是否已加载化合物数据库
            if not self.pie_compounds_data:
                # 尝试加载化合物数据库
                if not self.load_pie_compounds_data():
                    QMessageBox.warning(self, "警告", "未能加载化合物数据库")
                    return

            # 1. 获取当前表格中勾选的化合物
            self.log_print(f"\n获取当前表格中勾选的化合物", level=3)
            matching_compounds = []
            name_sources = {}  # 初始化 name_sources

            # 遍历表格中的所有行
            for row in range(self.pie_compounds_list.rowCount()):
                # 检查是否勾选
                checkbox = self.pie_compounds_list.item(row, 0)
                if checkbox and checkbox.checkState() == Qt.CheckState.Checked:
                    # 获取化合物数据
                    compound_item = self.pie_compounds_list.item(row, 2)  # 化合物名称列
                    if compound_item:
                        compound = compound_item.data(Qt.ItemDataRole.UserRole)
                        if compound:
                            # 获取权重系数
                            weight_item = self.pie_compounds_list.item(row, 1)
                            if weight_item:
                                try:
                                    weight = float(weight_item.text())
                                    compound['weight'] = weight
                                except ValueError:
                                    compound['weight'] = 1.0

                            # 记录化合物名称和来源
                            name = compound.get('name', '')
                            if name not in name_sources:
                                name_sources[name] = []
                            name_sources[name].append(compound)

                            # 检查是否有PIE数据
                            pie_data = compound.get('pie_data', None)
                            if pie_data and 'energy' in pie_data and 'pie' in pie_data:
                                matching_compounds.append(compound)

            # 如果没有勾选的化合物，则按照 pie_analysis.py 的逻辑筛选
            if not matching_compounds:
                self.log_print("\n没有勾选的化合物，根据质量数和来源规则从数据库筛选...", level=3)
                source_groups = {}
                if self.pie_compounds_data:
                    for compound_original_db in self.pie_compounds_data:
                        compound_db_copy = compound_original_db.copy()
                        try:
                            mass_val_str_db = str(compound_db_copy.get('mol_weight', compound_db_copy.get('mass_number', '')))
                            if not mass_val_str_db: continue
                            mass_val_db = float(mass_val_str_db)
                            compound_db_copy['mol_weight'] = mass_val_db

                            if abs(mass_val_db - target_mass) > tolerance: continue
                            
                            source_db = compound_db_copy.get('source', '未知来源')
                            if source_db not in source_groups: source_groups[source_db] = []
                            source_groups[source_db].append(compound_db_copy)
                        except (ValueError, TypeError):
                            continue
                
                temp_name_sources_db = {} # Temporary for db-sourced items to update global name_sources

                for source_filter, items_in_source_filter in source_groups.items():
                    non_total_items_filter = [c for c in items_in_source_filter if 'Total' not in c.get('name', '') and 'fragment' not in c.get('name', '')]
                    total_items_filter = [c for c in items_in_source_filter if 'Total' in c.get('name', '') and 'fragment' not in c.get('name', '')]
                    
                    chosen_items_filter = []
                    if non_total_items_filter: chosen_items_filter.extend(non_total_items_filter)
                    elif total_items_filter: chosen_items_filter.extend(total_items_filter)
                    
                    for compound_chosen_filter in chosen_items_filter:
                        name_chosen = compound_chosen_filter.get('name', '')
                        # Update global name_sources
                        if name_chosen not in name_sources: name_sources[name_chosen] = []
                        if compound_chosen_filter not in name_sources[name_chosen]: name_sources[name_chosen].append(compound_chosen_filter)
                        
                        # Update temporary name_sources for this specific DB scan, if needed for local logic
                        if name_chosen not in temp_name_sources_db: temp_name_sources_db[name_chosen] = []
                        if compound_chosen_filter not in temp_name_sources_db[name_chosen]: temp_name_sources_db[name_chosen].append(compound_chosen_filter)


                        pie_data_filter = compound_chosen_filter.get('pie_data', None)
                        if pie_data_filter and 'energy' in pie_data_filter and 'pie' in pie_data_filter:
                            if 'weight' not in compound_chosen_filter: compound_chosen_filter['weight'] = 0.0
                            if compound_chosen_filter not in matching_compounds: matching_compounds.append(compound_chosen_filter)
                
                self.log_print(f"筛选后从数据库加入 {len(matching_compounds)} 个候选化合物 (因无UI勾选)", level=3)
                
                # Update printing for compounds found via DB filtering if UI was empty
                if matching_compounds:
                    self.log_print("\n更新候选化合物列表 (基于数据库筛选):", level=3)
                    self.log_print("-" * 100, level=3)
                    self.log_print(f"{'化合物名称':<20} {'分子式':<15} {'质量数':<10} {'数据点数':<10} {'来源'}", level=3)
                    self.log_print("-" * 100, level=3)
                    for compound_print_db in matching_compounds: # Print from matching_compounds now
                        name_print_db = compound_print_db.get('name', '')
                        pie_data_print_db = compound_print_db.get('pie_data', {})
                        energy_print_db = pie_data_print_db.get('energy', [])
                        mol_weight_print_val_db = 0.0
                        try:
                            mol_weight_print_val_db = float(compound_print_db.get('mol_weight', 0.0))
                        except (ValueError, TypeError): pass
                        self.log_print(f"{name_print_db:<20} {compound_print_db.get('formula', ''):<15} {mol_weight_print_val_db:<10.4f} {len(energy_print_db):<10} {compound_print_db.get('source', '未知来源')}", level=3)
                    self.log_print("-" * 100, level=3)


            # 检查是否有足够的化合物
            if not matching_compounds:
                QMessageBox.warning(self, "警告", "没有找到符合筛选条件的化合物（质量数、容差、有效PIE数据）。请检查输入或数据库。")
                self.pie_compounds_list.setRowCount(0)
                self.pie_result_text.setText(f"未找到符合质量数 {target_mass:.4f} (±{tolerance}) 的化合物，或其无有效PIE数据。")
                return

            # 不再提前选择最佳PIE数据源，直接使用所有匹配的化合物
            # 让generate_multiple_solutions方法来处理不同数据源的组合
            self.log_print(f"\n收集到 {len(matching_compounds)} 个化合物数据源，将在拟合过程中尝试不同组合", level=2)
            
            # 如果化合物数据源总数过多，可以限制但不提前选择最佳来源
            if len(matching_compounds) > max_compounds * 3:  # 允许每个化合物名称平均有3个数据源
                # 按质量数差异排序，但保留多个数据源
                matching_compounds.sort(key=lambda x: abs(x.get('mol_weight', 0) - target_mass))
                matching_compounds = matching_compounds[:max_compounds * 3]
                self.log_print(f"化合物数据源数量过多，截取前 {max_compounds * 3} 个最相关的数据源", level=3)

            # 2. 获取实验数据
            self.log_print("\n获取实验数据...", level=3)

            # 获取当前选中的峰索引
            peak_index = results_widget.chartCombo.currentIndex()
            if peak_index < 0 or peak_index >= len(results_widget.peaks):
                QMessageBox.warning(self, "警告", "未选择峰")
                return

            # 获取X轴数据（能量）和Y轴数据（强度）
            x_values = []
            y_values = []

            # 根据选择的横坐标类型获取X轴数据
            x_axis_type = results_widget.xAxisCombo.currentText()
            if x_axis_type == "能量(eV)":
                # 从文件元数据中获取能量值
                for filename in results_widget.file_names:
                    energy = results_widget.file_metadata.get(filename, {}).get('energy')
                    if energy is not None:
                        x_values.append(energy)
                    else:
                        x_values.append(float('nan'))
            else:
                QMessageBox.warning(self, "警告", "请选择能量(eV)作为X轴类型")
                return

            # 获取Y轴数据（强度）
            if isinstance(results_widget.results, dict):
                # 字典格式的结果
                for file_name in results_widget.file_names:
                    if file_name in results_widget.results:
                        result_data = results_widget.results[file_name]
                        if peak_index < result_data.shape[0]:
                            y_values.append(float(result_data[peak_index, 0]))
                        else:
                            y_values.append(0.0)
                    else:
                        y_values.append(0.0)
            else:
                # 数组格式的结果
                y_values = results_widget.results[:, peak_index].tolist()

            # 移除NaN值
            valid_indices = []
            for i, (x, y) in enumerate(zip(x_values, y_values)):
                if not np.isnan(x) and not np.isnan(y):
                    valid_indices.append(i)

            x_exp = np.array([x_values[i] for i in valid_indices])
            y_exp = np.array([y_values[i] for i in valid_indices])

            # 检查是否有足够的数据点
            if len(x_exp) < 3:
                QMessageBox.warning(self, "警告", "实验数据点数不足")
                return

            self.log_print(f"获取到 {len(x_exp)} 个有效的实验数据点", level=2)
            self.log_print(f"能量范围: {min(x_exp):.2f} - {max(x_exp):.2f} eV", level=2)
            self.log_print(f"🔍【调试】原始实验数据Y值统计:", level=3)
            self.log_print(f"  最小值: {np.min(y_exp):.6f}", level=3)
            self.log_print(f"  最大值: {np.max(y_exp):.6f}", level=3)
            self.log_print(f"  平均值: {np.mean(y_exp):.6f}", level=3)

            # 记录当前质量数的实验数据坐标范围（用于保持原始坐标轴范围功能）
            self.current_mass_for_axis = target_mass
            if target_mass not in self.mass_axis_ranges:
                # 第一次分析这个质量数，记录实验数据的坐标范围（Y轴范围增加10%的边距）
                exp_x_range = [min(x_exp), max(x_exp)]
                y_min, y_max = min(y_exp), max(y_exp)
                y_margin = (y_max - y_min) * 0.1  # 10%的边距
                exp_y_range = [y_min - y_margin, y_max + y_margin]
                self.mass_axis_ranges[target_mass] = {
                    'x_range': exp_x_range,
                    'y_range': exp_y_range
                }
                print(f"记录质量数 {target_mass:.4f} 的实验数据坐标范围: X={exp_x_range}, Y={exp_y_range} (Y轴已添加10%边距)")
            else:
                print(f"质量数 {target_mass:.4f} 的坐标范围已记录: {self.mass_axis_ranges[target_mass]}")

            # 3. 寻找共同的能量范围
            print("\n寻找共同的能量范围...")

            # 获取所有化合物的能量范围
            compound_ranges = []
            for compound in matching_compounds:
                pie_data = compound.get('pie_data', {})
                energy = pie_data.get('energy', [])
                if energy:
                    compound_ranges.append((min(energy), max(energy)))

            # 如果没有化合物有能量范围，无法继续
            if not compound_ranges:
                QMessageBox.warning(self, "警告", "没有化合物有有效的PIE数据")
                return

            # 找出共同的能量范围
            common_min = max([r[0] for r in compound_ranges])
            common_max = min([r[1] for r in compound_ranges])

            # 检查实验数据是否在共同范围内
            exp_min = min(x_exp)
            exp_max = max(x_exp)

            # 取交集
            final_min = max(common_min, exp_min)
            final_max = min(common_max, exp_max)

            # 检查是否有有效的共同范围
            if final_min >= final_max:
                print(f"警告: 没有有效的共同能量范围")
                print(f"化合物共同范围: {common_min:.2f} - {common_max:.2f} eV")
                print(f"实验数据范围: {exp_min:.2f} - {exp_max:.2f} eV")

                # 使用最大可能的范围
                final_min = max(min([r[0] for r in compound_ranges]), exp_min)
                final_max = min(max([r[1] for r in compound_ranges]), exp_max)

                print(f"使用最大可能的范围: {final_min:.2f} - {final_max:.2f} eV")

                if final_min >= final_max:
                    QMessageBox.warning(self, "警告", "无法找到有效的共同能量范围")
                    return

            print(f"共同能量范围: {final_min:.2f} - {final_max:.2f} eV")

            # 4. 在共同范围内插值
            print("\n在共同范围内插值...")

            # 创建能量点（与refresh_solutions保持一致，使用实验数据的分辨率和范围）
            energy_points = np.linspace(np.min(x_exp), np.max(x_exp), len(x_exp))
            print(f"🔧 使用实验数据的完整分辨率: {len(x_exp)} 个能量点，范围 {np.min(x_exp):.2f}-{np.max(x_exp):.2f} eV")

            # 对实验数据进行插值（保持原始物理量级，与pie_analysis.py一致）
            exp_interp = interp1d(x_exp, y_exp, kind='linear', bounds_error=False, fill_value=0)
            y_exp_interp = exp_interp(energy_points)

            self.log_print(f"🔍【调试】插值后实验数据统计:", level=3)
            self.log_print(f"  最小值: {np.min(y_exp_interp):.6f}", level=3)
            self.log_print(f"  最大值: {np.max(y_exp_interp):.6f}", level=3)
            self.log_print(f"  平均值: {np.mean(y_exp_interp):.6f}", level=3)
            self.log_print(f"  ⚠️  保持原始物理量级，不进行归一化（与pie_analysis.py一致）", level=3)

            # 根据pie_analysis.py中的插值方法进行改进
            def interpolate_pie_data(compounds, target_energy, window_size=window_size):
                """将不同化合物的能量步长统一到目标能量轴上
                
                Args:
                    compounds: 化合物数据列表
                    target_energy: 目标能量轴
                    window_size: 插值窗口大小，必须为奇数
                """
                if window_size % 2 == 0:
                    window_size = window_size + 1  # 确保为奇数
                
                interpolated_data = {}
                half_window = window_size // 2
                
                for compound in compounds:
                    pie_data = compound.get('pie_data', {})
                    # 类型检查和转换
                    try:
                        energy = np.array(pie_data.get('energy', []), dtype=float)
                        pie = np.array(pie_data.get('pie', []), dtype=float)
                    except Exception as e:
                        self.log_print(f"化合物 {compound.get('name', '')} 的PIE数据无法转换为float，已跳过: {e}", level=1)
                        continue
                    source = compound.get('source', '未知来源')
                    
                    if len(energy) == 0 or len(pie) == 0 or len(energy) != len(pie):
                        self.log_print(f"化合物 {compound.get('name', '')} 缺少有效的PIE数据，跳过", level=1)
                        continue
                    
                    # 确保能量范围和目标能量轴有重叠
                    valid_min = max(energy.min(), target_energy.min())
                    valid_max = min(energy.max(), target_energy.max())
                    
                    if valid_min >= valid_max:
                        print(f"化合物 {compound.get('name', '')} 的能量范围与目标不匹配，跳过")
                        continue
                    
                    # 创建混合插值函数
                    interpolated_pie = np.zeros_like(target_energy)
                    
                    # 对每个目标能量点进行插值
                    for i, e in enumerate(target_energy):
                        idx = np.searchsorted(energy, e)
                        if idx == 0:
                            interpolated_pie[i] = 0
                        elif idx == len(energy):
                            interpolated_pie[i] = pie[-1]
                        else:
                            if e == energy[idx-1]:
                                interpolated_pie[i] = pie[idx-1]
                            else:
                                start_idx = max(0, idx - half_window)
                                end_idx = min(len(energy), idx + half_window + 1)
                                nearby_energy = energy[start_idx:end_idx]
                                nearby_pie = pie[start_idx:end_idx]
                                prev_pie = pie[idx-1]
                                next_pie = pie[idx]
                                if (prev_pie == 0 and next_pie > 0) or (prev_pie > 0 and next_pie == 0):
                                    interpolated_pie[i] = prev_pie
                                else:
                                    if prev_pie > 0 and next_pie > 0:
                                        ratio = (e - energy[idx-1]) / (energy[idx] - energy[idx-1])
                                        interpolated_pie[i] = prev_pie + ratio * (next_pie - prev_pie)
                                    else:
                                        interpolated_pie[i] = 0
                    interpolated_pie = np.maximum(0, interpolated_pie)
                    
                    # 📊 保持原始PIE物理量级（与pie_analysis.py一致，不进行归一化）
                    self.log_print(f"  化合物 {compound.get('name', '')}: PIE最大值 = {np.max(interpolated_pie):.6f} (保持原始量级)", level=3)
                    compound_key = f"{compound.get('name', '')}_{compound.get('source', '未知来源')}"
                    interpolated_data[compound_key] = {
                        'name': compound.get('name', ''),
                        'formula': compound.get('formula', ''),
                        'mass_number': compound.get('mol_weight', 0),
                        'interpolated_pie': interpolated_pie,
                        'source': source,
                        'compound': compound
                    }
                return interpolated_data

            # 使用改进的插值方法
            self.log_print(f"使用改进的插值方法（窗口大小={window_size}）...", level=3)
            interpolated_data = interpolate_pie_data(matching_compounds, energy_points, window_size)
            self.log_print(f"成功对 {len(interpolated_data)} 个化合物进行插值", level=2)

            # 5. 创建权重函数，使用与pie_analysis.py一致的方法
            self.log_print("\n创建权重函数...", level=3)

            def create_weights(energy, weight_ranges=None):
                """创建能量区间重要性权重数组
                
                ⚠️  重要说明：这里的"权重"是指【能量区间重要性权重】，
                用于控制NNLS拟合算法对不同能量区间的重视程度，
                与化合物的【拟合系数】是完全不同的概念！
                
                Args:
                    energy: 能量数组
                    weight_ranges: 能量区间重要性权重范围列表，每个元素为(start_energy, end_energy, importance_weight)的元组
                                  如果为None，使用GUI中设置的能量区间重要性权重
                
                Returns:
                    能量区间重要性权重数组（用于加权NNLS拟合）
                """
                weights = np.ones_like(energy)  # 初始化权重为1
                
                if weight_ranges is None:
                    # 使用GUI中设置的权重
                    min_energy = np.min(energy)
                    max_energy = np.max(energy)
                    
                    # 获取GUI中设置的能量区间和权重
                    range1_min = self.energy_range1_min.value()
                    range1_max = self.energy_range1_max.value()
                    weight1 = self.weight1_input.value()
                    
                    range2_min = self.energy_range2_min.value()
                    range2_max = self.energy_range2_max.value()
                    weight2 = self.weight2_input.value()
                    
                    range3_min = self.energy_range3_min.value()
                    range3_max = self.energy_range3_max.value()
                    weight3 = self.weight3_input.value()
                    
                    # 确保区间在能量范围内
                    range1_min = max(min_energy, range1_min)
                    range1_max = min(max_energy, range1_max)
                    range2_min = max(min_energy, range2_min)
                    range2_max = min(max_energy, range2_max)
                    range3_min = max(min_energy, range3_min)
                    range3_max = min(max_energy, range3_max)
                    
                    # 创建权重范围列表
                    weight_ranges = []
                    
                    # 只添加有效的区间（起始能量小于结束能量）
                    if range1_min < range1_max:
                        weight_ranges.append((range1_min, range1_max, weight1))
                    
                    if range2_min < range2_max:
                        weight_ranges.append((range2_min, range2_max, weight2))
                    
                    if range3_min < range3_max:
                        weight_ranges.append((range3_min, range3_max, weight3))
                    
                    # 如果没有有效区间，使用默认权重
                    if not weight_ranges:
                        weight_ranges = [(min_energy, max_energy, 1)]
                
                # 应用权重
                for start_e, end_e, weight in weight_ranges:
                    mask = (energy >= start_e) & (energy <= end_e)
                    weights[mask] = weight
                
                # 打印能量区间重要性权重设置信息
                self.log_print("\n能量区间重要性权重设置：", level=3)
                self.log_print("-" * 60, level=3)
                for start_e, end_e, weight in weight_ranges:
                    self.log_print(f"能量范围 [{start_e:.1f}, {end_e:.1f}] eV: 重要性权重 = {weight}", level=3)
                self.log_print("-" * 60, level=3)
                
                return weights

            # 6. 计算拟合质量的评价指标函数
            def calculate_fit_metrics(target_pie, fitted_pie, energy):
                """计算拟合质量的多个评价指标"""
                return MetricsCalculator.calculate_fit_metrics(target_pie, fitted_pie, energy)

            # 7. 计算拟合方案的综合评分函数
            def calculate_score(solution):
                """计算拟合方案的综合评分
                
                权重分配依据：
                1. R² (50%): 最重要，反映整体趋势的拟合效果
                2. 峰值位置误差 (25%): 关键特征点的准确性
                3. 阈值能量误差 (15%): 起始点的准确性
                4. RMSE (10%): 用于评估整体拟合的平滑度
                """
                metrics = solution[2]
                score = (
                    0.70 * metrics['r_squared'] +           # R²越大越好，反映整体趋势
                    -0.10 * metrics['peak_position_error'] + # 峰值位置误差越小越好
                    -0.10 * metrics['threshold_energy_error'] + # 阈值能量误差越小越好
                    -0.10 * metrics['rmse']                 # RMSE越小越好，反映平滑度
                )
                return score

            # 8. 使用非负最小二乘法拟合（与pie_analysis.py一致）
            self.log_print("\n使用非负最小二乘法拟合...", level=3)
            
            # 创建权重数组
            weights = create_weights(energy_points)
            
            # 应用权重到数据矩阵和目标数据
            # 保证A为float类型，防止字符串类型导致的numpy乘法报错
            # 将字典转换为列表，保持顺序一致
            interpolated_list = list(interpolated_data.values())
            compound_names = list(interpolated_data.keys())
            
            A = np.array([np.array(data['interpolated_pie'], dtype=float) for data in interpolated_list], dtype=float).T
            A_weighted = A * weights[:, np.newaxis]
            y_exp_interp_weighted = y_exp_interp * weights
            
            # 打印每个化合物的曲线相似度矩阵，帮助诊断
            self.log_print("\n化合物曲线相似度矩阵:", level=3)
            n_compounds = A.shape[1]
            similarity_matrix = np.zeros((n_compounds, n_compounds))
            for i in range(n_compounds):
                for j in range(n_compounds):
                    # 计算归一化后的点积（余弦相似度）
                    norm_i = np.linalg.norm(A[:, i])
                    norm_j = np.linalg.norm(A[:, j])
                    if norm_i > 0 and norm_j > 0:
                        similarity_matrix[i, j] = np.dot(A[:, i], A[:, j]) / (norm_i * norm_j)
                    else:
                        similarity_matrix[i, j] = 0
            
            # 打印相似度矩阵
            self.log_print("-" * 60, level=3)
            for i in range(n_compounds):
                compound_name = interpolated_list[i].get('name', f'化合物{i+1}')
                row_str = f"{compound_name[:15]:<15} |"
                for j in range(n_compounds):
                    row_str += f" {similarity_matrix[i, j]:5.2f}"
                self.log_print(row_str, level=3)
            self.log_print("-" * 60, level=3)
            
            # 🚀 新的多方案拟合算法（参考refresh_solutions的正确实现）
            try:
                # 获取用户选择的拟合方案
                fitting_method = self.fitting_method_combo.currentText()
                
                # 使用PIE_analysis.py的正确权重算法
                self.log_print(f"\n🔬 使用标准加权NNLS算法（参考pie_analysis.py）...", level=3)
                
                # 执行加权NNLS拟合（与pie_analysis.py完全一致）
                coefficients, residual = nnls(A_weighted, y_exp_interp_weighted)
                
                # 🔑 关键：用原始未加权的A矩阵计算拟合结果（pie_analysis.py的做法）
                y_fit_standard = A.dot(coefficients)
                
                self.log_print(f"📊 权重NNLS拟合结果:", level=2)
                self.log_print(f"  残差: {residual:.6f}", level=2)
                self.log_print(f"  非零系数数量: {np.sum(coefficients > 1e-10)}", level=2)
                
                # 显示所有非零系数（这些应该是正确的量级）
                self.log_print(f"\n🎯 拟合系数详情（应该是十几量级）:", level=2)
                for i, coef in enumerate(coefficients):
                    if coef > 1e-10:
                        compound_name = interpolated_list[i].get('name', f'化合物{i+1}')
                        pie_max = np.max(A[:, i])
                        theoretical_coef = np.max(y_exp_interp) / pie_max if pie_max > 0 else 0
                        self.log_print(f"  {compound_name}:", level=2)
                        self.log_print(f"    拟合系数: {coef:.6f}", level=2)
                        self.log_print(f"    PIE最大值: {pie_max:.6f}", level=3)
                        self.log_print(f"    理论系数: {theoretical_coef:.6f} (实验最大值/PIE最大值)", level=3)
                        self.log_print(f"    系数验证: {coef * pie_max:.6f} (系数×PIE最大值，应接近实验最大值)", level=3)
                
                # 验证拟合质量
                self.log_print(f"\n🔍 拟合质量验证:", level=2)
                fitted_max = np.max(y_fit_standard)
                exp_max = np.max(y_exp_interp) 
                ratio = fitted_max / exp_max if exp_max > 0 else 0
                self.log_print(f"  拟合最大值: {fitted_max:.2f}", level=2)
                self.log_print(f"  实验最大值: {exp_max:.2f}", level=2)  
                self.log_print(f"  拟合/实验比值: {ratio:.3f}", level=2)
                self.log_print(f"  💡 如果拟合系数合理，拟合最大值应接近实验最大值", level=3)
                
                # 使用新的多方案生成算法（基于修正后的基础）
                self.log_print(f"\n🔬 开始生成多个拟合方案 (方法: {fitting_method})...", level=3)
                solutions = self.generate_multiple_solutions(
                    matching_compounds, 
                    y_exp_interp, 
                    energy_points,
                    max_solutions=max_solutions,
                    fitting_method='nnls',  # 暂时固定使用NNLS，后续可扩展
                    weights=weights  # 传递能量区间重要性权重
                )
                
                if not solutions:
                    self.log_print("❌ 未能生成任何有效的拟合方案", level=1)
                    QMessageBox.warning(self, "警告", "未能生成任何有效的拟合方案，请检查化合物数据质量")
                    return
                
                # 保存所有方案到实例变量
                self.current_solutions = solutions
                
                # 保存所有匹配的化合物，供方案切换时使用
                self.all_matching_compounds = matching_compounds.copy()
                
                # 更新方案选择下拉框
                self.solution_combo.clear()
                for i, (compounds, fit_result, metrics) in enumerate(solutions):
                    score_text = f"方案{i+1} (R²={metrics['r_squared']:.3f}, 评分={metrics['overall_score']:.3f})"
                    self.solution_combo.addItem(score_text)
                
                # 选择最佳方案（第一个）
                if solutions:
                    self.solution_combo.setCurrentIndex(0)
                    best_solution_compounds, best_solution_fit, best_solution_metrics = solutions[0]
                    
                    self.log_print(f"✅ 生成了 {len(solutions)} 个拟合方案", level=2)
                    self.log_print(f"🏆 最佳方案: R²={best_solution_metrics['r_squared']:.4f}, 评分={best_solution_metrics['overall_score']:.4f}", level=2)
                    
                    # 使用传统的变量名以保持兼容性
                    coefficients = [comp['coefficient'] for comp in best_solution_compounds]
                    y_fit = best_solution_fit
                    metrics = best_solution_metrics
                    
                    # 构建interpolated_list以保持兼容性
                    interpolated_list = []
                    for comp in best_solution_compounds:
                        interpolated_list.append({
                            'compound': comp['compound'],
                            'name': comp['name']
                        })

                                # 打印评价指标
                self.log_print("\n📊 拟合质量评价指标:", level=2)
                self.log_print(f"R² = {metrics['r_squared']:.4f}", level=2)
                self.log_print(f"RMSE = {metrics['rmse']:.4f}", level=3)
                self.log_print(f"相对误差 = {metrics['relative_error']:.2%}", level=3)
                self.log_print(f"峰值位置误差 = {metrics['peak_position_error']:.4f} eV", level=3)
                self.log_print(f"阈值能量误差 = {metrics['threshold_energy_error']:.4f} eV", level=3)
                self.log_print(f"综合评分 = {metrics['overall_score']:.4f}", level=2)

                # 打印所有化合物的系数
                self.log_print("\n📋 所有化合物的系数:", level=2)
                self.log_print("-" * 80, level=2)
                self.log_print(f"{'化合物名称':<20} {'系数':<15} {'归一化系数':<15} {'来源'}", level=2)
                self.log_print("-" * 80, level=2)

                # 计算总系数
                total_coef = sum(abs(coef) for coef in coefficients if coef > 0)

                # 打印所有系数
                for i, (data, coef) in enumerate(zip(interpolated_list, coefficients)):
                    name = data.get('name', '')
                    norm_coef = coef / total_coef if total_coef > 0 else 0
                    source = data['compound'].get('source', '未知')
                    self.log_print(f"{name:<20} {coef:<15.10f} {norm_coef:<15.4f} {source}", level=2)

            except Exception as e:
                self.log_print(f"🚨 拟合过程中出错: {str(e)}", level=1)
                import traceback
                traceback.print_exc()
                QMessageBox.critical(self, "错误", f"拟合过程中出错: {str(e)}")
                return

            # 检查是否有拟合结果
            if not solutions:
                QMessageBox.warning(self, "警告", "未找到满意的拟合方案")
                return
            
            # 使用最佳方案更新UI
            best_solution_compounds, best_solution_fit, best_solution_metrics = solutions[0]
            
            # 更新结果文本
            result_text = f"PIE拟合结果 (R² = {best_solution_metrics['r_squared']:.4f}):\n\n"
            result_text += f"相对误差: {best_solution_metrics['relative_error']:.2%}\n"
            result_text += f"峰值位置误差: {best_solution_metrics['peak_position_error']:.4f} eV\n"
            result_text += f"阈值能量误差: {best_solution_metrics['threshold_energy_error']:.4f} eV\n"
            result_text += f"RMSE: {best_solution_metrics['rmse']:.4f}\n\n"
            
            # 计算总系数（用于显示归一化百分比）
            total_coef = sum(comp['coefficient'] for comp in best_solution_compounds)
            
            for comp in best_solution_compounds:
                name = comp['name']
                formula = comp['formula']
                mass = comp['mass']
                coef = comp['coefficient']  # 原始未归一化系数
                # 计算归一化百分比（仅用于显示）
                norm_coef = coef / total_coef if total_coef > 0 else 0
                source = comp['source']
                
                # 在结果文本中显示原始系数和归一化百分比
                # 确保mass是数字类型
                mass_float = float(mass) if isinstance(mass, (str, int, float)) else 0.0
                result_text += f"{name} ({formula}, {mass_float:.4f}): 系数={coef:.6f}, 占比={norm_coef:.2%}\n"
            
            self.pie_result_text.setText(result_text)
            
            # 更新表格中的化合物和系数，保留所有原始化合物
            self.pie_compounds_list.setRowCount(0)  # 清空表格
            
            # 创建一个字典来快速查找参与拟合的化合物系数
            fitted_compounds_dict = {}
            for comp in best_solution_compounds:
                compound_name = comp['compound'].get('name', '')
                compound_source = comp['compound'].get('source', '')
                # 使用名称+来源作为唯一标识
                key = f"{compound_name}|{compound_source}"
                fitted_compounds_dict[key] = comp['coefficient']
            
            # 保存所有匹配的化合物，用于方案切换时完整显示
            self.all_matching_compounds = matching_compounds.copy()
            
            # 填充所有原始化合物（保持完整列表）
            compounds_to_display = []
            for compound in matching_compounds:
                compound_name = compound.get('name', '')
                compound_source = compound.get('source', '')
                key = f"{compound_name}|{compound_source}"
                
                # 检查是否参与了拟合
                if key in fitted_compounds_dict:
                    coefficient = fitted_compounds_dict[key]
                    checked = True
                else:
                    coefficient = 0.0
                    # 🔧 如果没有参与拟合，检查用户是否原本勾选了这个化合物
                    original_checked = getattr(self, 'original_checked_compounds', set())
                    if key in original_checked:
                        checked = True
                        print(f"  ✅ 保持用户原始勾选状态: {compound_name} (未参与拟合但用户勾选)")
                    else:
                        # 使用默认逻辑：fragment不勾选，其他化合物勾选
                        is_fragment = 'fragment' in compound_name.lower() or 'fragment' in compound_source.lower()
                        checked = not is_fragment
                
                compounds_to_display.append({
                    'compound': compound,
                    'coefficient': coefficient,
                    'checked': checked
                })
            
            # 按系数大小排序（系数大的在前，系数为0的按原始顺序）
            compounds_to_display.sort(key=lambda x: (-x['coefficient'], x['compound'].get('name', '')))
            
            # 填充表格
            self.pie_compounds_list.setRowCount(len(compounds_to_display))
            for i, item in enumerate(compounds_to_display):
                compound = item['compound']
                coefficient = item['coefficient']
                checked = item['checked']
                
                # 获取化合物信息
                name = compound.get('name', '')
                formula = compound.get('formula', '')
                mass = compound.get('mol_weight', 0)
                source = compound.get('source', '')
                
                # 创建勾选框
                checkbox = QTableWidgetItem()
                checkbox.setCheckState(Qt.CheckState.Checked if checked else Qt.CheckState.Unchecked)
                
                # 创建权重系数输入框
                weight_item = QTableWidgetItem(f"{coefficient:.10f}")
                
                # 创建其他列的项目
                name_item = QTableWidgetItem(name)
                formula_item = QTableWidgetItem(formula)
                mass_item = QTableWidgetItem(f"{mass:.4f}")
                ie_item = QTableWidgetItem(self.get_first_ionization_energy(compound))
                source_item = QTableWidgetItem(source)
                
                # 添加到表格
                self.pie_compounds_list.setItem(i, 0, checkbox)
                self.pie_compounds_list.setItem(i, 1, weight_item)
                self.pie_compounds_list.setItem(i, 2, name_item)
                self.pie_compounds_list.setItem(i, 3, formula_item)
                self.pie_compounds_list.setItem(i, 4, mass_item)
                self.pie_compounds_list.setItem(i, 5, ie_item)
                self.pie_compounds_list.setItem(i, 6, source_item)
                
                # 存储化合物数据
                compound['weight'] = coefficient  # 更新化合物对象中的权重
                name_item.setData(Qt.ItemDataRole.UserRole, compound)
                
                # 设置权重系数列为可编辑，其他列为不可编辑
                for col in [0, 2, 3, 4, 5]:
                    item = self.pie_compounds_list.item(i, col)
                    if item:
                        item.setFlags(item.flags() & ~Qt.ItemFlag.ItemIsEditable)
            
            # 调整列宽
            self.pie_compounds_list.resizeColumnsToContents()
            
            # 不要立即选中第一个化合物，避免触发额外的更新
            # 这样可以避免曲线绘制后立即消失的问题
            if self.pie_compounds_list.rowCount() > 0:
                self.pie_compounds_list.selectRow(0)
                # 移除 self.on_pie_compound_selected() 调用，避免重复更新
            
            # 在主窗口中显示PIE曲线
            # 获取所有参与拟合的化合物，并确保权重正确设置
            compounds_to_show = []
            for comp in best_solution_compounds:
                # 从原始匹配化合物列表中找到完整的化合物数据（包含PIE数据）
                comp_name = comp['compound'].get('name', '')
                comp_source = comp['compound'].get('source', '')
                
                # 在原始化合物列表中查找对应的完整数据
                full_compound_data = None
                for orig_comp in matching_compounds:
                    if (orig_comp.get('name', '') == comp_name and 
                        orig_comp.get('source', '') == comp_source):
                        full_compound_data = copy.deepcopy(orig_comp)  # 深复制完整数据
                        break
                
                if full_compound_data:
                    # 确保权重正确设置
                    full_compound_data['weight'] = comp['coefficient']
                    full_compound_data['coefficient'] = comp['coefficient']
                    compounds_to_show.append(full_compound_data)
                else:
                    # 如果找不到，使用原始数据但添加权重
                    compound_data = comp['compound'].copy()
                    compound_data['weight'] = comp['coefficient']
                    compound_data['coefficient'] = comp['coefficient']
                    compounds_to_show.append(compound_data)
                    print(f"警告：未找到化合物 {comp_name} 的完整PIE数据")
            
            # 打印调试信息
            print(f"\n显示 {len(compounds_to_show)} 个化合物的PIE曲线")
            for compound in compounds_to_show:
                print(f"  - {compound.get('name', '')}: 拟合系数 = {compound.get('weight', 0.0):.10f}")
            
            self.show_pie_in_main_window(main_window, compounds_to_show, energy_points, y_exp_interp, best_solution_fit)
            
            # 显示分析完成消息
            QMessageBox.information(self, "成功", f"PIE曲线分析完成！\n生成了 {len(self.current_solutions)} 个拟合方案，已在主窗口显示最佳方案。")

        except Exception as e:
            print(f"拟合过程中出错: {str(e)}")
            import traceback
            traceback.print_exc()

            # 保存所有匹配的化合物，即使拟合失败也要保存
            self.all_matching_compounds = matching_compounds.copy()

            # 🔧 使用在分析开始前保存的勾选状态（保持用户的原始勾选状态）
            currently_checked_compounds = getattr(self, 'original_checked_compounds', set())
            print(f"🔧 使用保存的 {len(currently_checked_compounds)} 个勾选化合物状态")

            # 清空化合物列表
            self.pie_compounds_list.setRowCount(0)

            # 更新化合物列表，显示所有匹配的化合物，并保持勾选状态
            self.pie_compounds_list.setRowCount(len(matching_compounds))
            for i, compound in enumerate(matching_compounds):
                # 获取化合物信息
                name = compound.get('name', '')
                formula = compound.get('formula', '')
                mass = compound.get('mol_weight', 0)
                source = compound.get('source', '')

                # 设置默认权重系数
                if 'weight' not in compound:
                    compound['weight'] = 0.0

                # 🔧 创建勾选框，保持用户之前的勾选状态
                checkbox = QTableWidgetItem()
                compound_key = f"{name}|{source}"
                
                # 优先使用保存的勾选状态，如果没有则使用默认逻辑
                if compound_key in currently_checked_compounds:
                    checkbox.setCheckState(Qt.CheckState.Checked)
                    print(f"  ✅ 保持勾选状态: {name}")
                else:
                # 默认碎片不参与拟合
                    is_fragment = 'fragment' in name.lower() or 'fragment' in source.lower()
                checkbox.setCheckState(Qt.CheckState.Unchecked if is_fragment else Qt.CheckState.Checked)

                # 创建权重系数输入框
                weight_item = QTableWidgetItem(str(compound['weight']))

                # 创建其他列的项目
                name_item = QTableWidgetItem(name)
                formula_item = QTableWidgetItem(formula)
                mass_item = QTableWidgetItem(f"{mass:.4f}")
                ie_item = QTableWidgetItem(self.get_first_ionization_energy(compound))
                source_item = QTableWidgetItem(source)

                # 添加到表格
                self.pie_compounds_list.setItem(i, 0, checkbox)
                self.pie_compounds_list.setItem(i, 1, weight_item)
                self.pie_compounds_list.setItem(i, 2, name_item)
                self.pie_compounds_list.setItem(i, 3, formula_item)
                self.pie_compounds_list.setItem(i, 4, mass_item)
                self.pie_compounds_list.setItem(i, 5, ie_item)
                self.pie_compounds_list.setItem(i, 6, source_item)

                # 存储化合物数据
                name_item.setData(Qt.ItemDataRole.UserRole, compound)

                # 设置权重系数列为可编辑，其他列为不可编辑
                for col in [0, 2, 3, 4, 5, 6]:
                    item = self.pie_compounds_list.item(i, col)
                    if item:
                        item.setFlags(item.flags() & ~Qt.ItemFlag.ItemIsEditable)

            # 显示结果
            self.pie_result_text.setText(f"拟合失败: {str(e)}\n\n找到 {len(matching_compounds)} 个符合质量数 {target_mass:.4f} (±{tolerance}) 的化合物")

            # 如果只有一个匹配的化合物，自动选中它，但不触发更新
            if len(matching_compounds) == 1:
                self.pie_compounds_list.selectRow(0)
                # 移除 self.on_pie_compound_selected() 调用，避免重复更新

            # 在主窗口中显示PIE曲线
            self.show_pie_in_main_window(main_window, matching_compounds)
            
        # 最后的总异常处理
        except Exception as e:
            self.log_print(f"拟合PIE曲线时出错: {str(e)}", level=1)
            QMessageBox.critical(self, "错误", f"拟合PIE曲线时出错: {str(e)}")
            import traceback
            traceback.print_exc()

    def show_pie_in_main_window(self, main_window, matching_compounds, energy_points=None, y_exp_interp=None, y_fit=None):
        """在主窗口中显示PIE曲线

        参数:
            main_window: 主窗口实例
            matching_compounds: 匹配的化合物列表
            energy_points: 能量点（用于拟合曲线）
            y_exp_interp: 插值后的实验数据
            y_fit: 拟合结果
        """
        try:
            # 检查是否有匹配的化合物
            if not matching_compounds:
                print("没有匹配的化合物，无法显示PIE曲线")
                return

            # 检查主窗口对象
            if not main_window:
                print("主窗口对象为空，无法显示PIE曲线")
                return

            # 检查主窗口是否有results_widget属性
            if not hasattr(main_window, 'results_widget'):
                print("主窗口没有results_widget属性，无法显示PIE曲线")
                return

            # 获取结果组件
            results_widget = main_window.results_widget
            if not results_widget:
                print("无法获取主窗口的结果窗口部件")
                return

            # 检查主窗口是否有tabs属性
            if hasattr(main_window, 'tabs'):
                # 切换到结果页面
                main_window.tabs.setCurrentIndex(2)  # 结果页面索引为2
            else:
                print("主窗口没有tabs属性，无法切换到结果页面")

            # 检查results_widget是否有graphWidget属性
            if not hasattr(results_widget, 'graphWidget'):
                print("结果窗口部件没有graphWidget属性，无法显示PIE曲线")
                return

            # 检查results_widget是否有ionization_energy_markers属性
            if hasattr(results_widget, 'ionization_energy_markers'):
                # 保存当前的电离能标记
                saved_markers = results_widget.ionization_energy_markers.copy()
            else:
                print("结果窗口部件没有ionization_energy_markers属性，无法保存电离能标记")
                saved_markers = []

            # 确定是否需要保持坐标轴范围（使用记录的实验数据范围）
            original_x_range = None
            original_y_range = None
            keep_axis_range = False
            
            if hasattr(self, 'keep_axis_range_checkbox') and self.keep_axis_range_checkbox.isChecked():
                keep_axis_range = True
                # 使用记录的当前质量数的实验数据坐标范围
                if (hasattr(self, 'current_mass_for_axis') and self.current_mass_for_axis is not None and
                    hasattr(self, 'mass_axis_ranges') and self.current_mass_for_axis in self.mass_axis_ranges):
                    
                    stored_ranges = self.mass_axis_ranges[self.current_mass_for_axis]
                    original_x_range = stored_ranges['x_range']
                    original_y_range = stored_ranges['y_range']
                    print(f"使用质量数 {self.current_mass_for_axis:.4f} 记录的实验数据坐标范围: X={original_x_range}, Y={original_y_range}")
                else:
                    # 如果没有记录的范围，则使用当前坐标轴范围作为备选
                    try:
                        view_box = results_widget.graphWidget.getViewBox()
                        original_x_range = view_box.viewRange()[0]
                        original_y_range = view_box.viewRange()[1]
                        print(f"使用当前坐标轴范围作为备选: X={original_x_range}, Y={original_y_range}")
                    except Exception as e:
                        print(f"获取坐标轴范围时出错: {str(e)}")
                        keep_axis_range = False

            # 清除当前图表
            results_widget.graphWidget.clear()

            # 清除电离能标记列表，但保存其内容
            if hasattr(results_widget, 'ionization_energy_markers'):
                results_widget.ionization_energy_markers = []

            # 获取当前选中的峰和实验数据
            selected_peak = None
            peak_index = 0
            exp_x_data = None
            exp_y_data = None
            x_axis_label = "能量 (eV)"

            # 尝试获取实验数据
            try:
                # 获取当前选中的峰索引
                peak_index = -1
                selected_peak = None
                
                # 检查results_widget是否有getSelectedPeak方法
                if hasattr(results_widget, 'getSelectedPeak'):
                    selected_peak = results_widget.getSelectedPeak()
                    
                # 获取峰索引
                if hasattr(results_widget, 'chartCombo') and hasattr(results_widget, 'peaks'):
                    peak_index = results_widget.chartCombo.currentIndex()
                    if peak_index >= 0 and peak_index < len(results_widget.peaks):
                        if not selected_peak:  # 如果没有通过getSelectedPeak获取到峰，使用索引获取
                            selected_peak = results_widget.peaks[peak_index]
                    else:
                        peak_index = -1

                print(f"获取到峰索引: {peak_index}, 选中的峰: {selected_peak.get('formula', '') if selected_peak else 'None'}")

                # 获取实验数据（积分强度数据）
                if (selected_peak and peak_index >= 0 and hasattr(results_widget, 'results') and 
                    hasattr(results_widget, 'file_names') and hasattr(results_widget, 'file_metadata')):
                    
                    # 获取选中的横坐标类型
                    x_axis_type = "能量(eV)"  # 默认使用能量
                    if hasattr(results_widget, 'xAxisCombo'):
                        x_axis_type = results_widget.xAxisCombo.currentText()

                    # 获取实验数据的Y值（积分强度）
                    if isinstance(results_widget.results, dict):
                        # 字典格式的结果
                        y_values = []
                        for file_name in results_widget.file_names:
                            if file_name in results_widget.results:
                                result_data = results_widget.results[file_name]
                                if peak_index < result_data.shape[0]:
                                    y_values.append(float(result_data[peak_index, 0]))
                                else:
                                    y_values.append(0.0)
                            else:
                                y_values.append(0.0)
                        exp_y_data = np.array(y_values)
                    else:
                        # 数组格式的结果
                        exp_y_data = results_widget.results[:, peak_index]

                    # 获取实验数据的X值（根据横坐标类型）
                    if x_axis_type == "能量(eV)":
                        # 从文件元数据中获取能量值
                        x_values = []
                        for filename in results_widget.file_names:
                            energy = results_widget.file_metadata.get(filename, {}).get('energy')
                            x_values.append(energy if energy is not None else float('nan'))
                        
                        exp_x_data = np.array(x_values)
                        # 移除NaN值
                        mask = ~np.isnan(exp_x_data)
                        if np.any(mask):
                            exp_x_data = exp_x_data[mask]
                            exp_y_data = exp_y_data[mask]
                            x_axis_label = "能量 (eV)"
                        else:
                            # 如果所有值都是NaN，则使用文件索引
                            exp_x_data = np.arange(len(results_widget.file_names))
                            x_axis_label = "文件索引"
                    
                    elif x_axis_type == "温度(K)":
                        # 从文件元数据中获取温度值
                        x_values = []
                        for filename in results_widget.file_names:
                            temperature = results_widget.file_metadata.get(filename, {}).get('temperature')
                            x_values.append(temperature if temperature is not None else float('nan'))
                        
                        exp_x_data = np.array(x_values)
                        # 移除NaN值
                        mask = ~np.isnan(exp_x_data)
                        if np.any(mask):
                            exp_x_data = exp_x_data[mask]
                            exp_y_data = exp_y_data[mask]
                            x_axis_label = "温度 (K)"
                        else:
                            # 如果所有值都是NaN，则使用文件索引
                            exp_x_data = np.arange(len(results_widget.file_names))
                            x_axis_label = "文件索引"
                    
                    else:  # 默认使用文件索引
                        exp_x_data = np.arange(len(results_widget.file_names))
                        x_axis_label = "文件索引"

                    print(f"获取到实验数据: X轴类型={x_axis_type}, 数据点数={len(exp_x_data) if exp_x_data is not None else 0}")

            except Exception as e:
                print(f"获取实验数据时出错: {str(e)}")
                exp_x_data = None
                exp_y_data = None

            # 如果没有实验数据或能量点，使用默认能量范围
            if energy_points is None:
                if exp_x_data is not None and x_axis_label == "能量 (eV)":
                    # 使用实验数据的能量范围
                    energy_points = exp_x_data
                else:
                    # 使用默认能量范围（8-16 eV，100个点）
                    energy_points = np.linspace(8, 16, 100)
                    print("使用默认能量范围: 8-16 eV")

            # 设置图表标题和轴标签
            try:
                if selected_peak:
                    peak_formula = selected_peak.get('formula', '')
                    peak_mass = selected_peak.get('mass', 0.0)
                else:
                    peak_formula = ''
                    peak_mass = float(self.pie_mass_input.text()) if hasattr(self, 'pie_mass_input') else 0.0

                # 使用m/z值作为图表标题
                chart_title = f"m/z {peak_mass:.4f}" + (f" ({peak_formula})" if peak_formula else "")
                results_widget.graphWidget.setTitle(chart_title)
                results_widget.graphWidget.setLabel('left', '积分强度')
                results_widget.graphWidget.setLabel('bottom', x_axis_label)
            except Exception as e:
                print(f"设置图表标题和轴标签时出错: {str(e)}")
                results_widget.graphWidget.setTitle("PIE曲线分析")
                results_widget.graphWidget.setLabel('left', '积分强度')
                results_widget.graphWidget.setLabel('bottom', "能量 (eV)")

            # 首先绘制实验数据（如果有）
            if exp_x_data is not None and exp_y_data is not None and len(exp_x_data) > 0:
                print(f"绘制实验数据: {len(exp_x_data)} 个数据点")
                print(f"实验数据Y值范围: {np.min(exp_y_data):.6f} - {np.max(exp_y_data):.6f}")
                print(f"实验数据X值范围: {np.min(exp_x_data):.6f} - {np.max(exp_x_data):.6f}")
                
                # 确保实验数据没有被修改，创建副本
                exp_x_data_copy = exp_x_data.copy()
                exp_y_data_copy = exp_y_data.copy()
                
                pen = pg.mkPen(color='b', width=3)  # 蓝色，较粗的线
                results_widget.graphWidget.plot(exp_x_data_copy, exp_y_data_copy, 
                                               pen=pen, symbol='o', symbolSize=8, 
                                               symbolBrush='b', name='实验数据')
                
                print(f"实验数据绘制完成，Y值范围: {np.min(exp_y_data_copy):.6f} - {np.max(exp_y_data_copy):.6f}")

            # 对所有化合物进行统一横坐标内插
            print("开始对化合物PIE数据进行统一横坐标内插...")
            
            # 只处理系数大于0的化合物
            valid_compounds = []
            
            # 打印调试信息
            print(f"显示 {len(matching_compounds)} 个化合物的PIE曲线")
            for compound in matching_compounds:
                name = compound.get('name', '未知')
                weight = compound.get('weight', 0.0)
                coefficient = compound.get('coefficient', 0.0)
                print(f"  - {name}: 权重 = {weight:.10f}")
            
            for compound in matching_compounds:
                # 检查多个可能的权重字段
                weight = compound.get('weight', 0.0)
                coefficient = compound.get('coefficient', 0.0)
                # 优先使用coefficient，如果没有则使用weight
                effective_weight = coefficient if coefficient > 0 else weight
                
                if effective_weight > 0:
                    # 确保化合物有正确的权重字段
                    compound['weight'] = effective_weight
                    valid_compounds.append(compound)

            if not valid_compounds:
                print("没有权重大于0的化合物")
                # 如果没有系数大于0的化合物，仍然尝试显示所有传入的化合物
                print("尝试显示所有传入的化合物（权重设为默认值）")
                for compound in matching_compounds:
                    compound['weight'] = compound.get('weight', 1.0)  # 设置默认权重
                    valid_compounds.append(compound)

            # 创建插值数据列表
            interpolated_compounds = []
            total_pie_curve = np.zeros_like(energy_points)

            # 对每个化合物进行插值
            for compound in valid_compounds:
                pie_data = compound.get('pie_data', {})
                comp_energy = pie_data.get('energy', [])
                comp_pie = pie_data.get('pie', [])

                if not comp_energy or not comp_pie or len(comp_energy) != len(comp_pie):
                    print(f"化合物 {compound.get('name', '')} 的PIE数据不完整，跳过")
                    continue

                try:
                    # 导入插值函数
                    from scipy.interpolate import interp1d

                    # 确保数据是升序排列的
                    sorted_indices = np.argsort(comp_energy)
                    comp_energy_sorted = np.array(comp_energy)[sorted_indices]
                    comp_pie_sorted = np.array(comp_pie)[sorted_indices]

                    # 创建插值函数
                    pie_interp = interp1d(comp_energy_sorted, comp_pie_sorted,
                                        kind='linear', bounds_error=False, fill_value=0)
                    
                    # 插值到统一的能量点
                    y_pie_interp = pie_interp(energy_points)

                    # 获取权重系数
                    weight = compound.get('weight', 0.0)

                    # 应用权重系数（这里就是"离界面乘系数"）
                    y_pie_weighted = y_pie_interp * weight

                    # 添加到总的PIE曲线
                    total_pie_curve += y_pie_weighted

                    # 保存插值结果
                    interpolated_compounds.append({
                        'compound': compound,
                        'y_pie_interp': y_pie_interp,
                        'y_pie_weighted': y_pie_weighted,
                        'weight': weight
                    })

                    print(f"成功插值化合物 {compound.get('name', '')}, 权重: {weight:.6f}, PIE最大值: {np.max(y_pie_weighted):.6f}")

                except Exception as e:
                    print(f"插值化合物 {compound.get('name', '')} 的PIE数据时出错: {str(e)}")
                    continue

            # 计算缩放因子，使PIE曲线与实验数据在合适的比例范围内显示
            pie_scale_factor = 1.0
            if exp_x_data is not None and exp_y_data is not None and len(exp_x_data) > 0 and len(interpolated_compounds) > 0:
                exp_max = np.max(exp_y_data)
                pie_max = np.max(total_pie_curve)
                
                if pie_max > 0 and exp_max > 0:
                    # 计算比值
                    ratio = pie_max / exp_max
                    print(f"PIE曲线最大值与实验数据最大值比值: {ratio:.2f}")
                    
                    # 如果PIE曲线数值过大，进行缩放使其与实验数据在同一数量级
                    if ratio > 10:  # 如果PIE曲线比实验数据大10倍以上
                        # 缩放PIE曲线，使其最大值约为实验数据最大值的50%-80%
                        pie_scale_factor = exp_max * 0.6 / pie_max
                        print(f"应用PIE曲线缩放因子: {pie_scale_factor:.6f}")
                    elif ratio < 0.1:  # 如果PIE曲线比实验数据小10倍以上
                        # 放大PIE曲线，使其最大值约为实验数据最大值的50%-80%
                        pie_scale_factor = exp_max * 0.6 / pie_max
                        print(f"应用PIE曲线放大因子: {pie_scale_factor:.6f}")

            # 绘制各个化合物的加权PIE曲线
            colors = ['r', 'g', 'c', 'm', 'y', (255, 165, 0), (128, 0, 128), (0, 128, 0), (165, 42, 42)]
            
            for i, comp_data in enumerate(interpolated_compounds):
                compound = comp_data['compound']
                y_pie_weighted = comp_data['y_pie_weighted'] * pie_scale_factor  # 应用缩放因子
                weight = comp_data['weight']

                # 获取化合物信息
                name = compound.get('name', '')
                formula = compound.get('formula', '')

                # 选择颜色
                color_index = i % len(colors)
                color = colors[color_index]

                # 创建线条样式
                pen = pg.mkPen(color=color, width=2, style=Qt.PenStyle.DashLine)

                # 绘制加权PIE曲线（这里的weight实际是拟合系数）
                coefficient_str = f" (拟合系数: {weight:.4f})"
                results_widget.graphWidget.plot(energy_points, y_pie_weighted,
                                               pen=pen, name=f"{name} ({formula}){coefficient_str}")

            # 绘制总的拟合曲线（所有化合物的加权和）
            if len(interpolated_compounds) > 0:
                pen = pg.mkPen(color='k', width=3)  # 黑色，较粗的线
                total_pie_curve_scaled = total_pie_curve * pie_scale_factor  # 应用缩放因子
                results_widget.graphWidget.plot(energy_points, total_pie_curve_scaled,
                                               pen=pen, name='总拟合曲线')
                print(f"绘制总拟合曲线，原始最大值: {np.max(total_pie_curve):.6f}, 缩放后最大值: {np.max(total_pie_curve_scaled):.6f}")
                
                # 检查实验数据是否还在正确的范围内
                if exp_x_data is not None and exp_y_data is not None and len(exp_x_data) > 0:
                    print(f"绘制完成后，实验数据Y值范围确认: {np.min(exp_y_data):.6f} - {np.max(exp_y_data):.6f}")
                    
                    # 获取当前图表的Y轴范围
                    try:
                        y_range = results_widget.graphWidget.getViewBox().viewRange()[1]
                        print(f"当前图表Y轴范围: {y_range[0]:.6f} - {y_range[1]:.6f}")
                        
                        # 现在检查缩放后的比例
                        exp_max = np.max(exp_y_data)
                        pie_max_scaled = np.max(total_pie_curve_scaled)
                        if pie_max_scaled > 0 and exp_max > 0:
                            ratio_scaled = pie_max_scaled / exp_max
                            print(f"缩放后PIE曲线与实验数据最大值比值: {ratio_scaled:.2f}")
                                
                    except Exception as e:
                        print(f"获取Y轴范围时出错: {str(e)}")

            # 重新添加保存的电离能标记
            for marker in saved_markers:
                vLine, text = marker
                # 重新创建标记
                pos = vLine.value()
                text_content = text.textItem.toPlainText()

                # 获取原始标记的颜色
                try:
                    original_color = vLine.pen.color().name()
                except:
                    # 如果无法获取原始颜色，使用默认颜色
                    colors_marker = ['r', 'g', 'b', 'c', 'm', 'y', (255, 165, 0), (128, 0, 128), (0, 128, 0), (165, 42, 42)]
                    color_index = len(results_widget.ionization_energy_markers) % len(colors_marker)
                    original_color = colors_marker[color_index]

                # 创建新的垂直线
                new_vLine = pg.InfiniteLine(pos=pos, angle=90, pen=pg.mkPen(original_color, width=2, style=Qt.PenStyle.DashLine))
                results_widget.graphWidget.addItem(new_vLine)

                # 获取当前图表的Y轴范围
                y_range = results_widget.graphWidget.getViewBox().viewRange()[1]
                y_max = y_range[1]
                y_pos = y_max * 0.9

                # 创建新的文本标签
                new_text = pg.TextItem(text_content, color=original_color)
                new_text.setPos(pos, y_pos)
                results_widget.graphWidget.addItem(new_text)

                # 将新标记添加到列表
                results_widget.ionization_energy_markers.append((new_vLine, new_text))

            # 添加图例
            try:
                # 先移除现有的图例（如果有）
                if hasattr(results_widget.graphWidget, 'legend') and results_widget.graphWidget.legend is not None:
                    results_widget.graphWidget.legend.scene().removeItem(results_widget.graphWidget.legend)
                    results_widget.graphWidget.legend = None

                # 添加新的图例
                results_widget.graphWidget.addLegend()
                print("成功添加图例")
            except Exception as e:
                print(f"添加图例时出错: {str(e)}")

            # 处理坐标轴范围（根据开关状态）
            if keep_axis_range and original_x_range is not None and original_y_range is not None:
                # 恢复原始坐标轴范围
                try:
                    view_box = results_widget.graphWidget.getViewBox()
                    view_box.setRange(xRange=original_x_range, yRange=original_y_range)
                    print(f"已恢复原始坐标轴范围: X={original_x_range}, Y={original_y_range}")
                except Exception as e:
                    print(f"恢复原始坐标轴范围时出错: {str(e)}")
            else:
                # 如果关闭了保持坐标轴范围选项，让图表自动调整到最适合的范围
                try:
                    view_box = results_widget.graphWidget.getViewBox()
                    view_box.autoRange()  # 自动调整到最适合的范围
                    print("已设置坐标轴自动调整范围")
                except Exception as e:
                    print(f"设置坐标轴自动调整范围时出错: {str(e)}")

            # 显示状态消息
            if hasattr(main_window, 'statusBar'):
                try:
                    axis_info = " (保持原始坐标轴范围)" if keep_axis_range else ""
                    main_window.statusBar.showMessage(f"已显示PIE分析结果: 质量数 {peak_mass:.4f}, 找到 {len(valid_compounds)} 个有效化合物{axis_info}")
                except Exception as e:
                    print(f"显示状态消息时出错: {str(e)}")

            print(f"PIE曲线显示完成: 实验数据点数={len(exp_x_data) if exp_x_data is not None else 0}, 化合物数={len(valid_compounds)}")

        except Exception as e:
            print(f"在主窗口显示PIE曲线时出错: {str(e)}")
            QMessageBox.critical(self, "错误", f"在主窗口显示PIE曲线时出错: {str(e)}")
            import traceback
            traceback.print_exc()

    def clear_all_ionization_energies(self):
        """清除所有电离能标记"""
        # 首先检查当前选中的主标签页
        current_main_tab_index = self.main_tab_widget.currentIndex()

        # 如果当前是PIE查询标签页
        if current_main_tab_index == 0:  # PIE查询标签页
            try:
                # 清除电离截面数据表格
                self.pie_data_table.setRowCount(0)
                self.pie_result_text.clear()

                # 清空化合物列表
                self.pie_compounds_list.setRowCount(0)

                # 显示成功消息
                QMessageBox.information(self, "成功", "已清除PIE分析结果")
            except Exception as e:
                print(f"清除PIE分析结果时出错: {str(e)}")
                import traceback
                traceback.print_exc()
            return

        # 如果当前是电离能查询标签页
        elif current_main_tab_index == 1:  # 电离能查询标签页
            try:
                # 导入信号模块
                from .compound_dialog import compound_dialog_signals

                # 发送信号清除所有电离能标记
                # 我们使用特殊值-1表示清除所有
                compound_dialog_signals.ionization_energy_selected.emit(-1, "clear_all")

                # 显示成功消息
                QMessageBox.information(self, "成功", "已清除所有电离能标记")
            except Exception as e:
                print(f"清除电离能标记时出错: {str(e)}")
                import traceback
                traceback.print_exc()

    def on_energetics_cell_clicked(self, row, column):
        """当用户点击电离能表格时触发

        参数:
            row: 行索引
            column: 列索引
        """
        # 只处理电离能列的点击
        if column != 0:
            return

        # 获取电离能值
        ie_item = self.energetics_table.item(row, column)
        if not ie_item:
            return

        ie_text = ie_item.text()
        if not ie_text:
            return

        try:
            # 处理电离能值，可能包含不确定度（如"8.05±0.17"）
            ie_value = self.parse_ionization_energy(ie_text)

            # 获取化合物名称
            compound_name = ""
            current_item = self.compounds_list.currentItem()
            if current_item:
                compound_name = current_item.text()

            # 发送信号
            if ie_value is not None:
                from .compound_dialog import compound_dialog_signals
                compound_dialog_signals.ionization_energy_selected.emit(ie_value, compound_name)
                print(f"选中电离能值: {ie_value} eV, 化合物: {compound_name}")
        except Exception as e:
            print(f"处理电离能值时出错: {str(e)}")

    def show_energetics_context_menu(self, pos):
        """显示电离能表格的右键菜单

        参数:
            pos: 鼠标位置
        """
        # 直接获取点击位置的行和列
        row = self.energetics_table.rowAt(pos.y())
        column = self.energetics_table.columnAt(pos.x())

        # 打印调试信息
        print(f"右键点击位置: ({pos.x()}, {pos.y()}), 行: {row}, 列: {column}")

        # 检查行和列是否有效
        if row < 0 or column < 0:
            print(f"无效的行或列: {row}, {column}")
            return

        # 只处理电离能列
        if column != 0:
            print(f"非电离能列: {column}")
            return

        # 获取电离能值
        ie_item = self.energetics_table.item(row, column)
        if not ie_item or not ie_item.text():
            print(f"无法获取电离能值或为空: 行={row}, 列={column}")
            return

        print(f"获取到电离能值: {ie_item.text()}")

        # 创建菜单
        menu = QMenu(self)

        # 解析电离能值
        ie_text = ie_item.text()
        ie_value = self.parse_ionization_energy(ie_text)

        if ie_value is not None:
            # 获取化合物名称
            current_item = self.compounds_list.currentItem()
            compound_name = current_item.text() if current_item else ""

            # 添加绘制电离能菜单项
            plot_action = menu.addAction(f"在结果图上绘制电离能值: {ie_value:.2f} eV ({compound_name})")
            plot_action.triggered.connect(lambda: self.on_energetics_cell_double_clicked(row, column))

            # 添加复制电离能值菜单项
            copy_action = menu.addAction(f"复制电离能值: {ie_value:.2f}")
            copy_action.triggered.connect(lambda: QApplication.clipboard().setText(f"{ie_value:.6f}"))

            # 显示菜单
            global_pos = self.energetics_table.viewport().mapToGlobal(pos)
            print(f"将显示菜单在全局位置: {global_pos.x()}, {global_pos.y()}")
            menu.exec(global_pos)

    def parse_ionization_energy(self, ie_text):
        """解析电离能值，处理可能的不确定度

        参数:
            ie_text: 电离能文本，如"8.05"或"8.05±0.17"

        返回:
            浮点数电离能值，如果无法解析则返回None
        """
        try:
            # 如果包含"±"符号，取前面的值
            if "±" in ie_text:
                ie_value = float(ie_text.split("±")[0])
                return ie_value

            # 如果包含"≤"或"≥"符号，取后面的值
            if "≤" in ie_text:
                ie_value = float(ie_text.replace("≤", "").strip())
                return ie_value
            if "≥" in ie_text:
                ie_value = float(ie_text.replace("≥", "").strip())
                return ie_value

            # 如果包含范围，取平均值
            if "-" in ie_text and ie_text.count(".") > 1:
                parts = ie_text.split("-")
                if len(parts) == 2:
                    try:
                        min_val = float(parts[0].strip())
                        max_val = float(parts[1].strip())
                        return (min_val + max_val) / 2
                    except ValueError:
                        pass

            # 直接转换为浮点数
            return float(ie_text)
        except ValueError:
            # 如果无法转换为浮点数，返回None
            return None

    def get_first_ionization_energy(self, compound):
        """获取化合物的第一个电离能值

        参数:
            compound: 化合物信息字典

        返回:
            第一个电离能值的字符串，如果没有则返回"无"
        """
        try:
            # 首先尝试从compounds_data.json的ionization_energy字段获取
            ionization_energy = compound.get('ionization_energy', None)
            if ionization_energy and ionization_energy != "":
                return str(ionization_energy)
            
            # 如果没有，再尝试从NIST数据库的ion_energetics字段获取
            ion_energetics = compound.get('ion_energetics', None)
            if ion_energetics:
                ie_values = ion_energetics.get('ie_values', [])
                if ie_values and len(ie_values) > 0:
                    first_ie = ie_values[0]
                    if first_ie is not None and first_ie != "":
                        return str(first_ie)

            return "无"
        except Exception as e:
            print(f"获取电离能时出错: {str(e)}")
            return "无"

    def apply_recorded_axis_range(self):
        """应用记录的坐标轴范围到主窗口"""
        try:
            # 检查是否有当前质量数和记录的范围
            if (not hasattr(self, 'current_mass_for_axis') or self.current_mass_for_axis is None or
                not hasattr(self, 'mass_axis_ranges') or self.current_mass_for_axis not in self.mass_axis_ranges):
                print("没有记录的坐标轴范围可应用")
                return
            
            # 获取记录的范围
            recorded_range = self.mass_axis_ranges[self.current_mass_for_axis]
            x_range = recorded_range.get('x_range')
            y_range = recorded_range.get('y_range')
            
            if not x_range or not y_range:
                print("记录的坐标轴范围数据不完整")
                return
            
            # 查找主窗口
            main_window = None
            for widget in QApplication.topLevelWidgets():
                if widget.__class__.__name__ == 'MainWindow':
                    main_window = widget
                    break
            
            if not main_window or not hasattr(main_window, 'results_widget'):
                print("无法找到主窗口或结果组件")
                return
            
            # 应用坐标轴范围
            try:
                view_box = main_window.results_widget.graphWidget.getViewBox()
                view_box.setRange(xRange=x_range, yRange=y_range, padding=0)
                print(f"已应用质量数 {self.current_mass_for_axis:.4f} 的记录坐标范围: X={x_range}, Y={y_range}")
            except Exception as e:
                print(f"设置坐标轴范围时出错: {str(e)}")
                
        except Exception as e:
            print(f"应用记录坐标轴范围时出错: {str(e)}")

    def delayed_update(self):
        """延迟执行更新操作，包括更新主窗口绘图和重新排序化合物列表"""
        try:
            # 先更新主窗口绘图
            self.update_main_window_plot()

            # 然后按拟合系数重新排序化合物列表
            self.sort_compounds_by_coefficient()

            print("已完成延迟更新操作")
        except Exception as e:
            print(f"延迟更新操作时出错: {str(e)}")
            import traceback
            traceback.print_exc()

    def update_main_window_plot(self):
        """根据当前化合物列表的拟合系数更新主窗口绘图"""
        try:
            # 查找主窗口
            main_window = None
            for widget in QApplication.topLevelWidgets():
                if widget.__class__.__name__ == 'MainWindow':
                    main_window = widget
                    break

            if not main_window:
                for widget in QApplication.topLevelWidgets():
                    if (hasattr(widget, 'windowTitle') and
                        "质谱数据分析" in widget.windowTitle() and
                        "化合物查询" not in widget.windowTitle()):
                        main_window = widget
                        break

            if not main_window:
                for widget in QApplication.topLevelWidgets():
                    if (widget != self and  # 排除当前窗口
                        hasattr(widget, 'tabs') and
                        hasattr(widget, 'results_widget')):
                        main_window = widget
                        break

            if not main_window:
                print("无法找到主窗口，无法更新绘图")
                return

            # 检查是否需要保持坐标轴范围（使用记录的实验数据范围）
            keep_axis_range = False
            original_x_range = None
            original_y_range = None
            
            if hasattr(self, 'keep_axis_range_checkbox') and self.keep_axis_range_checkbox.isChecked():
                keep_axis_range = True
                # 使用记录的当前质量数的实验数据坐标范围
                if (hasattr(self, 'current_mass_for_axis') and self.current_mass_for_axis is not None and
                    hasattr(self, 'mass_axis_ranges') and self.current_mass_for_axis in self.mass_axis_ranges):
                    
                    stored_ranges = self.mass_axis_ranges[self.current_mass_for_axis]
                    original_x_range = stored_ranges['x_range']
                    original_y_range = stored_ranges['y_range']
                    print(f"使用质量数 {self.current_mass_for_axis:.4f} 记录的实验数据坐标范围: X={original_x_range}, Y={original_y_range}")
                else:
                    # 如果没有记录的范围，则使用当前坐标轴范围作为备选
                    try:
                        # 获取主窗口的results_widget
                        if hasattr(main_window, 'results_widget'):
                            results_widget = main_window.results_widget
                            view_box = results_widget.graphWidget.getViewBox()
                            original_x_range = view_box.viewRange()[0]
                            original_y_range = view_box.viewRange()[1]
                            print(f"使用当前坐标轴范围作为备选: X={original_x_range}, Y={original_y_range}")
                    except Exception as e:
                        print(f"获取坐标轴范围时出错: {str(e)}")
                        keep_axis_range = False

            # 获取所有化合物，包括权重为0的化合物
            all_compounds = []
            for i in range(self.pie_compounds_list.rowCount()):
                compound_item = self.pie_compounds_list.item(i, 2)  # 化合物名称列
                weight_item = self.pie_compounds_list.item(i, 1)   # 权重系数列
                if compound_item and weight_item:
                    compound = compound_item.data(Qt.ItemDataRole.UserRole)
                    if compound:
                        # 确保从表格中获取最新的权重系数
                        try:
                            current_weight = float(weight_item.text())
                            compound['weight'] = current_weight
                        except (ValueError, AttributeError):
                            compound['weight'] = 0.0
                        all_compounds.append(compound)

            # 获取所有权重大于0的化合物
            compounds_to_show = []
            for compound in all_compounds:
                # 只显示权重大于0的化合物
                weight = compound.get('weight', 0.0)
                if weight > 0:
                    compounds_to_show.append(compound)

            if not compounds_to_show:
                print("没有权重大于0的化合物，无法更新绘图")
                return

            # 打印调试信息
            print(f"更新主窗口绘图，显示 {len(compounds_to_show)} 个化合物")
            for compound in compounds_to_show:
                print(f"  - {compound.get('name', '')}: 权重 = {compound.get('weight', 0.0):.10f}")

            # 获取当前选中的峰
            selected_peak = None

            # 检查主窗口是否有results_widget属性
            if hasattr(main_window, 'results_widget'):
                results_widget = main_window.results_widget

                # 检查results_widget是否有getSelectedPeak方法
                if hasattr(results_widget, 'getSelectedPeak'):
                    selected_peak = results_widget.getSelectedPeak()
                    if not selected_peak:
                        print("没有选中的峰，使用默认峰对象")
                        # 创建一个默认峰对象
                        selected_peak = {
                            'mass': float(self.pie_mass_input.text()) if hasattr(self, 'pie_mass_input') else 0.0,
                            'formula': '',
                            'data': []  # 空数据，后面会使用默认能量范围
                        }
                # 如果没有getSelectedPeak方法，尝试使用chartCombo获取峰索引
                elif hasattr(results_widget, 'chartCombo') and hasattr(results_widget, 'peaks'):
                    peak_index = results_widget.chartCombo.currentIndex()
                    if peak_index < 0 or peak_index >= len(results_widget.peaks):
                        print("无效的峰索引，使用默认峰对象")
                        # 创建一个默认峰对象
                        selected_peak = {
                            'mass': float(self.pie_mass_input.text()) if hasattr(self, 'pie_mass_input') else 0.0,
                            'formula': '',
                            'data': []  # 空数据，后面会使用默认能量范围
                        }
                    else:
                        selected_peak = results_widget.peaks[peak_index]
                else:
                    print("无法获取选中的峰，使用默认峰对象")
                    # 创建一个默认峰对象
                    selected_peak = {
                        'mass': float(self.pie_mass_input.text()) if hasattr(self, 'pie_mass_input') else 0.0,
                        'formula': '',
                        'data': []  # 空数据，后面会使用默认能量范围
                    }
            else:
                print("主窗口没有results_widget属性，使用默认峰对象")
                # 创建一个默认峰对象
                selected_peak = {
                    'mass': float(self.pie_mass_input.text()) if hasattr(self, 'pie_mass_input') else 0.0,
                    'formula': '',
                    'data': []  # 空数据，后面会使用默认能量范围
                }

                # 创建一个临时的results_widget对象
                from PyQt6.QtWidgets import QWidget
                from pyqtgraph import PlotWidget

                results_widget = QWidget()
                results_widget.graphWidget = PlotWidget()

            # 获取峰的数据
            peak_data = selected_peak.get('data', [])
            if not peak_data:
                print("选中的峰没有数据，使用默认能量范围")
                # 使用默认能量范围（8-16 eV，100个点）
                energy = np.linspace(8, 16, 100)
            else:
                # 提取能量数据（我们只需要能量点）
                energy = [point[0] for point in peak_data]



            # 创建共同的能量点
            energy_points = np.array(energy)

            # 我们不需要在这里设置x_axis_type，因为它只在show_pie_in_main_window方法中使用

            # 对每个化合物的PIE数据进行插值
            compound_interp_data = []
            for compound in compounds_to_show:
                pie_data = compound.get('pie_data', {})
                comp_energy = pie_data.get('energy', [])
                comp_pie = pie_data.get('pie', [])

                if comp_energy and comp_pie and len(comp_energy) == len(comp_pie):
                    # 创建插值函数
                    try:
                        from scipy.interpolate import interp1d
                        # 确保数据是升序排列的
                        sorted_indices = np.argsort(comp_energy)
                        comp_energy_sorted = np.array(comp_energy)[sorted_indices]
                        comp_pie_sorted = np.array(comp_pie)[sorted_indices]

                        # 创建插值函数
                        pie_interp = interp1d(comp_energy_sorted, comp_pie_sorted,
                                            kind='linear', bounds_error=False, fill_value=0)
                        y_pie_interp = pie_interp(energy_points)

                        # 获取权重系数
                        weight = compound.get('weight', 0.0)

                        # 不在这里归一化PIE数据，而是保留原始数据
                        # 这样权重系数才能正确地反映每个化合物的贡献

                        # 添加到插值数据列表
                        compound_interp_data.append((compound, y_pie_interp, weight))
                    except Exception as e:
                        print(f"插值化合物 {compound.get('name', '')} 的PIE数据时出错: {str(e)}")

            # 检查是否有足够的插值数据
            if not compound_interp_data:
                print("没有化合物有有效的插值PIE数据")
                return

            # 计算总的拟合曲线
            y_fit = np.zeros_like(energy_points)

            # 使用未归一化的PIE数据和原始权重系数
            normalized_pie_data = []
            for compound, y_pie_interp, weight in compound_interp_data:
                # 确保权重大于0
                if weight > 0:
                    # 不对PIE数据进行归一化，保留原始数据
                    # 应用权重系数
                    y_pie_weighted = y_pie_interp * weight

                    # 添加到数据列表
                    normalized_pie_data.append((compound, y_pie_interp, y_pie_weighted, weight))

                    # 累加到总的拟合曲线
                    y_fit += y_pie_weighted

            # 不对拟合结果进行归一化，保留原始系数的影响
            # 这样可以确保权重系数反映的是真实的贡献比例

            # 打印调试信息
            print(f"总拟合曲线最大值: {np.max(y_fit)}")

            # 在主窗口中显示PIE曲线
            # 传递化合物列表和相关数据，确保权重信息正确传递
            self.show_pie_in_main_window(main_window, compounds_to_show, energy_points, None, y_fit)
            print(f"已更新主窗口绘图，显示 {len(compounds_to_show)} 个化合物和总拟合曲线")

            # 恢复坐标轴范围（如果需要）
            if keep_axis_range and original_x_range is not None and original_y_range is not None:
                try:
                    if hasattr(main_window, 'results_widget'):
                        results_widget = main_window.results_widget
                        view_box = results_widget.graphWidget.getViewBox()
                        view_box.setRange(xRange=original_x_range, yRange=original_y_range)
                        print(f"已恢复原始坐标轴范围: X={original_x_range}, Y={original_y_range}")
                except Exception as e:
                    print(f"恢复原始坐标轴范围时出错: {str(e)}")

        except Exception as e:
            print(f"更新主窗口绘图时出错: {str(e)}")
            import traceback
            traceback.print_exc()

    def sort_compounds_by_coefficient(self):
        """按拟合系数从大到小排序化合物列表"""
        try:
            # 获取所有行的数据
            rows_data = []
            for i in range(self.pie_compounds_list.rowCount()):
                row_data = {}
                # 获取勾选状态
                checkbox = self.pie_compounds_list.item(i, 0)
                if checkbox:
                    row_data['checked'] = checkbox.checkState() == Qt.CheckState.Checked

                # 获取拟合系数
                weight_item = self.pie_compounds_list.item(i, 1)
                if weight_item:
                    try:
                        row_data['coefficient'] = float(weight_item.text())
                    except ValueError:
                        row_data['coefficient'] = 0.0
                else:
                    row_data['coefficient'] = 0.0

                # 获取化合物数据
                compound_item = self.pie_compounds_list.item(i, 2)
                if compound_item:
                    row_data['compound'] = compound_item.data(Qt.ItemDataRole.UserRole)
                    row_data['name'] = compound_item.text()

                # 获取分子式
                formula_item = self.pie_compounds_list.item(i, 3)
                if formula_item:
                    row_data['formula'] = formula_item.text()

                # 获取质量数
                mass_item = self.pie_compounds_list.item(i, 4)
                if mass_item:
                    row_data['mass'] = mass_item.text()

                # 获取电离能
                ie_item = self.pie_compounds_list.item(i, 5)
                if ie_item:
                    row_data['ie'] = ie_item.text()

                # 获取来源
                source_item = self.pie_compounds_list.item(i, 6)
                if source_item:
                    row_data['source'] = source_item.text()

                rows_data.append((i, row_data))

            # 按拟合系数从大到小排序
            rows_data.sort(key=lambda x: x[1].get('coefficient', 0.0), reverse=True)

            # 临时禁用cellChanged信号，避免排序过程中触发更新
            self.pie_compounds_list.cellChanged.disconnect(self.on_pie_compound_cell_changed)

            # 重新排列表格
            for new_row, (old_row, data) in enumerate(rows_data):
                # 移动行
                if new_row != old_row:
                    # 创建新行的所有单元格
                    checkbox = QTableWidgetItem()
                    checkbox.setCheckState(Qt.CheckState.Checked if data.get('checked', False) else Qt.CheckState.Unchecked)

                    weight_item = QTableWidgetItem(str(data.get('coefficient', 0.0)))

                    name_item = QTableWidgetItem(data.get('name', ''))
                    name_item.setData(Qt.ItemDataRole.UserRole, data.get('compound'))

                    formula_item = QTableWidgetItem(data.get('formula', ''))
                    mass_item = QTableWidgetItem(data.get('mass', ''))
                    ie_item = QTableWidgetItem(data.get('ie', '无'))
                    source_item = QTableWidgetItem(data.get('source', ''))

                    # 设置新行的单元格
                    self.pie_compounds_list.setItem(new_row, 0, checkbox)
                    self.pie_compounds_list.setItem(new_row, 1, weight_item)
                    self.pie_compounds_list.setItem(new_row, 2, name_item)
                    self.pie_compounds_list.setItem(new_row, 3, formula_item)
                    self.pie_compounds_list.setItem(new_row, 4, mass_item)
                    self.pie_compounds_list.setItem(new_row, 5, ie_item)
                    self.pie_compounds_list.setItem(new_row, 6, source_item)

                    # 设置权重系数列为可编辑，其他列为不可编辑
                    for col in [0, 2, 3, 4, 5, 6]:
                        item = self.pie_compounds_list.item(new_row, col)
                        if item:
                            item.setFlags(item.flags() & ~Qt.ItemFlag.ItemIsEditable)

            # 重新连接cellChanged信号
            self.pie_compounds_list.cellChanged.connect(self.on_pie_compound_cell_changed)

            print("化合物列表已按拟合系数从大到小排序")
        except Exception as e:
            print(f"排序化合物列表时出错: {str(e)}")
            import traceback
            traceback.print_exc()

    def select_best_pie_sources(self, name_sources, target_mass):
        """为每个化合物选择最佳的PIE数据源
        
        Args:
            name_sources: 字典，键为化合物名称，值为该名称下的所有数据源列表
            target_mass: 目标质量数
            
        Returns:
            优化后的化合物列表，每个化合物名称只保留最佳数据源
        """
        optimized_compounds = []
        
        for name, sources in name_sources.items():
            if not sources:
                continue
                
            # 为每个数据源计算评分
            scored_sources = []
            
            for compound in sources:
                score = 0
                
                # 1. 质量数匹配度 (40%)
                mass_diff = abs(compound.get('mol_weight', 0) - target_mass)
                mass_score = max(0, 1 - mass_diff / 0.1)  # 0.1是容差基准
                score += mass_score * 0.4
                
                # 2. PIE数据质量 (30%)
                pie_data = compound.get('pie_data', {})
                energy = pie_data.get('energy', [])
                pie = pie_data.get('pie', [])
                
                if energy and pie and len(energy) == len(pie):
                    # 数据点数量
                    data_points = len(energy)
                    points_score = min(1.0, data_points / 50)  # 50个点为满分
                    
                    # 能量范围覆盖度
                    energy_range = max(energy) - min(energy)
                    range_score = min(1.0, energy_range / 10)  # 10eV为满分
                    
                    # PIE数据的动态范围
                    pie_array = np.array(pie)
                    if np.max(pie_array) > 0:
                        dynamic_range = np.max(pie_array) / (np.mean(pie_array) + 1e-10)
                        dynamic_score = min(1.0, dynamic_range / 5)  # 5倍动态范围为满分
                    else:
                        dynamic_score = 0
                    
                    pie_quality = (points_score + range_score + dynamic_score) / 3
                else:
                    pie_quality = 0
                
                score += pie_quality * 0.3
                
                # 3. 数据源优先级 (20%)
                source = compound.get('source', '').lower()
                if 'nist' in source:
                    source_score = 1.0
                elif 'webbook' in source:
                    source_score = 0.9
                elif 'literature' in source or 'lit' in source:
                    source_score = 0.8
                else:
                    source_score = 0.5
                
                score += source_score * 0.2
                
                # 4. 完整性检查 (10%)
                completeness = 0
                if compound.get('formula'):
                    completeness += 0.25
                if compound.get('name'):
                    completeness += 0.25
                if compound.get('mol_weight'):
                    completeness += 0.25
                if pie_data:
                    completeness += 0.25
                
                score += completeness * 0.1
                
                scored_sources.append((score, compound))
            
            # 选择评分最高的数据源
            if scored_sources:
                scored_sources.sort(key=lambda x: x[0], reverse=True)
                best_score, best_compound = scored_sources[0]
                
                print(f"化合物 {name}: 选择评分最高的数据源 (评分: {best_score:.3f}, 来源: {best_compound.get('source', '未知')})")
                optimized_compounds.append(best_compound)
        
        print(f"优化完成，从 {sum(len(sources) for sources in name_sources.values())} 个数据源中选择了 {len(optimized_compounds)} 个最佳数据源")
        return optimized_compounds

    def generate_multiple_solutions(self, matching_compounds, y_exp_interp, energy_points, 
                                  max_solutions=5, fitting_method='nnls', weights=None):
        """生成多个拟合方案，参考pie_analysis.py算法避免同时包含"物质"和"物质_Total"
        
        Args:
            matching_compounds: 匹配的化合物列表
            y_exp_interp: 插值后的实验数据
            energy_points: 能量点数组
            max_solutions: 最大方案数
            fitting_method: 拟合方法
            
        Returns:
            List of tuples: [(compounds, fit_result, metrics), ...]
        """
        from scipy.optimize import nnls
        from collections import defaultdict
        import copy
        import numpy as np
        
        print(f"\n🔬 开始基于pie_analysis算法的多方案生成...")
        
        # 1. 获取化合物基础名称的函数（参考pie_analysis.py）
        def get_canonical_name(name):
            """去除_Total和_fragment等后缀，保留本体名"""
            import re
            name = re.sub(r'_Total$', '', name, flags=re.IGNORECASE)
            name = re.sub(r'_fragment.*$', '', name, flags=re.IGNORECASE)
            return name
        
        # 2. 准备所有化合物数据源（类似pie_analysis.py的interpolated_data）
        interpolated_data = {}
        keys = []
        
        for i, compound in enumerate(matching_compounds):
            pie_data = compound.get('pie_data', {})
            if not pie_data:
                continue
                
            try:
                energy_data = np.array(pie_data.get('energy', []))
                pie_values = np.array(pie_data.get('pie', []))
                
                if len(energy_data) < 2 or len(pie_values) < 2:
                    continue
                
                # 插值到统一能量网格
                from scipy.interpolate import interp1d
                f_interp = interp1d(energy_data, pie_values, 
                                  bounds_error=False, fill_value=0.0)
                pie_interp = f_interp(energy_points)
                
                # 创建唯一键
                key = f"{compound['name']}_{compound.get('source', 'unknown')}_{i}"
                keys.append(key)
                
                interpolated_data[key] = {
                    'name': compound['name'],
                    'formula': compound['formula'],
                    'mass_number': compound['mass_number'],
                    'interpolated_pie': pie_interp,
                    'source': compound.get('source', '未知来源')
                }
                
            except Exception as e:
                print(f"  处理化合物 {compound.get('name', '未知')} 的PIE数据时出错: {str(e)}")
        
        if not interpolated_data:
            print("没有找到有效的PIE数据源")
            return []
        
        n = len(keys)
        print(f"📊 总共找到 {n} 个PIE数据源")
        
        # 3. 构建数据矩阵（参考pie_analysis.py）
        A = np.zeros((len(y_exp_interp), n))
        for i, key in enumerate(keys):
            A[:, i] = interpolated_data[key]['interpolated_pie']
        
        # 确保所有数据都是有限值
        A = np.nan_to_num(A, nan=0.0)
        y_exp_interp = np.nan_to_num(y_exp_interp, nan=0.0)
        
        # 应用能量区间重要性权重（如果提供了权重数组）
        if weights is not None:
            print(f"✅ 应用【能量区间重要性权重】到NNLS拟合过程...")
            # 显示权重统计信息
            unique_weights = np.unique(weights)
            print(f"   权重值分布: {unique_weights}")
            print(f"   平均权重: {np.mean(weights):.3f}")
            A_weighted = A * weights[:, np.newaxis]
            y_exp_interp_weighted = y_exp_interp * weights
        else:
            print("⚠️  未提供能量区间重要性权重，使用等权重拟合")
            A_weighted = A
            y_exp_interp_weighted = y_exp_interp
        
        # 4. 首先使用所有数据进行拟合（与pie_analysis.py完全一致）
        print("\n尝试使用所有可能的化合物组合...")
        coefficients, residuals = nnls(A_weighted, y_exp_interp_weighted)
        
        # 🔑 关键：用原始未加权的A矩阵计算拟合结果（pie_analysis.py的标准做法）
        fitted_pie = np.dot(A, coefficients)
        metrics = self.calculate_comprehensive_metrics(y_exp_interp, fitted_pie, energy_points)
        
        print(f"✅ 标准NNLS拟合完成:")
        print(f"   残差: {residuals:.6f}")
        print(f"   非零系数数量: {np.sum(coefficients > 1e-6)}")
        
        # 显示重要系数的值（应该是正确的量级）
        for i, coef in enumerate(coefficients):
            if coef > 1e-6:
                name = interpolated_data[keys[i]]['name']
                print(f"   {name}: 系数 = {coef:.6f}")
        
        # 5. 构建解决方案，按化合物归一名分组（避免同时包含"物质"和"物质_Total"）
        compound_sources = defaultdict(list)  # 用于存储每个归一名的所有来源
        for i, key in enumerate(keys):
            if coefficients[i] > 1e-6:  # 只考虑系数显著的化合物
                name = interpolated_data[key]['name']
                canonical_name = get_canonical_name(name)
                compound_sources[canonical_name].append({
                    'key': key,
                    'name': name,
                    'coefficient': coefficients[i],
                    'source': interpolated_data[key]['source']
                })
        
        # 6. 构建最优解（每个归一名只选一个来源，选系数最大的）
        best_solution = []
        for canonical_name, sources in compound_sources.items():
            best_source = max(sources, key=lambda x: x['coefficient'])
            key = best_source['key']
            best_solution.append({
                'key': key,
                'name': best_source['name'],
                'formula': interpolated_data[key]['formula'],
                'mass': interpolated_data[key]['mass_number'],
                'coefficient': best_source['coefficient'],
                'source': best_source['source']
            })
        
        # 按系数大小排序
        best_solution.sort(key=lambda x: x['coefficient'], reverse=True)
        
        # 限制最多使用的化合物数，并重新拟合
        max_compounds = 5
        if len(best_solution) > max_compounds:
            best_solution = best_solution[:max_compounds]
        
        # 用当前best_solution重新拟合
        selected_keys = [s['key'] for s in best_solution]
        selected_indices = [keys.index(key) for key in selected_keys]
        A_selected = A[:, selected_indices]
        
        # 应用权重到选中的化合物矩阵（遵循pie_analysis.py标准）
        if weights is not None:
            A_selected_weighted = A_selected * weights[:, np.newaxis]
            coefficients_sel, residuals_sel = nnls(A_selected_weighted, y_exp_interp_weighted)
        else:
            coefficients_sel, residuals_sel = nnls(A_selected, y_exp_interp)
            
        fitted_pie_sel = np.dot(A_selected, coefficients_sel)
        metrics_sel = self.calculate_comprehensive_metrics(y_exp_interp, fitted_pie_sel, energy_points)
        
        # 更新solution中的系数
        for i, coef in enumerate(coefficients_sel):
            best_solution[i]['coefficient'] = coef
        
        # 转换为需要的格式
        solution_compounds = []
        for comp_info in best_solution:
            solution_compounds.append({
                'compound': {
                    'name': comp_info['name'],
                    'formula': comp_info['formula'],
                    'mass': comp_info['mass'],
                    'source': comp_info['source']
                },
                'coefficient': comp_info['coefficient'],
                'name': comp_info['name'],
                'formula': comp_info['formula'],
                'mass': comp_info['mass'],
                'source': comp_info['source']
            })
        
        solutions = [(solution_compounds, fitted_pie_sel, metrics_sel)]
        
        # 7. 寻找替代方案，考虑不同来源（参考pie_analysis.py）
        print("\n寻找替代方案，考虑不同来源...")
        for canonical_name, sources in compound_sources.items():
            if len(sources) > 1:  # 只处理有多个来源的化合物
                print(f"\n尝试 {canonical_name} 的不同来源:")
                for source_info in sources:
                    # 创建一个新的候选方案，替换当前来源
                    current_solution_alt = copy.deepcopy(best_solution)
                    
                    # 找到并替换当前化合物的条目（按归一名）
                    for i_alt, comp_alt in enumerate(current_solution_alt):
                        if get_canonical_name(comp_alt['name']) == canonical_name:
                            current_solution_alt[i_alt] = {
                                'key': source_info['key'],
                                'name': source_info['name'],
                                'formula': interpolated_data[source_info['key']]['formula'],
                                'mass': interpolated_data[source_info['key']]['mass_number'],
                                'coefficient': source_info['coefficient'],
                                'source': source_info['source']
                            }
                            break
                    
                    # 用当前current_solution_alt重新拟合
                    selected_keys_alt = [s['key'] for s in current_solution_alt]
                    selected_indices_alt = [keys.index(key) for key in selected_keys_alt]
                    A_selected_alt = A[:, selected_indices_alt]
                    
                    # 应用权重到替代方案的化合物矩阵（遵循pie_analysis.py标准）
                    if weights is not None:
                        A_selected_alt_weighted = A_selected_alt * weights[:, np.newaxis]
                        coefficients_sel_alt, residuals_sel_alt = nnls(A_selected_alt_weighted, y_exp_interp_weighted)
                    else:
                        coefficients_sel_alt, residuals_sel_alt = nnls(A_selected_alt, y_exp_interp)
                        
                    fitted_pie_sel_alt = np.dot(A_selected_alt, coefficients_sel_alt)
                    metrics_sel_alt = self.calculate_comprehensive_metrics(y_exp_interp, fitted_pie_sel_alt, energy_points)
                    
                    # 更新current_solution_alt中的系数为重新拟合后的系数
                    for i_alt, coef_alt in enumerate(coefficients_sel_alt):
                        current_solution_alt[i_alt]['coefficient'] = coef_alt
                    
                    # 检查是否是更好的方案（参考pie_analysis.py的标准）
                    base_metrics = solutions[0][2]
                    is_good_alternative = (
                        metrics_sel_alt['r_squared'] > base_metrics['r_squared'] * 0.95 and
                        (metrics_sel_alt.get('peak_position_error', 1.0) < 0.3 if 'peak_position_error' in metrics_sel_alt else True) and
                        (metrics_sel_alt.get('threshold_energy_error', 1.0) < 0.3 if 'threshold_energy_error' in metrics_sel_alt else True) and
                        metrics_sel_alt['rmse'] < base_metrics['rmse'] * 1.2
                    )
                    
                    if is_good_alternative:
                        print(f"找到更好的方案：使用 {canonical_name} 的来源 {source_info['source']}")
                        
                        # 转换为需要的格式
                        alt_solution_compounds = []
                        for comp_info in current_solution_alt:
                            alt_solution_compounds.append({
                                'compound': {
                                    'name': comp_info['name'],
                                    'formula': comp_info['formula'],
                                    'mass': comp_info['mass'],
                                    'source': comp_info['source']
                                },
                                'coefficient': comp_info['coefficient'],
                                'name': comp_info['name'],
                                'formula': comp_info['formula'],
                                'mass': comp_info['mass'],
                                'source': comp_info['source']
                            })
                        
                        solutions.append((alt_solution_compounds, fitted_pie_sel_alt, metrics_sel_alt))
        
        # 8. 按评分排序（参考pie_analysis.py的评分方式）
        def calculate_score(solution):
            """计算拟合方案的综合评分（参考pie_analysis.py）"""
            metrics = solution[2]
            score = metrics.get('overall_score', 0)  # 直接使用已计算的overall_score
            return score
        
        solutions.sort(key=calculate_score, reverse=True)
        
        # 9. 去重：强制输出max_solutions种组分/来源组合不同的方案
        unique_solutions = []
        def solution_signature(solution_tuple):
            solution_list = solution_tuple[0]
            return tuple(sorted((comp['name'], comp['source']) for comp in solution_list))
        
        seen_signatures = set()
        for sol in solutions:
            sig = solution_signature(sol)
            if sig not in seen_signatures:
                unique_solutions.append(sol)
                seen_signatures.add(sig)
            if len(unique_solutions) >= max_solutions:
                break
        
        print(f"🏆 成功生成 {len(unique_solutions)} 个拟合方案:")
        for i, (compounds, _, metrics) in enumerate(unique_solutions):
            compound_names = [c['name'] for c in compounds]
            print(f"  方案{i+1}: R²={metrics['r_squared']:.3f}, 评分={metrics.get('overall_score', 0):.3f}")
            print(f"          化合物: {' + '.join(compound_names)}")
        
        return unique_solutions

    def calculate_comprehensive_metrics(self, y_exp, y_fit, energy_points):
        """计算综合评价指标"""
        return MetricsCalculator.calculate_fit_metrics(y_exp, y_fit, energy_points)

    def find_threshold_energy(self, energy_points, y_data, threshold_fraction=0.1):
        """找到PIE曲线的阈值能量（开始上升的位置）"""
        max_val = np.max(y_data)
        threshold_val = max_val * threshold_fraction
        
        # 找到第一个超过阈值的点
        above_threshold = y_data > threshold_val
        if np.any(above_threshold):
            first_idx = np.where(above_threshold)[0][0]
            return energy_points[first_idx]
        else:
            return energy_points[0]

    def _get_experimental_data_from_results(self, results_widget, target_mass):
        """从results_widget获取实验数据"""
        try:
            # 获取选中的峰索引
            peak_index = results_widget.chartCombo.currentIndex()
            if peak_index < 0 or peak_index >= len(results_widget.peaks):
                # 如果没有选中的峰，尝试根据质量数查找匹配的峰
                for i, peak in enumerate(results_widget.peaks):
                    if abs(peak.get('mass', 0.0) - target_mass) < 0.5:  # 容差0.5
                        peak_index = i
                        break
                else:
                    print(f"未找到匹配质量数 {target_mass} 的峰")
                    return None, None, None

            # 获取Y轴数据（积分强度）
            if isinstance(results_widget.results, dict):
                # 字典格式的结果
                y_values = []
                for file_name in results_widget.file_names:
                    if file_name in results_widget.results:
                        result_data = results_widget.results[file_name]
                        if peak_index < result_data.shape[0]:
                            y_values.append(float(result_data[peak_index, 0]))
                        else:
                            y_values.append(0.0)
                    else:
                        y_values.append(0.0)
                exp_y_data = np.array(y_values)
            else:
                # 数组格式的结果
                exp_y_data = results_widget.results[:, peak_index]

            # 获取X轴数据（能量）
            x_values = []
            for filename in results_widget.file_names:
                energy = results_widget.file_metadata.get(filename, {}).get('energy')
                x_values.append(energy if energy is not None else float('nan'))

            exp_x_data = np.array(x_values)
            
            # 移除NaN值
            mask = ~np.isnan(exp_x_data)
            if not np.any(mask):
                print("无法从文件名中提取能量值")
                return None, None, None
            
            # 只保留有效的数据点
            exp_x_data = exp_x_data[mask]
            exp_y_data = exp_y_data[mask]
            
            print(f"获取到实验数据: X轴类型=能量(eV), 数据点数={len(exp_x_data)}")
            
            return exp_x_data, exp_y_data, "能量 (eV)"
            
        except Exception as e:
            print(f"获取实验数据时出错: {str(e)}")
            return None, None, None

    def refresh_solutions(self):
        """刷新拟合方案，保留当前化合物列表"""
        try:
            # 检查是否有保存的化合物列表
            if not hasattr(self, 'all_matching_compounds') or not self.all_matching_compounds:
                QMessageBox.warning(self, "警告", "没有可用的化合物数据，请先进行PIE分析")
                return
            
            # 获取当前的分析参数
            target_mass = float(self.pie_mass_input.text())
            tolerance = float(self.pie_tolerance_input.text())
            max_compounds = self.max_compounds_input.value()
            max_solutions = self.max_solutions_input.value()
            window_size = self.window_size_input.value()
            
            print(f"\n刷新PIE分析方案，质量数: {target_mass:.4f}，保留 {len(self.all_matching_compounds)} 个化合物")
            
            # 获取主窗口和实验数据
            main_window = None
            for widget in QApplication.topLevelWidgets():
                if widget.__class__.__name__ == 'MainWindow':
                    main_window = widget
                    break
            
            if not main_window or not hasattr(main_window, 'results_widget'):
                QMessageBox.warning(self, "错误", "无法找到主窗口或结果页面")
                return
            
            results_widget = main_window.results_widget
            
            # 获取实验数据
            if not hasattr(results_widget, 'graphWidget'):
                QMessageBox.warning(self, "错误", "无法获取图表数据")
                return
            
            # 获取选中的峰数据
            selected_peak = results_widget.getSelectedPeak()
            if not selected_peak:
                print("没有选中的峰，使用质量数查找实验数据")
                # 由于没有get_experimental_data_by_mass方法，我们需要获取当前实验数据
                if hasattr(results_widget, 'results') and hasattr(results_widget, 'file_names') and hasattr(results_widget, 'file_metadata'):
                    # 使用现有的实验数据获取方法
                    exp_x_data, exp_y_data, x_axis_label = self._get_experimental_data_from_results(results_widget, target_mass)
                else:
                    QMessageBox.warning(self, "错误", "无法获取实验数据")
                    return
            else:
                # 使用现有的实验数据获取方法
                exp_x_data, exp_y_data, x_axis_label = self._get_experimental_data_from_results(results_widget, target_mass)
            
            if exp_x_data is None or exp_y_data is None or x_axis_label != "能量 (eV)":
                QMessageBox.warning(self, "错误", "无法获取有效的实验数据或横坐标不是能量类型")
                return
            
            import numpy as np
            from scipy.optimize import nnls
            from scipy.interpolate import interp1d
            
            # 插值实验数据到统一的能量网格
            energy_points = np.linspace(np.min(exp_x_data), np.max(exp_x_data), len(exp_x_data))
            
            if len(exp_x_data) != len(set(exp_x_data)):
                # 如果有重复的x值，先去重
                unique_indices = np.unique(exp_x_data, return_index=True)[1]
                exp_x_unique = exp_x_data[unique_indices]
                exp_y_unique = exp_y_data[unique_indices]
                interp_func = interp1d(exp_x_unique, exp_y_unique, kind='linear', fill_value='extrapolate')
            else:
                interp_func = interp1d(exp_x_data, exp_y_data, kind='linear', fill_value='extrapolate')
            
            y_exp_interp = interp_func(energy_points)
            
            # 创建能量区间重要性权重（与主分析保持一致）
            def create_weights_for_refresh(energy, weight_ranges=None):
                """创建能量区间重要性权重数组"""
                if weight_ranges is None:
                    # 使用GUI中设置的权重
                    min_energy = np.min(energy)
                    max_energy = np.max(energy)

                    # 获取GUI中设置的能量区间和权重
                    range1_min = self.energy_range1_min.value()
                    range1_max = self.energy_range1_max.value()
                    weight1 = self.weight1_input.value()

                    range2_min = self.energy_range2_min.value()
                    range2_max = self.energy_range2_max.value()
                    weight2 = self.weight2_input.value()

                    range3_min = self.energy_range3_min.value()
                    range3_max = self.energy_range3_max.value()
                    weight3 = self.weight3_input.value()

                    # 确保区间在能量范围内
                    range1_min = max(min_energy, range1_min)
                    range1_max = min(max_energy, range1_max)
                    range2_min = max(min_energy, range2_min)
                    range2_max = min(max_energy, range2_max)
                    range3_min = max(min_energy, range3_min)
                    range3_max = min(max_energy, range3_max)

                    # 创建权重范围列表
                    weight_ranges = []

                    # 只添加有效的区间（起始能量小于结束能量）
                    if range1_min < range1_max:
                        weight_ranges.append((range1_min, range1_max, weight1))

                    if range2_min < range2_max:
                        weight_ranges.append((range2_min, range2_max, weight2))

                    if range3_min < range3_max:
                        weight_ranges.append((range3_min, range3_max, weight3))

                    # 如果没有有效区间，使用默认权重
                    if not weight_ranges:
                        weight_ranges = [(min_energy, max_energy, 1)]

                # 使用统一的权重计算器
                return WeightCalculator.create_energy_weights(energy, weight_ranges)
            
            # 创建权重数组
            refresh_weights = create_weights_for_refresh(energy_points)
            
            # 使用现有的化合物列表重新生成拟合方案
            solutions = self.generate_multiple_solutions(
                self.all_matching_compounds, 
                y_exp_interp, 
                energy_points,
                max_solutions=max_solutions,
                fitting_method='nnls',
                weights=refresh_weights  # 传递权重到拟合过程
            )
            
            if not solutions:
                QMessageBox.warning(self, "警告", "未能生成任何有效的拟合方案")
                return
            
            # 保存新的方案到实例变量
            self.current_solutions = solutions
            
            # 更新方案选择下拉框
            self.solution_combo.clear()
            for i, (compounds, fit_result, metrics) in enumerate(solutions):
                score_text = f"方案{i+1} (R²={metrics['r_squared']:.3f}, 评分={metrics['overall_score']:.3f})"
                self.solution_combo.addItem(score_text)
            
            # 自动选择最佳方案（第一个）
            if solutions:
                self.solution_combo.setCurrentIndex(0)
                self.on_solution_changed()  # 触发方案更新
            
            QMessageBox.information(self, "成功", f"已刷新 {len(solutions)} 个拟合方案")
            
        except Exception as e:
            print(f"刷新方案时出错: {str(e)}")
            QMessageBox.critical(self, "错误", f"刷新方案时出错: {str(e)}")
            import traceback
            traceback.print_exc()

    def on_solution_changed(self):
        """当用户切换拟合方案时的处理"""
        try:
            current_index = self.solution_combo.currentIndex()
            if current_index < 0 or current_index >= len(self.current_solutions):
                return
            
            # 获取选中的方案
            solution_compounds, solution_fit, solution_metrics = self.current_solutions[current_index]
            
            print(f"\n切换到方案 {current_index + 1}")
            
            # 更新结果文本
            result_text = f"PIE拟合结果 - 方案 {current_index + 1} (R² = {solution_metrics['r_squared']:.4f}):\n\n"
            result_text += f"相对误差: {solution_metrics['relative_error']:.2%}\n"
            result_text += f"峰值位置误差: {solution_metrics['peak_position_error']:.4f} eV\n"
            result_text += f"阈值能量误差: {solution_metrics['threshold_energy_error']:.4f} eV\n"
            result_text += f"RMSE: {solution_metrics['rmse']:.4f}\n\n"
            
            # 计算总系数
            total_coef = sum(comp['coefficient'] for comp in solution_compounds)
            
            for comp in solution_compounds:
                name = comp['name']
                formula = comp['formula']
                mass = comp['mass']
                coef = comp['coefficient']
                norm_coef = coef / total_coef if total_coef > 0 else 0
                source = comp['source']
                
                # 确保mass是数字类型
                mass_float = float(mass) if isinstance(mass, (str, int, float)) else 0.0
                result_text += f"{name} ({formula}, {mass_float:.4f}): 系数={coef:.6f}, 占比={norm_coef:.2%}, 来源={source}\n"
            
            self.pie_result_text.setText(result_text)
            
            # 更新表格
            self.update_compounds_table(solution_compounds)
            
            # 更新主窗口显示
            self.update_main_window_display(solution_compounds, solution_fit, solution_metrics)
            
            # 确保PIE曲线在方案切换后保持显示
            print(f"已更新主窗口PIE曲线显示")
            
        except Exception as e:
            print(f"切换方案时出错: {str(e)}")
            import traceback
            traceback.print_exc()

    def update_compounds_table(self, solution_compounds):
        """更新化合物表格显示"""
        try:
            # 清空表格
            self.pie_compounds_list.setRowCount(0)
            
            # 使用所有匹配的化合物（如果可用），否则只显示参与拟合的化合物
            if hasattr(self, 'all_matching_compounds') and self.all_matching_compounds:
                all_compounds = self.all_matching_compounds
            else:
                # 如果没有保存的全部化合物，只显示参与拟合的化合物
                all_compounds = [comp['compound'] for comp in solution_compounds]
            
            # 创建一个字典来快速查找参与拟合的化合物系数
            fitted_compounds_dict = {}
            for comp in solution_compounds:
                compound_name = comp['compound'].get('name', '')
                compound_source = comp['compound'].get('source', '')
                # 使用名称+来源作为唯一标识
                key = f"{compound_name}|{compound_source}"
                fitted_compounds_dict[key] = comp['coefficient']
            
            # 创建显示列表，包含所有化合物
            compounds_to_display = []
            for compound in all_compounds:
                compound_name = compound.get('name', '')
                compound_source = compound.get('source', '')
                key = f"{compound_name}|{compound_source}"
                
                # 检查是否参与了拟合
                if key in fitted_compounds_dict:
                    coefficient = fitted_compounds_dict[key]
                    checked = True
                else:
                    coefficient = 0.0
                    # 🔧 如果没有参与拟合，检查用户是否原本勾选了这个化合物
                    original_checked = getattr(self, 'original_checked_compounds', set())
                    checked = key in original_checked
                    if checked:
                        print(f"  ✅ 保持用户原始勾选状态: {compound_name} (未参与拟合但用户勾选)")
                
                compounds_to_display.append({
                    'compound': compound,
                    'coefficient': coefficient,
                    'checked': checked
                })
            
            # 按系数大小排序（系数大的在前，系数为0的按原始顺序）
            compounds_to_display.sort(key=lambda x: (-x['coefficient'], x['compound'].get('name', '')))
            
            # 填充表格
            self.pie_compounds_list.setRowCount(len(compounds_to_display))
            for i, item in enumerate(compounds_to_display):
                compound = item['compound']
                coefficient = item['coefficient']
                checked = item['checked']
                
                # 获取化合物信息
                name = compound.get('name', '')
                formula = compound.get('formula', '')
                mass = compound.get('mol_weight', 0)
                source = compound.get('source', '')
                
                # 创建表格项
                checkbox = QTableWidgetItem()
                checkbox.setCheckState(Qt.CheckState.Checked if checked else Qt.CheckState.Unchecked)
                
                weight_item = QTableWidgetItem(f"{coefficient:.10f}")
                name_item = QTableWidgetItem(name)
                formula_item = QTableWidgetItem(formula)
                mass_item = QTableWidgetItem(f"{mass:.4f}")
                ie_item = QTableWidgetItem(self.get_first_ionization_energy(compound))
                source_item = QTableWidgetItem(source)
                
                # 添加到表格
                self.pie_compounds_list.setItem(i, 0, checkbox)
                self.pie_compounds_list.setItem(i, 1, weight_item)
                self.pie_compounds_list.setItem(i, 2, name_item)
                self.pie_compounds_list.setItem(i, 3, formula_item)
                self.pie_compounds_list.setItem(i, 4, mass_item)
                self.pie_compounds_list.setItem(i, 5, ie_item)
                self.pie_compounds_list.setItem(i, 6, source_item)
                
                # 存储化合物数据
                compound['weight'] = coefficient
                name_item.setData(Qt.ItemDataRole.UserRole, compound)
                
                # 设置权重系数列为可编辑，其他列为不可编辑
                for col in [0, 2, 3, 4, 5, 6]:
                    item = self.pie_compounds_list.item(i, col)
                    if item:
                        item.setFlags(item.flags() & ~Qt.ItemFlag.ItemIsEditable)
            
            # 调整列宽
            self.pie_compounds_list.resizeColumnsToContents()
            
        except Exception as e:
            print(f"更新化合物表格时出错: {str(e)}")

    def update_main_window_display(self, solution_compounds, solution_fit, solution_metrics):
        """更新主窗口的PIE曲线显示"""
        try:
            # 获取主窗口
            main_window = None
            for widget in QApplication.topLevelWidgets():
                if widget.__class__.__name__ == 'MainWindow':
                    main_window = widget
                    break
            
            if main_window:
                # 为每个化合物准备正确的数据结构，包含系数信息
                compounds_to_show = []
                for comp in solution_compounds:
                    # 从保存的完整化合物列表中找到对应的数据（包含PIE数据）
                    comp_name = comp['compound'].get('name', '') if 'compound' in comp else comp.get('name', '')
                    comp_source = comp['compound'].get('source', '') if 'compound' in comp else comp.get('source', '')
                    
                    # 在原始化合物列表中查找对应的完整数据
                    full_compound_data = None
                    if hasattr(self, 'all_matching_compounds') and self.all_matching_compounds:
                        for orig_comp in self.all_matching_compounds:
                            if (orig_comp.get('name', '') == comp_name and 
                                orig_comp.get('source', '') == comp_source):
                                full_compound_data = copy.deepcopy(orig_comp)  # 深复制完整数据
                                break
                    
                    if full_compound_data:
                        # 确保权重正确设置
                        full_compound_data['coefficient'] = comp['coefficient']
                        full_compound_data['weight'] = comp['coefficient']
                        compounds_to_show.append(full_compound_data)
                    else:
                        # 如果找不到，使用原始数据但添加权重
                        if 'compound' in comp:
                            compound_data = comp['compound'].copy()
                        else:
                            compound_data = comp.copy()
                        compound_data['coefficient'] = comp['coefficient']
                        compound_data['weight'] = comp['coefficient']
                        compounds_to_show.append(compound_data)
                        print(f"警告：在切换方案时未找到化合物 {comp_name} 的完整PIE数据")
                
                print(f"更新主窗口显示: {len(compounds_to_show)} 个化合物")
                
                # 显示化合物系数信息，用于调试
                for i, comp_data in enumerate(compounds_to_show):
                    print(f"  - {comp_data.get('name', '')}: 拟合系数 = {comp_data.get('weight', 0.0):.10f}")
                
                # 在主窗口显示PIE曲线
                # 获取拟合相关的数据，如果可用的话
                energy_points = None
                y_exp_interp = None
                if hasattr(self, 'current_solutions') and self.current_solutions:
                    # 尝试从当前方案中获取能量点数据
                    current_index = self.solution_combo.currentIndex()
                    if 0 <= current_index < len(self.current_solutions):
                        # 可以在这里添加能量点的提取逻辑，如果需要的话
                        pass
                
                self.show_pie_in_main_window(main_window, compounds_to_show, energy_points, y_exp_interp)
                
        except Exception as e:
            print(f"更新主窗口显示时出错: {str(e)}")

    def closeEvent(self, event):
        """关闭窗口时从全局存储器中移除实例"""
        global _calculator_instances
        if self in _calculator_instances:
            _calculator_instances.remove(self)
        
        # 停止PSI4计算线程
        if hasattr(self, 'calculation_thread') and self.calculation_thread and self.calculation_thread.isRunning():
            self.calculation_thread.stop_monitoring = True
            self.calculation_thread.wait(3000)  # 等待最多3秒
        
        event.accept()
    
    def create_psi4_calculation_tab(self):
        """创建PSI4电离能计算标签页"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # 检查PSI4可用性
        if not PSI4_AVAILABLE:
            # PSI4不可用时显示提示信息
            warning_label = QLabel("PSI4电离能计算功能不可用")
            warning_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
            warning_label.setStyleSheet("""
                QLabel {
                    font-size: 16px;
                    color: #d32f2f;
                    background-color: #ffebee;
                    border: 2px solid #d32f2f;
                    border-radius: 8px;
                    padding: 20px;
                    margin: 20px;
                }
            """)
            
            details_label = QLabel("""
                <b>可能的原因：</b><br>
                • PSI4 未正确安装<br>
                • RDKit 未正确安装<br>
                • 模块导入失败<br><br>
                
                <b>安装指南：</b><br>
                <code>conda install -c conda-forge psi4 rdkit</code><br>
                或参考项目文档进行安装
            """)
            details_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
            details_label.setStyleSheet("""
                QLabel {
                    font-size: 12px;
                    color: #666;
                    background-color: #f5f5f5;
                    border: 1px solid #ddd;
                    border-radius: 4px;
                    padding: 15px;
                    margin: 10px 20px;
                }
            """)
            
            layout.addWidget(warning_label)
            layout.addWidget(details_label)
            layout.addStretch()
            
            self.tab_widget.addTab(widget, "PSI4电离能计算")
            return
        
        # PSI4可用时创建完整的计算界面
        # 创建分割器 - 上方参数设置，下方输出显示
        splitter = QSplitter(Qt.Orientation.Vertical)
        layout.addWidget(splitter)
        
        # 上方：参数设置区域
        params_widget = QWidget()
        params_layout = QVBoxLayout(params_widget)
        
        # 计算参数组
        params_group = QGroupBox("PSI4电离能计算参数")
        params_form_layout = QVBoxLayout(params_group)
        
        # 使用网格布局组织参数
        import PyQt6.QtWidgets as QtWidgets
        grid_layout = QtWidgets.QGridLayout()
        
        # SMILES输入
        grid_layout.addWidget(QLabel("SMILES:"), 0, 0)
        self.psi4_smiles_input = QLineEdit()
        self.psi4_smiles_input.setPlaceholderText("输入分子的SMILES字符串，如 CCO (乙醇)")
        self.psi4_smiles_input.textChanged.connect(self.on_psi4_smiles_changed)
        grid_layout.addWidget(self.psi4_smiles_input, 0, 1, 1, 2)
        
        # 从化合物填充SMILES按钮
        self.psi4_fill_smiles_button = QPushButton("从选中化合物填充SMILES")
        self.psi4_fill_smiles_button.clicked.connect(self.fill_psi4_smiles_from_compound)
        grid_layout.addWidget(self.psi4_fill_smiles_button, 0, 3)
        
        # 分子信息显示
        self.psi4_molecule_info_label = QLabel("分子信息将在输入SMILES后显示")
        self.psi4_molecule_info_label.setStyleSheet("color: #666; font-style: italic;")
        grid_layout.addWidget(self.psi4_molecule_info_label, 1, 0, 1, 4)
        
        # 线程数
        grid_layout.addWidget(QLabel("CPU线程数:"), 2, 0)
        self.psi4_nthreads_input = QtWidgets.QSpinBox()
        self.psi4_nthreads_input.setMinimum(1)
        self.psi4_nthreads_input.setMaximum(16)
        self.psi4_nthreads_input.setValue(10)
        grid_layout.addWidget(self.psi4_nthreads_input, 2, 1)
        
        # 内存大小
        grid_layout.addWidget(QLabel("内存大小:"), 2, 2)
        self.psi4_memory_input = QComboBox()
        self.psi4_memory_input.addItems(["1GB", "2GB", "4GB", "8GB", "16GB"])
        self.psi4_memory_input.setCurrentText("10GB")
        self.psi4_memory_input.setEditable(True)
        grid_layout.addWidget(self.psi4_memory_input, 2, 3)
        
        # 构象搜索方法
        grid_layout.addWidget(QLabel("构象搜索方法:"), 3, 0)
        self.psi4_low_method_input = QComboBox()
        self.psi4_low_method_input.addItems(["b3lyp", "pbe0", "m06", "wb97x-d", "hf"])
        self.psi4_low_method_input.setCurrentText("b3lyp")
        self.psi4_low_method_input.setEditable(True)
        grid_layout.addWidget(self.psi4_low_method_input, 3, 1)
        
        # 构象搜索基组
        grid_layout.addWidget(QLabel("构象搜索基组:"), 3, 2)
        self.psi4_low_basis_input = QComboBox()
        self.psi4_low_basis_input.addItems(["6-31g(d)", "6-31g(d,p)", "6-31+g(d)", "6-31+g(d,p)", "def2-svp"])
        self.psi4_low_basis_input.setCurrentText("6-31g(d)")
        self.psi4_low_basis_input.setEditable(True)
        grid_layout.addWidget(self.psi4_low_basis_input, 3, 3)
        
        # 电离能计算方法
        grid_layout.addWidget(QLabel("电离能计算方法:"), 4, 0)
        self.psi4_high_method_input = QComboBox()
        self.psi4_high_method_input.addItems(["m062x", "wb97x-d", "b3lyp", "pbe0", "ccsd(t)"])
        self.psi4_high_method_input.setCurrentText("m062x")
        self.psi4_high_method_input.setEditable(True)
        grid_layout.addWidget(self.psi4_high_method_input, 4, 1)
        
        # 电离能计算基组
        grid_layout.addWidget(QLabel("电离能计算基组:"), 4, 2)
        self.psi4_high_basis_input = QComboBox()
        self.psi4_high_basis_input.addItems(["aug-cc-pvtz", "aug-cc-pvdz", "cc-pvtz", "cc-pvdz", "def2-tzvp"])
        self.psi4_high_basis_input.setCurrentText("aug-cc-pvtz")
        self.psi4_high_basis_input.setEditable(True)
        grid_layout.addWidget(self.psi4_high_basis_input, 4, 3)
        
        # 最大构象数
        grid_layout.addWidget(QLabel("最大构象数:"), 5, 0)
        self.psi4_max_conformers_input = QtWidgets.QSpinBox()
        self.psi4_max_conformers_input.setMinimum(1)
        self.psi4_max_conformers_input.setMaximum(50)
        self.psi4_max_conformers_input.setValue(10)
        grid_layout.addWidget(self.psi4_max_conformers_input, 5, 1)
        
        # 跳过构象搜索优化
        self.psi4_skip_optimization_checkbox = QCheckBox("跳过构象搜索优化（快速模式）")
        grid_layout.addWidget(self.psi4_skip_optimization_checkbox, 5, 2, 1, 2)
        
        # 缓存目录设置
        grid_layout.addWidget(QLabel("PSI4缓存目录:"), 6, 0)
        self.psi4_scratch_input = QLineEdit()
        self.psi4_scratch_input.setPlaceholderText("留空使用默认临时目录")
        grid_layout.addWidget(self.psi4_scratch_input, 6, 1, 1, 2)
        
        self.psi4_browse_button = QPushButton("浏览...")
        self.psi4_browse_button.clicked.connect(self.browse_psi4_scratch_dir)
        grid_layout.addWidget(self.psi4_browse_button, 6, 3)
        
        params_form_layout.addLayout(grid_layout)
        params_layout.addWidget(params_group)
        
        # 控制按钮
        button_layout = QHBoxLayout()
        
        self.psi4_start_button = QPushButton("开始计算")
        self.psi4_start_button.clicked.connect(self.start_psi4_calculation)
        self.psi4_start_button.setStyleSheet("""
            QPushButton {
                background-color: #4CAF50;
                color: white;
                font-weight: bold;
                padding: 8px 16px;
                border: none;
                border-radius: 4px;
            }
            QPushButton:hover {
                background-color: #45a049;
            }
            QPushButton:disabled {
                background-color: #cccccc;
                color: #666666;
            }
        """)
        button_layout.addWidget(self.psi4_start_button)
        
        self.psi4_stop_button = QPushButton("停止计算")
        self.psi4_stop_button.clicked.connect(self.stop_psi4_calculation)
        self.psi4_stop_button.setEnabled(False)
        self.psi4_stop_button.setStyleSheet("""
            QPushButton {
                background-color: #f44336;
                color: white;
                font-weight: bold;
                padding: 8px 16px;
                border: none;
                border-radius: 4px;
            }
            QPushButton:hover {
                background-color: #da190b;
            }
            QPushButton:disabled {
                background-color: #cccccc;
                color: #666666;
            }
        """)
        button_layout.addWidget(self.psi4_stop_button)
        
        button_layout.addStretch()
        params_layout.addLayout(button_layout)
        
        splitter.addWidget(params_widget)
        
        # 下方：输出和结果区域
        output_widget = QWidget()
        output_layout = QVBoxLayout(output_widget)
        
        # 进度显示
        self.psi4_progress_label = QLabel("等待开始计算...")
        self.psi4_progress_label.setStyleSheet("font-weight: bold; color: #333;")
        output_layout.addWidget(self.psi4_progress_label)
        
        # PSI4输出区域
        output_group = QGroupBox("PSI4计算输出")
        output_group_layout = QVBoxLayout(output_group)
        
        self.psi4_output_text = QTextEdit()
        self.psi4_output_text.setReadOnly(True)
        self.psi4_output_text.setFont(QFont("Consolas", 9))
        self.psi4_output_text.setStyleSheet("""
            QTextEdit {
                background-color: #f8f8f8;
                border: 1px solid #ddd;
                color: #333;
            }
        """)
        output_group_layout.addWidget(self.psi4_output_text)
        output_layout.addWidget(output_group)
        
        # 结果显示区域
        results_group = QGroupBox("计算结果")
        results_layout = QVBoxLayout(results_group)
        
        self.psi4_results_text = QTextEdit()
        self.psi4_results_text.setReadOnly(True)
        self.psi4_results_text.setMaximumHeight(200)
        self.psi4_results_text.setFont(QFont("Arial", 10))
        results_layout.addWidget(self.psi4_results_text)
        
        # 结果操作按钮
        results_button_layout = QHBoxLayout()
        
        self.psi4_copy_results_button = QPushButton("绘制结果")
        self.psi4_copy_results_button.clicked.connect(self.plot_psi4_results)
        self.psi4_copy_results_button.setEnabled(False)
        results_button_layout.addWidget(self.psi4_copy_results_button)
        
        self.psi4_save_to_db_button = QPushButton("保存到数据库")
        self.psi4_save_to_db_button.clicked.connect(self.save_psi4_result_to_database)
        self.psi4_save_to_db_button.setEnabled(False)
        results_button_layout.addWidget(self.psi4_save_to_db_button)
        
        results_button_layout.addStretch()
        results_layout.addLayout(results_button_layout)
        output_layout.addWidget(results_group)
        
        splitter.addWidget(output_widget)
        
        # 设置分割器比例 (参数:输出 = 1:2)
        splitter.setSizes([200, 400])
        
        # 添加到标签页
        self.tab_widget.addTab(widget, "PSI4电离能计算")
    
    def on_psi4_smiles_changed(self):
        """当SMILES输入改变时更新分子信息"""
        smiles = self.psi4_smiles_input.text().strip()
        if not smiles:
            self.psi4_molecule_info_label.setText("分子信息将在输入SMILES后显示")
            return
        
        try:
            # 尝试导入RDKit来分析SMILES
            try:
                from rdkit import Chem
                from rdkit.Chem import rdMolDescriptors
                
                mol = Chem.MolFromSmiles(smiles)
                if mol is None:
                    self.psi4_molecule_info_label.setText("⚠️ 无效的SMILES字符串")
                    self.psi4_molecule_info_label.setStyleSheet("color: #d32f2f; font-weight: bold;")
                    return
                
                # 计算分子信息
                mol_weight = rdMolDescriptors.CalcExactMolWt(mol)
                heavy_atoms = mol.GetNumHeavyAtoms()
                
                # 创建信息文本
                info_parts = [f"分子量: {mol_weight:.4f}"]
                info_parts.append(f"重原子数: {heavy_atoms}")
                
                # 检查与主窗口质量数的匹配度
                if hasattr(self, 'initial_mass') and self.initial_mass:
                    mass_diff = abs(mol_weight - self.initial_mass)
                    if mass_diff > 0.1:
                        info_parts.append(f"⚠️ 与主窗口质量数差异: {mass_diff:.4f}")
                        self.psi4_molecule_info_label.setStyleSheet("color: #ff9800; font-weight: bold;")
                    else:
                        info_parts.append("✓ 质量数匹配")
                        self.psi4_molecule_info_label.setStyleSheet("color: #4CAF50; font-weight: bold;")
                else:
                    self.psi4_molecule_info_label.setStyleSheet("color: #333;")
                
                # 大分子耗时警告
                if heavy_atoms > 6:
                    info_parts.append(f"⚠️ 大分子计算耗时较长")
                    if self.psi4_molecule_info_label.styleSheet() == "color: #333;":
                        self.psi4_molecule_info_label.setStyleSheet("color: #ff9800; font-weight: bold;")
                
                self.psi4_molecule_info_label.setText(" | ".join(info_parts))
                
            except ImportError:
                self.psi4_molecule_info_label.setText("无法分析SMILES（需要RDKit）")
                self.psi4_molecule_info_label.setStyleSheet("color: #666; font-style: italic;")
                
        except Exception as e:
            self.psi4_molecule_info_label.setText(f"分析SMILES时出错: {str(e)}")
            self.psi4_molecule_info_label.setStyleSheet("color: #d32f2f;")
    
    def fill_psi4_smiles_from_compound(self):
        """从选中的化合物填充SMILES"""
        try:
            # 获取当前选中的化合物
            if not hasattr(self, 'compounds_list') or not self.compounds_list.currentItem():
                QMessageBox.warning(self, "警告", "请先在化合物信息标签页中选择一个化合物")
                return
            
            current_item = self.compounds_list.currentItem()
            compound_id = current_item.data(Qt.ItemDataRole.UserRole)
            
            if not compound_id:
                QMessageBox.warning(self, "警告", "无法获取化合物ID")
                return
            
            # 查找化合物详细信息
            compound = None
            for c in self.compounds:
                if c.get('id', '') == compound_id:
                    compound = c
                    break
            
            if not compound:
                QMessageBox.warning(self, "警告", "无法获取化合物信息")
                return
            
            # 尝试获取SMILES
            smiles = compound.get('smiles', '')
            if not smiles:
                QMessageBox.information(self, "提示", f"化合物 '{compound.get('name', 'Unknown')}' 没有SMILES信息")
                return
            
            # 填充SMILES
            self.psi4_smiles_input.setText(smiles)
            QMessageBox.information(self, "成功", f"已填充化合物 '{compound.get('name', 'Unknown')}' 的SMILES")
            
        except Exception as e:
            QMessageBox.critical(self, "错误", f"填充SMILES时出错: {str(e)}")
    
    def browse_psi4_scratch_dir(self):
        """浏览选择PSI4缓存目录"""
        from PyQt6.QtWidgets import QFileDialog
        directory = QFileDialog.getExistingDirectory(self, "选择PSI4缓存目录")
        if directory:
            self.psi4_scratch_input.setText(directory)
    
    def get_psi4_calculation_params(self):
        """获取PSI4计算参数"""
        params = {
            'smiles': self.psi4_smiles_input.text().strip(),
            'nthreads': self.psi4_nthreads_input.value(),
            'memory': self.psi4_memory_input.currentText(),
            'low_level_method': self.psi4_low_method_input.currentText(),
            'low_level_basis': self.psi4_low_basis_input.currentText(),
            'high_level_method': self.psi4_high_method_input.currentText(),
            'high_level_basis': self.psi4_high_basis_input.currentText(),
            'max_conformers': self.psi4_max_conformers_input.value(),
            'skip_low_level_optimization': self.psi4_skip_optimization_checkbox.isChecked(),
            'scratch_dir': self.psi4_scratch_input.text().strip() or None
        }
        return params
    
    def start_psi4_calculation(self):
        """开始PSI4计算"""
        if not PSI4_AVAILABLE:
            QMessageBox.critical(self, "错误", "PSI4模块不可用")
            return
        
        # 验证输入
        params = self.get_psi4_calculation_params()
        
        if not params['smiles']:
            QMessageBox.warning(self, "警告", "请输入SMILES字符串")
            return
        
        # 停止正在运行的计算
        if self.calculation_thread and self.calculation_thread.isRunning():
            self.stop_psi4_calculation()
            time.sleep(0.5)  # 等待线程停止
        
        # 清空输出
        self.psi4_output_text.clear()
        self.psi4_results_text.clear()
        
        # 创建并启动计算线程
        self.calculation_thread = PSI4CalculationThread(params)
        self.calculation_thread.calculation_started.connect(self.on_psi4_calculation_started)
        self.calculation_thread.calculation_finished.connect(self.on_psi4_calculation_finished)
        self.calculation_thread.calculation_error.connect(self.on_psi4_calculation_error)
        self.calculation_thread.progress_updated.connect(self.on_psi4_progress_updated)
        self.calculation_thread.output_updated.connect(self.on_psi4_output_updated)
        
        self.calculation_thread.start()
    
    def stop_psi4_calculation(self):
        """停止PSI4计算"""
        if self.calculation_thread and self.calculation_thread.isRunning():
            self.calculation_thread.stop_monitoring = True
            self.calculation_thread.wait(3000)  # 等待最多3秒
        
        self.update_psi4_interface_state(False)
        self.psi4_progress_label.setText("计算已停止")
    
    def update_psi4_interface_state(self, calculating):
        """更新PSI4界面状态"""
        if hasattr(self, 'psi4_start_button'):
            self.psi4_start_button.setEnabled(not calculating)
        if hasattr(self, 'psi4_stop_button'):
            self.psi4_stop_button.setEnabled(calculating)
        if hasattr(self, 'psi4_copy_results_button'):
            self.psi4_copy_results_button.setEnabled(not calculating and bool(self.psi4_results_text.toPlainText()))
        if hasattr(self, 'psi4_save_to_db_button'):
            self.psi4_save_to_db_button.setEnabled(not calculating and bool(self.psi4_results_text.toPlainText()))
    
    @pyqtSlot()
    def on_psi4_calculation_started(self):
        """PSI4计算开始"""
        self.update_psi4_interface_state(True)
        self.psi4_progress_label.setText("PSI4计算进行中...")
    
    @pyqtSlot(dict)
    def on_psi4_calculation_finished(self, results):
        """PSI4计算完成"""        
        if results['success']:
            self.psi4_progress_label.setText("计算完成 ✓")
            self.display_psi4_results(results)
        else:
            self.psi4_progress_label.setText("计算失败 ✗")
            self.psi4_results_text.setText(f"计算失败: {results.get('error_message', '未知错误')}")
        
        # 修复：在显示结果后更新界面状态，确保按钮正确启用
        self.update_psi4_interface_state(False)
    
    @pyqtSlot(str)
    def on_psi4_calculation_error(self, error_message):
        """PSI4计算出错"""
        self.update_psi4_interface_state(False)
        self.psi4_progress_label.setText("计算出错 ✗")
        self.psi4_results_text.setText(f"计算出错: {error_message}")
    
    @pyqtSlot(str)
    def on_psi4_progress_updated(self, message):
        """PSI4进度更新"""
        self.psi4_progress_label.setText(message)
    
    @pyqtSlot(str)
    def on_psi4_output_updated(self, output):
        """PSI4输出更新"""
        if output.strip():
            # 添加时间戳
            from datetime import datetime
            timestamp = datetime.now().strftime("%H:%M:%S")

            # 格式化输出
            formatted_output = output.strip()

            # 为不同类型的信息添加颜色标记（通过HTML）
            if any(keyword in output.lower() for keyword in ['error', 'failed', 'warning']):
                # 错误和警告信息用红色
                formatted_output = f'<span style="color: red;">[{timestamp}] {formatted_output}</span>'
            elif any(keyword in output.lower() for keyword in ['completed', 'converged', 'finished', 'success']):
                # 成功信息用绿色
                formatted_output = f'<span style="color: green;">[{timestamp}] {formatted_output}</span>'
            elif any(keyword in output.lower() for keyword in ['starting', 'beginning', 'initializing']):
                # 开始信息用蓝色
                formatted_output = f'<span style="color: blue;">[{timestamp}] {formatted_output}</span>'
            else:
                # 普通信息用默认颜色
                formatted_output = f'[{timestamp}] {formatted_output}'

            # 使用HTML格式添加内容
            cursor = self.psi4_output_text.textCursor()
            cursor.movePosition(cursor.MoveOperation.End)
            cursor.insertHtml(formatted_output + '<br>')

            # 自动滚动到底部
            scrollbar = self.psi4_output_text.verticalScrollBar()
            scrollbar.setValue(scrollbar.maximum())

            # 限制输出文本的长度，避免内存占用过多
            if self.psi4_output_text.document().blockCount() > 1000:
                # 删除前面的内容，保留最新的1000行
                cursor = self.psi4_output_text.textCursor()
                cursor.movePosition(cursor.MoveOperation.Start)
                for _ in range(100):  # 删除前100行
                    cursor.select(cursor.SelectionType.BlockUnderCursor)
                    cursor.removeSelectedText()
                    cursor.deleteChar()  # 删除换行符
    
    def display_psi4_results(self, results):
        """显示PSI4计算结果"""
        result_text = "=== PSI4绝热电离能计算结果 ===\n\n"
        
        result_text += f"SMILES: {results['smiles']}\n"
        result_text += f"构象搜索方法: {results['low_level_method']}\n"
        result_text += f"电离能计算方法: {results['high_level_method']}\n"
        result_text += f"生成构象数: {results['conformers_generated']}\n"
        result_text += f"优化构象数: {results['conformers_optimized']}\n\n"
        
        result_text += "=== 能量信息 ===\n"
        if results['neutral_energy'] is not None:
            result_text += f"中性分子能量: {results['neutral_energy']:.8f} Hartree\n"
        if results['cation_energy'] is not None:
            result_text += f"阳离子能量: {results['cation_energy']:.8f} Hartree\n"
        
        result_text += "\n=== 绝热电离能 ===\n"
        if results['ionization_energy_ev'] is not None:
            result_text += f"电离能: {results['ionization_energy_ev']:.3f} eV\n"
            result_text += f"电离能: {results['ionization_energy_kcal_mol']:.2f} kcal/mol\n"
            result_text += f"电离能: {results['ionization_energy_hartree']:.6f} Hartree\n"
        
        result_text += f"\n计算时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n"
        
        self.psi4_results_text.setText(result_text)
        
        # 存储结果用于后续操作
        self.current_psi4_results = results
    
    def plot_psi4_results(self):
        """绘制PSI4计算结果到主窗口图表"""
        if not hasattr(self, 'current_psi4_results') or not self.current_psi4_results['success']:
            QMessageBox.warning(self, "警告", "没有可绘制的有效计算结果")
            return
        
        try:
            results = self.current_psi4_results
            ie_value = results['ionization_energy_ev']
            compound_name = f"PSI4计算_{results['smiles']}"
            
            # 导入信号模块并发送绘制信号
            from .compound_dialog import compound_dialog_signals
            compound_dialog_signals.ionization_energy_selected.emit(ie_value, compound_name)
            
            QMessageBox.information(self, "成功", 
                f"已将PSI4计算的电离能值 {ie_value:.3f} eV 发送到主窗口图表\n"
                f"化合物: {compound_name}")
        
        except Exception as e:
            QMessageBox.critical(self, "错误", f"绘制结果时出错: {str(e)}")
    
    def save_psi4_result_to_database(self):
        """将PSI4计算结果保存到数据库"""
        if not hasattr(self, 'current_psi4_results') or not self.current_psi4_results['success']:
            QMessageBox.warning(self, "警告", "没有可保存的有效计算结果")
            return

        try:
            results = self.current_psi4_results
            smiles = results['smiles']

            # 弹出对话框让用户输入化合物名称
            from PyQt6.QtWidgets import QInputDialog
            compound_name, ok = QInputDialog.getText(
                self,
                "输入化合物名称",
                "请输入要保存的化合物名称:",
                text=f"PSI4_{results.get('formula', 'Unknown')}"
            )

            if not ok or not compound_name.strip():
                return  # 用户取消或输入为空

            compound_name = compound_name.strip()

            # 验证名称格式（避免特殊字符）
            import re
            if not re.match(r'^[a-zA-Z0-9_\-\s\(\)]+$', compound_name):
                QMessageBox.warning(self, "警告", "化合物名称只能包含字母、数字、下划线、连字符、空格和括号")
                return

            # 使用RDKit从SMILES推导分子式和分子量
            mol_formula = ""
            mol_weight = 0.0

            try:
                from rdkit import Chem
                from rdkit.Chem import rdMolDescriptors

                mol = Chem.MolFromSmiles(smiles)
                if mol is not None:
                    mol_formula = rdMolDescriptors.CalcMolFormula(mol)
                    mol_weight = rdMolDescriptors.CalcExactMolWt(mol)
                else:
                    QMessageBox.warning(self, "警告", f"无法从SMILES解析分子: {smiles}")
                    return
            except ImportError:
                QMessageBox.warning(self, "警告", "RDKit不可用，无法计算分子式和分子量")
                return

            # 从结果中获取方法/基组字符串
            method_basis = f"{results.get('high_level_method', 'Unknown').upper()}/{results.get('high_level_basis', 'Unknown').upper()}"
            
            # 创建新的化合物条目，按用户要求的格式
            new_compound = {
                'id': str(uuid.uuid4()),
                'name': compound_name,  # 使用用户输入的名称
                'smiles': smiles,
                'formula': mol_formula,
                'mol_weight': mol_weight,
                'cas_rn': "",  # 空的CAS号
                'ion_energetics': {
                    'ie_values': [f"{results['ionization_energy_ev']:.3f}"],
                    'methods': ["Calc."],  # 按用户要求设置为"Calc."
                    'references': ["P.W."],  # 按用户要求设置为"P.W."
                    'comments': [method_basis]  # 使用计算方法/基组作为备注
                },
                'source': 'PSI4',  # 添加来源标识
                'metadata': {
                    'calculation_method': results.get('high_level_method', 'Unknown'),
                    'calculation_basis': results.get('high_level_basis', 'Unknown'),
                    'conformer_method': results.get('low_level_method', 'Unknown'),
                    'conformer_basis': results.get('low_level_basis', 'Unknown'),
                    'conformers_generated': results.get('conformers_generated', 0),
                    'conformers_optimized': results.get('conformers_optimized', 0),
                    'neutral_energy': results.get('neutral_energy', 0.0),
                    'cation_energy': results.get('cation_energy', 0.0),
                    'ie_hartree': results.get('ionization_energy_hartree', 0.0),
                    'ie_kcal_mol': results.get('ionization_energy_kcal_mol', 0.0),
                    'calculated_at': datetime.now().isoformat()
                }
            }
            
            # 保存到NIST数据库文件
            nist_db_path = os.path.join("database", "nist_compounds.json")

            # 确保数据库目录存在
            os.makedirs(os.path.dirname(nist_db_path), exist_ok=True)

            # 读取现有数据库或创建新的
            if os.path.exists(nist_db_path):
                try:
                    with open(nist_db_path, 'r', encoding='utf-8') as f:
                        nist_data = json.load(f)
                except (json.JSONDecodeError, UnicodeDecodeError) as e:
                    QMessageBox.warning(self, "警告", f"数据库文件格式错误，将创建新的数据库文件: {e}")
                    nist_data = []
            else:
                nist_data = []

            # 检查是否已存在相同的化合物（基于SMILES）
            existing_compound = None
            for i, compound in enumerate(nist_data):
                if compound.get('smiles') == smiles:
                    existing_compound = i
                    break

            if existing_compound is not None:
                # 询问用户是否追加新的电离能数据
                existing_entry = nist_data[existing_compound]
                existing_ie_values = existing_entry.get('ion_energetics', {}).get('ie_values', [])

                from PyQt6.QtWidgets import QMessageBox
                reply = QMessageBox.question(
                    self,
                    "化合物已存在",
                    f"数据库中已存在相同SMILES的化合物：{existing_entry.get('name', 'Unknown')}\n"
                    f"现有电离能值: {', '.join(existing_ie_values)} eV\n"
                    f"新计算值: {results['ionization_energy_ev']:.3f} eV\n\n"
                    f"是否将新的电离能数据追加到现有化合物中？\n"
                    f"(选择'是'将追加数据，选择'否'将取消保存)",
                    QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
                    QMessageBox.StandardButton.Yes
                )

                if reply == QMessageBox.StandardButton.Yes:
                    # 追加新的电离能数据到现有化合物
                    existing_ion_energetics = existing_entry.setdefault('ion_energetics', {
                        'ie_values': [],
                        'methods': [],
                        'references': [],
                        'comments': []
                    })

                    # 追加新数据到各个数组
                    existing_ion_energetics['ie_values'].append(f"{results['ionization_energy_ev']:.3f}")
                    existing_ion_energetics['methods'].append("Calc.")
                    existing_ion_energetics['references'].append("P.W.")
                    existing_ion_energetics['comments'].append(method_basis)

                    # 更新元数据（如果存在的话）
                    if 'metadata' not in existing_entry:
                        existing_entry['metadata'] = {}

                    # 添加新的计算记录到元数据
                    if 'calculation_history' not in existing_entry['metadata']:
                        existing_entry['metadata']['calculation_history'] = []

                    existing_entry['metadata']['calculation_history'].append({
                        'calculation_method': results.get('high_level_method', 'Unknown'),
                        'calculation_basis': results.get('high_level_basis', 'Unknown'),
                        'conformer_method': results.get('low_level_method', 'Unknown'),
                        'conformer_basis': results.get('low_level_basis', 'Unknown'),
                        'conformers_generated': results.get('conformers_generated', 0),
                        'conformers_optimized': results.get('conformers_optimized', 0),
                        'neutral_energy': results.get('neutral_energy', 0.0),
                        'cation_energy': results.get('cation_energy', 0.0),
                        'ie_hartree': results.get('ionization_energy_hartree', 0.0),
                        'ie_kcal_mol': results.get('ionization_energy_kcal_mol', 0.0),
                        'calculated_at': datetime.now().isoformat(),
                        'user_name': compound_name  # 记录用户输入的名称
                    })

                    # 更新最后修改时间
                    existing_entry['metadata']['last_updated'] = datetime.now().isoformat()

                else:
                    return
            else:
                nist_data.append(new_compound)

            # 保存数据库
            try:
                with open(nist_db_path, 'w', encoding='utf-8') as f:
                    json.dump(nist_data, f, indent=2, ensure_ascii=False)

                QMessageBox.information(self, "保存成功",
                    f"PSI4计算结果已成功保存到NIST数据库！\n\n"
                    f"化合物名称: {compound_name}\n"
                    f"分子式: {mol_formula}\n"
                    f"分子量: {mol_weight:.4f}\n"
                    f"电离能: {results['ionization_energy_ev']:.3f} eV\n"
                    f"计算方法: {method_basis}\n"
                    f"保存位置: {nist_db_path}")

            except Exception as e:
                QMessageBox.critical(self, "保存失败", f"写入数据库文件时出错: {str(e)}")

        except Exception as e:
            QMessageBox.critical(self, "保存失败", f"保存到数据库时出错: {str(e)}")
            import traceback
            traceback.print_exc()


class PSI4CalculationThread(QThread):
    """PSI4电离能计算线程"""
    
    # 定义信号
    calculation_started = pyqtSignal()
    calculation_finished = pyqtSignal(dict)
    calculation_error = pyqtSignal(str)
    progress_updated = pyqtSignal(str)
    output_updated = pyqtSignal(str)  # PSI4输出信号
    
    def __init__(self, params):
        super().__init__()
        self.params = params
        self.stop_monitoring = False
        self.last_file_size = 0
    
    def run(self):
        """执行计算"""
        try:
            self.calculation_started.emit()
            
            # 更新进度
            self.progress_updated.emit("开始电离能计算...")
            
            if not PSI4_AVAILABLE:
                raise ImportError("PSI4模块不可用，请确保已正确安装PSI4和RDKit")
            
            import os
            
            # 设置PSI4缓存目录
            if 'scratch_dir' not in self.params or not self.params['scratch_dir']:
                # 默认使用程序目录下的psi4_scratch目录
                default_scratch_dir = os.path.join(os.getcwd(), "psi4_scratch")
                os.makedirs(default_scratch_dir, exist_ok=True)
                self.params['scratch_dir'] = default_scratch_dir
                print(f"使用默认PSI4缓存目录: {default_scratch_dir}")
            else:
                # 确保用户指定的目录存在
                os.makedirs(self.params['scratch_dir'], exist_ok=True)
                print(f"使用指定PSI4缓存目录: {self.params['scratch_dir']}")
            
            # 创建PSI4输出文件（在缓存目录中）
            psi4_output_file = os.path.join(self.params['scratch_dir'], "psi4_calculation.out")
            
            # 启动文件监控线程
            monitor_thread = threading.Thread(
                target=self.monitor_output_file, 
                args=(psi4_output_file,), 
                daemon=True
            )
            monitor_thread.start()
            
            # 执行计算
            results = self.run_calculation_with_output_capture(self.params, psi4_output_file)
            
            # 停止监控
            self.stop_monitoring = True
            
            # 发送结果
            self.calculation_finished.emit(results)
            
        except Exception as e:
            self.stop_monitoring = True
            self.calculation_error.emit(str(e))
    
    def monitor_output_file(self, file_path):
        """监控PSI4输出文件的变化并实时发送更新"""
        try:
            # 初始化文件大小
            self.last_file_size = 0

            # 如果文件已存在，从当前大小开始（避免读取旧内容）
            if os.path.exists(file_path):
                self.last_file_size = os.path.getsize(file_path)

            # 缓冲区用于处理不完整的行
            line_buffer = ""

            while not getattr(self, 'stop_monitoring', False):
                try:
                    if os.path.exists(file_path):
                        current_size = os.path.getsize(file_path)
                        if current_size > self.last_file_size:
                            # 读取新增内容
                            with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                                f.seek(self.last_file_size)
                                new_content = f.read()

                                if new_content:
                                    # 将新内容添加到缓冲区
                                    line_buffer += new_content

                                    # 处理完整的行
                                    lines = line_buffer.split('\n')
                                    # 保留最后一行（可能不完整）
                                    line_buffer = lines[-1]
                                    complete_lines = lines[:-1]

                                    if complete_lines:
                                        complete_content = '\n'.join(complete_lines)
                                        # 过滤PSI4输出信息
                                        filtered_content = self.filter_psi4_output(complete_content)
                                        if filtered_content:
                                            self.output_updated.emit(filtered_content)

                                self.last_file_size = current_size

                        # 如果文件大小没有变化，检查是否有缓冲的内容需要处理
                        elif line_buffer.strip():
                            # 处理缓冲区中的内容（可能是最后一行）
                            filtered_content = self.filter_psi4_output(line_buffer)
                            if filtered_content:
                                self.output_updated.emit(filtered_content)
                            line_buffer = ""

                except Exception as e:
                    # 忽略读取错误，继续监控
                    pass

                time.sleep(0.2)  # 进一步缩短检查间隔，提高实时性

        except Exception as e:
            print(f"监控线程异常: {e}")
        finally:
            # 清理缓冲区
            if hasattr(self, 'line_buffer'):
                del self.line_buffer
    
    def filter_psi4_output(self, content):
        """过滤PSI4输出，保留重要的计算进度和结果信息"""
        lines = content.split('\n')
        filtered_lines = []

        # 重要关键词列表
        important_keywords = [
            'optimization', 'optimizing', 'converged', 'convergence',
            'step', 'energy', 'hartree', 'scf', 'iteration', 'iter',
            'gradient', 'geometry', 'complete', 'completed', 'finished',
            'starting', 'beginning', 'initializing', 'final',
            'warning', 'error', 'failed', 'success', 'successful',
            'conformer', 'conformers', 'generated', 'optimized',
            'neutral', 'cation', 'ionization', 'ie', 'ev',
            'calculation', 'computing', 'processing'
        ]

        # 需要跳过的模式
        skip_patterns = [
            '***', '==>',  # PSI4装饰性输出
            'scratch directory', 'temporary directory',  # 目录信息
            'psi4 exiting successfully',  # 退出信息
            'reading options from',  # 配置读取
            'nuclear repulsion energy',  # 核排斥能（太详细）
            'basis set information',  # 基组信息（太详细）
            'molecular point group',  # 分子点群（太详细）
        ]

        for line in lines:
            line_lower = line.lower().strip()

            # 跳过空行
            if not line.strip():
                continue

            # 跳过特定模式
            should_skip = False
            for pattern in skip_patterns:
                if pattern in line_lower:
                    should_skip = True
                    break

            if should_skip:
                continue

            # 保留包含重要关键词的行
            contains_important = False
            for keyword in important_keywords:
                if keyword in line_lower:
                    contains_important = True
                    break

            # 保留重要信息或简短的状态行
            if contains_important or len(line.strip()) < 80:
                # 清理行格式
                cleaned_line = line.strip()
                if cleaned_line and not cleaned_line.startswith('*'):
                    filtered_lines.append(cleaned_line)

        return '\n'.join(filtered_lines) if filtered_lines else ''
    
    def run_calculation_with_output_capture(self, params, output_file):
        """运行计算并设置输出文件"""
        import psi4
        import sys
        from datetime import datetime

        # 发送初始化信息到输出
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write("=== PSI4绝热电离能计算开始 ===\n")
            f.write(f"时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"SMILES: {params['smiles']}\n")
            f.write(f"方法: {params['high_level_method']}/{params['high_level_basis']}\n")
            f.write(f"线程数: {params['nthreads']}\n")
            f.write(f"内存: {params['memory']}\n")
            f.write(f"缓存目录: {params['scratch_dir']}\n")
            f.write("="*50 + "\n\n")
            f.flush()

        # 设置PSI4输出文件（文件监控线程会实时读取这个文件）
        try:
            # 设置PSI4的主输出文件
            psi4.core.set_output_file(output_file, False)

            # 同时重定向Python的stdout和stderr到文件（捕获更多输出）
            original_stdout = sys.stdout
            original_stderr = sys.stderr

            # 创建一个自定义的输出流，同时写入文件和保持原始输出
            class TeeOutput:
                def __init__(self, file_path, original_stream):
                    self.file_path = file_path
                    self.original_stream = original_stream

                def write(self, text):
                    # 写入原始流
                    if self.original_stream:
                        self.original_stream.write(text)
                    # 写入文件
                    try:
                        with open(self.file_path, 'a', encoding='utf-8') as f:
                            f.write(text)
                            f.flush()
                    except:
                        pass  # 忽略写入错误

                def flush(self):
                    if self.original_stream:
                        self.original_stream.flush()

            # 重定向输出
            tee_stdout = TeeOutput(output_file, original_stdout)
            tee_stderr = TeeOutput(output_file, original_stderr)

            sys.stdout = tee_stdout
            sys.stderr = tee_stderr

            # 添加进度信息
            print("开始PSI4计算...")

            # 调用计算函数
            results = calculate_ionization_energy(**params)

        finally:
            # 恢复原始输出流
            sys.stdout = original_stdout
            sys.stderr = original_stderr

        # 在输出文件末尾添加完成信息
        with open(output_file, 'a', encoding='utf-8') as f:
            f.write("\n" + "="*50 + "\n")
            f.write("=== PSI4计算完成 ===\n")
            if results['success']:
                f.write(f"绝热电离能: {results['ionization_energy_ev']:.3f} eV\n")
                f.write(f"中性分子能量: {results['neutral_energy']:.8f} Hartree\n")
                f.write(f"阳离子能量: {results['cation_energy']:.8f} Hartree\n")
            else:
                f.write(f"计算失败: {results.get('error_message', '未知错误')}\n")
            f.write(f"完成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.flush()

        return results

if __name__ == "__main__":
    app = QApplication(sys.argv)
    window = ElementalCompositionCalculator()
    window.show()
    sys.exit(app.exec())